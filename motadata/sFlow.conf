plugins: print[sFlow]
sfacctd_port:6343
logfile: /home/<USER>/Workspace/java-workspace/Motadata-NR/motadata/logs/flow/sFlow_1746797607870.log
loglevel: 7
snaplen: 65535
use_ip_next_hop: true
classifier_num_roots: 10240
aggregate[sFlow]: src_host, dst_host, peer_src_ip, peer_dst_ip, in_iface, out_iface, src_port, dst_port, proto, sampling_rate, flows, tcpflags, tag, export_proto_version, tos, class
print_output_file[sFlow]:/home/<USER>/Workspace/java-workspace/Motadata-NR/motadata/flow-cache/%Y%m%d%H%M
print_output[sFlow]: json
print_history[sFlow]: 3m
print_history_roundoff[sFlow]: m
print_refresh_time[sFlow]: 180
print_output_file_append: true
timestamps_secs: true
plugin_pipe_zmq: true
print_num_protos: true
pre_tag_map: /home/<USER>/Workspace/java-workspace/Motadata-NR/motadata/tag.rule
print_cache_entries[sFlow]: 16411
sfacctd_time_new: true
daemonize: true
pidfile: /home/<USER>/Workspace/java-workspace/Motadata-NR/motadata/sFlow
