/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 */
package com.mindarray.eventbus;

import com.mindarray.Bootstrap;
import com.mindarray.BootstrapAgent;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.store.EventCacheStore;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;
import org.zeromq.SocketType;
import org.zeromq.ZMQ;

import java.util.concurrent.atomic.AtomicBoolean;

import static com.mindarray.GlobalConstants.DOT_SEPARATOR;
import static com.mindarray.GlobalConstants.EMPTY_VALUE;
import static com.mindarray.eventbus.EventBusConstants.*;

/**
 * The RemoteEventSubscriber class subscribes to events from remote publishers using ZeroMQ's SUB socket.
 * <p>
 * This class is responsible for:
 * <ul>
 *   <li>Connecting to remote publishers and subscribing to specific topics</li>
 *   <li>Filtering messages by topic based on bootstrap type (APP, AGENT, COLLECTOR, MANAGER)</li>
 *   <li>Receiving and processing messages in a dedicated thread</li>
 *   <li>Forwarding received messages to local handlers via the Vert.x event bus</li>
 *   <li>Managing high-water marks to control message buffering</li>
 *   <li>Handling connection lifecycle and error recovery</li>
 * </ul>
 * <p>
 * The RemoteEventSubscriber connects to a specified host and port, and subscribes to topics
 * based on the bootstrap type. It receives messages in a dedicated thread to avoid blocking
 * the main event loop, then routes them to appropriate handlers within the system.
 * <p>
 * The class uses ZeroMQ's SUB socket type, which implements the subscriber side of the publish-subscribe
 * pattern. This pattern allows for one-to-many distribution of messages, where subscribers only
 * receive messages they're interested in based on topic filtering.
 * <p>
 * Example usage:
 * <pre>
 * // Create and deploy a RemoteEventSubscriber
 * RemoteEventSubscriber subscriber = new RemoteEventSubscriber(5555, "publisher-host");
 * vertx.deployVerticle(subscriber);
 *
 * // Set up a handler for received events
 * vertx.eventBus().consumer("EVENT_APP", message -> {
 *     JsonObject event = (JsonObject) message.body();
 *     System.out.println("Received event: " + event);
 *     // Process the event
 * });
 * </pre>
 */
public class RemoteEventSubscriber extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(RemoteEventSubscriber.class, GlobalConstants.MOTADATA_EVENT_BUS, "Remote Event Subscriber");
    private final ZMQ.Socket subscriber;
    private final int subscriberPort;
    private final String subscriberHost;
    private final AtomicBoolean hasMoreEvent = new AtomicBoolean(true);
    private final String remoteEventTopic = getRemoteEventTopic(Bootstrap.bootstrapType());

    public RemoteEventSubscriber(int port)
    {
        subscriber = Bootstrap.zcontext().socket(SocketType.SUB);

        subscriberPort = port;

        subscriberHost = CommonUtil.getRemoteEventSubscriber();
    }

    public RemoteEventSubscriber(int port, String host)
    {
        subscriber = Bootstrap.zcontext().socket(SocketType.SUB);

        subscriberPort = port;

        subscriberHost = host;
    }

    @Override
    public void start(Promise<Void> promise) throws Exception
    {

        if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.APP)
        {
            // for slave replica

            subscriber.setHWM(MotadataConfigUtil.getEventBacklogSize());

            subscriber.setRcvHWM(MotadataConfigUtil.getEventBacklogSize());

            subscriber.subscribe(remoteEventTopic);

            subscriber.connect("tcp://" + subscriberHost + ":" + subscriberPort);

            LOGGER.info(String.format("event subscriber connected to %s with topic %s ", subscriberHost + ":" + subscriberPort, remoteEventTopic));

            var cipherUtil = new CipherUtil();

            LOGGER.info(String.format("consumer register with event type : %s ", EVENT_APP + DOT_SEPARATOR + subscriberPort));

            // consumer : "app.port" , reason : if same name consumer exist then , if we send multiple event then the final receiver will receive event in unordered manner. that's why we separate with port
            // ex : delete and save_all for same table in compliance has to be sequential else it will create issue.
            vertx.eventBus().<byte[]>localConsumer(EVENT_APP + DOT_SEPARATOR + subscriberPort, message ->
            {
                if (message.body() != null && message.body().length > 0)
                {
                    var buffer = Buffer.buffer(message.body());

                    var event = CodecUtil.toJSONObject(cipherUtil.decrypt(buffer.getBytes(2 + buffer.getShortLE(0), buffer.length())));

                    vertx.eventBus().send(REMOTE_SESSION_UPDATE, System.currentTimeMillis());

                    switch (event.getString(EVENT_TYPE))
                    {
                        case EventBusConstants.EVENT_METRIC_POLL, EventBusConstants.EVENT_REDISCOVER,
                             EventBusConstants.EVENT_DISCOVERY, EventBusConstants.EVENT_PLUGIN_ENGINE,
                             EventBusConstants.EVENT_TOPOLOGY ->
                        {
                            vertx.eventBus().send(EventBusConstants.EVENT_REMOTE, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_ACKNOWLEDGEMENT)
                                    .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                                    .put(EventBusConstants.EVENT_ID, event.getLong(EventBusConstants.EVENT_ID)));

                            EventBusConstants.updateEvent(event.getLong(EventBusConstants.EVENT_ID),
                                    String.format(InfoMessageConstants.EVENT_TRACKER_EVENT_RECEIVED, Bootstrap.getRegistrationId(), DateTimeUtil.timestamp()));

                            EventCacheStore.getStore().addItem(event.getLong(EventBusConstants.EVENT_ID));

                            vertx.eventBus().send(event.getString(EventBusConstants.EVENT_TYPE), event);
                        }

                        case EventBusConstants.EVENT_TIME_OUT ->
                                EventCacheStore.getStore().deleteItem(event.getLong(EventBusConstants.EVENT_ID));

                        case EventBusConstants.EVENT_CHANGE_NOTIFICATION, EventBusConstants.EVENT_ENGINE_STATS ->
                                vertx.eventBus().publish(event.getString(EventBusConstants.EVENT_TYPE), event);

                        case EVENT_HA_DATASTORE_SECONDARY_SYNC ->
                                vertx.eventBus().publish(EVENT_HA_DATASTORE_SECONDARY_SYNC, event);

                        case EVENT_HA_CONFIG_OBSERVER_SYNC ->
                                vertx.eventBus().publish(EVENT_HA_CONFIG_OBSERVER_SYNC, event);

                        case EVENT_HA_CACHE_OBSERVER_SYNC ->
                                vertx.eventBus().publish(EVENT_HA_CACHE_OBSERVER_SYNC, event);

                        case EVENT_HA_ACKNOWLEDGEMENT -> vertx.eventBus().publish(EVENT_HA_ACKNOWLEDGEMENT, event);

                        case EVENT_OBSERVER_HEARTBEAT -> vertx.eventBus().send(EVENT_OBSERVER_HEARTBEAT, event);

                        default ->
                        {
                            // ignore
                        }
                    }
                }
            });

            new Thread(() ->
            {
                byte[] bufferBytes;

                byte[] topicBytes;

                while (hasMoreEvent.get())
                {
                    try
                    {
                        topicBytes = subscriber.recv();

                        bufferBytes = subscriber.recv();

                        if (bufferBytes.length > 0)
                        {
                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("receiving on %s port ", subscriberPort));
                            }

                            vertx.eventBus().send(EVENT_APP + DOT_SEPARATOR + subscriberPort, Buffer.buffer().appendShortLE(CommonUtil.getShort(topicBytes.length)).appendBytes(topicBytes).appendBytes(bufferBytes).getBytes());
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }

                LOGGER.warn("Remote Event Subscriber disconnected.....");

                disconnect();

            }, "Remote Event Subscriber APP").start();

            promise.complete();
        }
        else if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.FLOW_COLLECTOR ||
                Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.EVENT_COLLECTOR ||
                Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.EVENT_PROCESSOR ||
                Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.COLLECTOR)
        {
            var cipherUtil = new CipherUtil();

            vertx.eventBus().<byte[]>localConsumer(EventBusConstants.EVENT_PUBLICATION, message ->
            {
                try
                {
                    if (message.body() != null && message.body().length > 0)
                    {
                        vertx.eventBus().send(REMOTE_SESSION_UPDATE, System.currentTimeMillis());

                        var buffer = Buffer.buffer(message.body());

                        var topic = buffer.getString(2, buffer.getShortLE(0) + 2);

                        vertx.eventBus().send(REMOTE_SESSION_UPDATE, System.currentTimeMillis());

                        if (topic.startsWith(EventBusConstants.SHUTDOWN_TOPIC))
                        {
                            Bootstrap.stop(true, null, true);
                        }
                        else if (topic.contains(EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)) // ignore motadata.manager topic message
                        {
                            var event = CodecUtil.toJSONObject(cipherUtil.decrypt(buffer.getBytes(2 + buffer.getShortLE(0), buffer.length())));

                            switch (event.getString(EventBusConstants.EVENT_TYPE))
                            {
                                case EventBusConstants.EVENT_METRIC_POLL, EventBusConstants.EVENT_REDISCOVER,
                                     EventBusConstants.EVENT_DISCOVERY, EventBusConstants.EVENT_PLUGIN_ENGINE,
                                     EventBusConstants.EVENT_TOPOLOGY, EVENT_CONFIG_UPGRADE_MANAGER ->
                                {
                                    vertx.eventBus().send(EventBusConstants.EVENT_REMOTE, new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_ACKNOWLEDGEMENT)
                                            .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                                            .put(EventBusConstants.EVENT_ID, event.getLong(EventBusConstants.EVENT_ID)));

                                    EventBusConstants.updateEvent(event.getLong(EventBusConstants.EVENT_ID),
                                            String.format(InfoMessageConstants.EVENT_TRACKER_EVENT_RECEIVED, Bootstrap.getRegistrationId(), DateTimeUtil.timestamp()));

                                    EventCacheStore.getStore().addItem(event.getLong(EventBusConstants.EVENT_ID));

                                    vertx.eventBus().send(event.getString(EventBusConstants.EVENT_TYPE), event);
                                }
                                case EVENT_DIAGNOSTIC ->
                                        vertx.eventBus().send(event.getString(EventBusConstants.EVENT_TYPE), event);

                                case EventBusConstants.EVENT_TIME_OUT ->
                                        EventCacheStore.getStore().deleteItem(event.getLong(EventBusConstants.EVENT_ID));

                                case EventBusConstants.EVENT_CHANGE_NOTIFICATION,
                                     EventBusConstants.EVENT_ENGINE_STATS ->
                                        vertx.eventBus().publish(event.getString(EventBusConstants.EVENT_TYPE), event);

                                default ->
                                {
                                    // ignore
                                }
                            }
                        }
                    }
                }

                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }).exceptionHandler(LOGGER::error);

            subscriber.setHWM(MotadataConfigUtil.getEventBacklogSize());

            subscriber.setRcvHWM(MotadataConfigUtil.getEventBacklogSize());

            subscriber.subscribe(remoteEventTopic);

            subscriber.connect("tcp://" + CommonUtil.getRemoteEventSubscriber() + ":" + subscriberPort);

            LOGGER.info(String.format("event subscriber connected to %s", CommonUtil.getRemoteEventPublisher() + ":" + subscriberPort));

            new Thread(() ->
            {
                byte[] bufferBytes;

                byte[] topicBytes;

                while (hasMoreEvent.get())
                {
                    try
                    {
                        topicBytes = subscriber.recv();

                        bufferBytes = subscriber.recv();

                        if (bufferBytes.length > 0)
                        {
                            vertx.eventBus().send(EventBusConstants.EVENT_PUBLICATION, Buffer.buffer().appendShortLE(CommonUtil.getShort(topicBytes.length)).appendBytes(topicBytes).appendBytes(bufferBytes).getBytes());
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }

                LOGGER.warn("Remote Event Subscriber disconnected....");

            }, "Remote Event Subscriber").start();

            promise.complete();

        }

        else if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.AGENT)
        {
            subscriber.setHWM(AgentConfigUtil.getAgentMaxEventBacklogQueueSize());

            subscriber.setRcvHWM(AgentConfigUtil.getAgentMaxEventBacklogQueueSize());

            subscriber.subscribe(remoteEventTopic);

            subscriber.connect("tcp://" + CommonUtil.getRemoteEventSubscriber() + ":" + subscriberPort);

            LOGGER.info(String.format("event subscriber connected to %s", CommonUtil.getRemoteEventSubscriber() + ":" + subscriberPort));

            new Thread(() ->
            {
                byte[] bufferBytes;

                byte[] topicBytes;

                while (hasMoreEvent.get())
                {
                    try
                    {
                        topicBytes = subscriber.recv();

                        bufferBytes = subscriber.recv();

                        if (bufferBytes.length > 0)
                        {
                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace(String.format("receiving on %s port ", subscriberPort));
                            }

                            vertx.eventBus().send(EventBusConstants.EVENT_REMOTE, Buffer.buffer().appendShortLE(CommonUtil.getShort(topicBytes.length)).appendBytes(topicBytes).appendBytes(bufferBytes).getBytes());
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }

                LOGGER.warn("Agent Event Subscriber disconnected.....");

            }, "Agent Event Subscriber").start();

            promise.complete();
        }

        else if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.MANAGER)
        {
            subscriber.setHWM(500);

            subscriber.setRcvHWM(500);

            subscriber.subscribe(remoteEventTopic);

            subscriber.connect("tcp://" + CommonUtil.getRemoteEventSubscriber() + ":" + subscriberPort);

            LOGGER.info(String.format("event subscriber connected to %s with topic %s", CommonUtil.getRemoteEventSubscriber() + ":" + subscriberPort, remoteEventTopic));

            vertx.eventBus().<byte[]>localConsumer(EVENT_PUBLICATION, message ->
            {
                try
                {
                    if (message.body() != null && message.body().length > 0)
                    {
                        vertx.eventBus().send(REMOTE_SESSION_UPDATE, System.currentTimeMillis());

                        var buffer = Buffer.buffer(message.body());

                        vertx.eventBus().send(EVENT_REMOTE, new JsonObject(buffer.getBuffer(2 + buffer.getShortLE(0), buffer.length())));

                        vertx.eventBus().send(REMOTE_SESSION_UPDATE, System.currentTimeMillis());
                    }
                }

                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }).exceptionHandler(LOGGER::error);

            new Thread(() ->
            {
                byte[] bufferBytes;

                byte[] topicBytes;

                while (hasMoreEvent.get())
                {
                    try
                    {
                        topicBytes = subscriber.recv();

                        bufferBytes = subscriber.recv();

                        if (bufferBytes.length > 0)
                        {
                            vertx.eventBus().send(EVENT_PUBLICATION, Buffer.buffer().appendShortLE(CommonUtil.getShort(topicBytes.length)).appendBytes(topicBytes).appendBytes(bufferBytes).getBytes());
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }

                LOGGER.warn("Remote Event Subscriber disconnected....");

            }, "Motadata Manager").start();

            promise.complete();
        }
        else
        {
            promise.fail(String.format("failed to start %s, reason: invalid boot sequence...", this.getClass().getSimpleName()));
        }
    }

    private void disconnect()
    {
        LOGGER.info(String.format("stopping motadata %s ....", Bootstrap.bootstrapType().toString().toLowerCase()));
    }

    private String getRemoteEventTopic(GlobalConstants.BootstrapType bootstrapType)
    {
        var topic = EMPTY_VALUE;

        if (bootstrapType == GlobalConstants.BootstrapType.MANAGER)
        {
            topic = Bootstrap.getRegistrationId() + "." + MOTADATA_MANAGER_TOPIC;
        }
        else if (bootstrapType == GlobalConstants.BootstrapType.AGENT)
        {
            topic = BootstrapAgent.getAgentUUID() + "." + EventBusConstants.EVENT_AGENT;
        }
        else if (bootstrapType == GlobalConstants.BootstrapType.COLLECTOR)
        {
            topic = Bootstrap.getRegistrationId() + "." + REMOTE_EVENT_PROCESSOR_TOPIC;
        }
        else if (bootstrapType == GlobalConstants.BootstrapType.APP)
        {
            topic = Bootstrap.getRegistrationId() + "." + REMOTE_EVENT_PROCESSOR_TOPIC;
        }

        return topic;
    }

    @Override
    public void stop(Promise<Void> promise) throws Exception
    {
        hasMoreEvent.set(false);

        subscriber.close();

        promise.complete();
    }
}
