/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 * Change Logs:
 *  Date			Author			         Notes
 *  28-Feb-2025     Smit Prajapati           MOTADATA-4956: Added getTagRuleApplyTimerSeconds for Rule Based Tagging
 *  17-Mar-2025     Chandresh                MOTADATA-5379: Added configurable json key max length parameter
 *  24-Mar-2025     Yash Tiwari              MOTADATA-5432::Added configurable json key for ping check packet retries & for http API server specific websocket maximum message size, websocket backlog queue size are introduced
 *  25-Mar-2025     Smit Prajapati           MOTADATA-5435: Added configuration for Flow back-pressure mechanism.
 *  24-Mar-2025     Sankalp                  Added method to get plugin engine
 *  27-Feb-2025     Pruthviraj               MOTADATA-5284 : NetRoute polling related parameter added
 *  27-Mar-2025     Umang Sharma             Added Support for status flap instance
 *  23-May-2025     Priyansh                 MOTADATA-6342: Made Inactive Session timer configurable
 */

package com.mindarray.util;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.config.ConfigConstants;
import com.mindarray.ha.HAConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.plugin.PluginEngineConstants;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;

/**
 * Utility class for accessing Motadata application configuration settings.
 * <p>
 * This class provides centralized access to all configuration parameters used throughout
 * the Motadata application. It loads configuration from a JSON file (motadata.json) and
 * provides getter methods for accessing specific configuration values with appropriate
 * default values when settings are not explicitly configured.
 * <p>
 * Configuration categories include:
 * - System settings (bootstrap type, environment type, etc.)
 * - Network settings (ports, hosts, timeouts, etc.)
 * - Worker pool sizes for various components
 * - Cache settings and timeouts
 * - Integration settings
 * - Database settings
 * - High availability (HA) settings
 * - Security settings
 * <p>
 * The class uses a static initializer to load configuration when first accessed,
 * ensuring configuration is available to all components that need it.
 */
public class MotadataConfigUtil
{
    /**
     * Logger instance for this class
     */
    private static final Logger LOGGER = new Logger(MotadataConfigUtil.class, GlobalConstants.MOTADATA_UTIL, "Motadata Config Util");

    /**
     * Central storage for all configuration values loaded from motadata.json
     */
    private static final JsonObject configs = new JsonObject();

    /**
     * Storage for version information used for compatibility checks
     */
    private static final JsonObject versions = new JsonObject();

    /**
     * Flag indicating whether the application is running in development mode
     */
    private static boolean devMode = false;

    /**
     * Static initializer that loads configuration from motadata.json when the class is first loaded.
     * This ensures configuration is available to all components that need it.
     */
    static
    {
        try
        {
            // Load configuration from motadata.json file in the config directory
            loadConfigs(new JsonObject(Files.readString(new File(GlobalConstants.CURRENT_DIR +
                    GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR +
                    GlobalConstants.PATH_SEPARATOR + "motadata.json").toPath(), StandardCharsets.UTF_8)));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Checks if the application is running in development mode.
     * <p>
     * Development mode is determined by the environment type setting in the configuration.
     * If the environment type is "dev" or "test", development mode is enabled.
     *
     * @return true if the application is running in development mode, false otherwise
     */
    public static boolean devMode()
    {
        return devMode;
    }

    /**
     * Checks if Cross-Origin Resource Sharing (CORS) is enabled.
     * <p>
     * This setting controls whether the application allows cross-origin requests.
     * If not explicitly configured, CORS is enabled by default for broader compatibility.
     *
     * @return true if CORS is enabled, false otherwise
     */
    public static boolean corsEnabled()
    {
        if (configs.containsKey("cors.enabled"))
        {
            return configs.getString("cors.enabled").equalsIgnoreCase(YES);
        }

        // Default to enabled if not explicitly configured
        return true;
    }

    /**
     * Loads configuration settings from a JsonObject and applies them to the application.
     * <p>
     * This method:
     * 1. Merges the new configuration with any existing configuration
     * 2. Sets the system log level based on the configuration
     * 3. Determines if the application is in development mode based on the environment type
     * <p>
     * This method is called during static initialization and can be called again
     * to update configuration at runtime.
     *
     * @param newConfigs A JsonObject containing configuration settings to load
     */
    public static void loadConfigs(JsonObject newConfigs)
    {
        try
        {
            // Merge new configuration with existing configuration
            configs.mergeIn(newConfigs);

            // Set system log level from configuration or use INFO level as default
            CommonUtil.setLogLevel(configs.containsKey(SYSTEM_LOG_LEVEL) ?
                    configs.getInteger(SYSTEM_LOG_LEVEL) : LOG_LEVEL_INFO);

            // Set development mode based on environment type
            devMode = getEnvironmentType().equalsIgnoreCase(ENV_DEV) ||
                    getEnvironmentType().equalsIgnoreCase(ENV_TEST);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Gets the system bootstrap type from configuration.
     * <p>
     * The bootstrap type determines how the application starts and which components are initialized.
     * Common values include "APP", "AGENT", "COLLECTOR", etc.
     *
     * @return The configured system bootstrap type
     */
    public static String getSystemBootstrapType()
    {
        return configs.getString(SYSTEM_BOOTSTRAP_TYPE);
    }

    /**
     * Gets the remote event publisher host address.
     * <p>
     * This is the host address where events are published to be consumed by subscribers.
     * If not configured, defaults to localhost (127.0.0.1).
     *
     * @return The configured remote event publisher host, or "127.0.0.1" if not configured
     */
    public static String getRemoteEventPublisher()
    {
        return configs.getString(EVENT_PUBLISHER_HOST, "127.0.0.1");
    }

    /**
     * Gets the remote event secondary subscriber host address.
     * <p>
     * This is used in high availability setups to specify a secondary host that subscribes to events.
     *
     * @return The configured remote event secondary subscriber host, or null if not configured
     */
    public static String getRemoteEventSecondarySubscriber()
    {
        return configs.getString(EVENT_SUBSCRIBER_SECONDARY_HOST, null);
    }

    /**
     * Gets the remote event observer subscriber host address.
     * <p>
     * This is used in distributed setups to specify an observer host that subscribes to events.
     *
     * @return The configured remote event observer subscriber host, or null if not configured
     */
    public static String getRemoteEventObserverSubscriber()
    {
        return configs.getString(EVENT_SUBSCRIBER_OBSERVER_HOST, null);
    }

    /**
     * Gets the remote event observer publisher host address.
     * <p>
     * This is used in distributed setups to specify an observer host that publishes events.
     *
     * @return The configured remote event observer publisher host, or null if not configured
     */
    public static String getRemoteEventObserverPublisher()
    {
        return configs.getString(EVENT_PUBLISHER_OBSERVER_HOST, null);
    }

    public static int getFlowLogLevel()
    {
        // follows Syslog:
        // LOG_EMERG    -> 0
        // LOG_ALERT    -> 1
        // LOG_CRIT     -> 2
        // LOG_ERR      -> 3
        // LOG_WARNING  -> 4
        // LOG_NOTICE   -> 5
        // LOG_INFO     -> 6
        // LOG_DEBUG    -> 7
        if (configs.containsKey("flow.log.level"))
        {
            return configs.getInteger("flow.log.level");
        }

        return 0;   // default is LOG_EMERG
    }

    public static int getDeploymentType()
    {
        // 0 -> small, 1-> medium, 2-> large
        if (configs.containsKey("deployment.type"))
        {
            return configs.getInteger("deployment.type");
        }

        return 0;
    }

    public static int getMetricPollerBatchSize()
    {
        if (configs.containsKey("metric.poller.batch.size"))
        {
            return Math.max(5, Math.min(configs.getInteger("metric.poller.batch.size"), 600));
        }
        return 100;
    }

    public static int getDiscoveryBatchSize()
    {
        if (configs.containsKey("discovery.batch.size"))
        {
            return Math.max(5, Math.min(configs.getInteger("discovery.batch.size"), 500));
        }
        return 100;
    }

    public static int getRediscoveryBatchSize()
    {
        if (configs.containsKey("rediscovery.batch.size"))
        {
            return Math.max(5, Math.min(configs.getInteger("rediscovery.batch.size"), 500));
        }
        return 100;
    }

    public static int getPluginEngineProbeBatchSize()
    {
        if (configs.containsKey("plugin.engine.probe.batch.size"))
        {
            return Math.max(5, Math.min(configs.getInteger("plugin.engine.probe.batch.size"), 500));
        }
        return 100;
    }

    public static int getPluginEnginePingBatchSize()
    {
        if (configs.containsKey("plugin.engine.ping.batch.size"))
        {
            return Math.max(2, Math.min(configs.getInteger("plugin.engine.ping.batch.size"), 100));
        }
        return 10;
    }

    public static int getPluginEnginePingPackets()
    {
        if (configs.containsKey("plugin.engine.ping.packets"))
        {
            return Math.max(1, Math.min(configs.getInteger("plugin.engine.ping.packets"), 20));
        }
        return 5;
    }

    public static int getMetricPollerBatchTimeoutSeconds(NMSConstants.MetricTimeoutValueType metricTimeoutValueType)
    {
        return switch (metricTimeoutValueType)
        {
            case SMALL ->
                    configs.containsKey("metric.poller.small.batch.timeout.seconds") ? Math.max(30, Math.min(configs.getInteger("metric.poller.small.batch.timeout.seconds"), 60)) : 60;

            case MEDIUM ->
                    configs.containsKey("metric.poller.medium.batch.timeout.seconds") ? Math.max(60, Math.min(configs.getInteger("metric.poller.medium.batch.timeout.seconds"), 150)) : 150;

            case LARGE ->
                    configs.containsKey("metric.poller.large.batch.timeout.seconds") ? Math.max(150, Math.min(configs.getInteger("metric.poller.large.batch.timeout.seconds"), 300)) : 300;

            case EXTRA_LARGE ->
                    configs.containsKey("metric.poller.extra.large.batch.timeout.seconds") ? Math.max(300, Math.min(configs.getInteger("metric.poller.extra.large.batch.timeout.seconds"), 6000)) : 420;
        };
    }

    public static int getDiscoveryBatchTimeoutSeconds()
    {
        if (configs.containsKey("discovery.batch.timeout.seconds"))
        {
            return Math.max(60, Math.min(configs.getInteger("discovery.batch.timeout.seconds"), 300));
        }
        return 60;
    }

    public static int getRediscoveryBatchTimeoutSeconds()
    {
        if (configs.containsKey("rediscovery.batch.timeout.seconds"))
        {
            return Math.max(60, Math.min(configs.getInteger("rediscovery.batch.timeout.seconds"), 600));
        }
        return 60;
    }

    public static int getPluginEngineProbeBatchTimeoutSeconds()
    {
        if (configs.containsKey("plugin.engine.probe.batch.timeout.seconds"))
        {
            return Math.max(60, Math.min(configs.getInteger("plugin.engine.probe.batch.timeout.seconds"), 600));
        }
        return 60;
    }

    public static int getIOWorkers()
    {
        if (configs.containsKey("io.workers"))
        {
            return Math.max(4, Math.min(configs.getInteger("io.workers"), 16));
        }

        return 4;
    }

    public static int getEventLoopWorkers()
    {
        if (configs.containsKey("eventloop.workers"))
        {
            return Math.max(4, Math.min(configs.getInteger("eventloop.workers"), 32));
        }
        else
        {
            return switch (getDeploymentType())
            {
                //S
                case 0 -> 8;

                //M
                case 1 -> 12;

                //L
                case 2 -> 16;

                //XL
                case 3 -> 24;

                default -> 8;
            };
        }
    }

    public static int getMaxWebSocketMessageSizeBytes()
    {
        if (configs.containsKey("websocket.max.message.size.bytes"))
        {
            return Math.max(5, Math.min(configs.getInteger("websocket.max.message.size.bytes"), 20));
        }

        return 10;
    }

    public static int getWebSocketBacklogQueueSize()
    {
        if (configs.containsKey("websocket.backlog.queue.size"))
        {
            return Math.max(1024, Math.min(configs.getInteger("websocket.backlog.queue.size"), 8192));
        }

        return 1024;
    }

    /**
     * Checks if HTTPS is enabled for the HTTP server.
     * <p>
     * This setting determines whether the HTTP server uses secure HTTPS connections.
     * If not explicitly configured, HTTPS is enabled by default for security.
     *
     * @return true if HTTPS is enabled, false otherwise
     */
    public static boolean httpsEnabled()
    {
        //TODO comment this condition on final build
        if (configs.containsKey("https"))
        {
            return configs.getString("https").equalsIgnoreCase(YES);
        }

        // Default to enabled if not explicitly configured
        return true;
    }

    /**
     * Gets the HTTP server port for the specified bootstrap type.
     * <p>
     * Different bootstrap types may use different default ports:
     * - APP: Uses the configured HTTP server port (default: 443)
     * - MANAGER: Uses a temporary port for restore/master upgrade operations (default: 8080)
     *
     * @param bootStrapType The bootstrap type to get the port for (e.g., "APP", "MANAGER")
     * @return The configured HTTP server port for the specified bootstrap type
     */
    public static int getHTTPServerPort(String bootStrapType)
    {
        var port = 443;

        if (bootStrapType.equalsIgnoreCase(GlobalConstants.BootstrapType.APP.name()))
        {
            // For APP bootstrap type, use the configured HTTP server port or default to 443 (HTTPS)
            port = configs.getInteger(HTTP_SERVER_PORT, 443);
        }
        else if (bootStrapType.equalsIgnoreCase(GlobalConstants.BootstrapType.MANAGER.name()))
        {
            // For MANAGER bootstrap type, use a temporary port for restore/master upgrade operations
            port = configs.getInteger("manager.http.server.port", 8080);
        }

        return port;
    }

    /**
     * Gets the HTTP server host address.
     * <p>
     * This is the host address that the HTTP server binds to.
     * If not configured, defaults to "localhost".
     *
     * @return The configured HTTP server host, or "localhost" if not configured
     */
    public static String getHTTPServerHost()
    {
        return configs.getString(HTTP_SERVER_HOST, "localhost");
    }

    public static int getHTTPServerInstances()
    {
        if (configs.containsKey("http.server.instances"))
        {
            return Math.max(1, Math.min(configs.getInteger("http.server.instances"), 16));
        }
        else
        {
            return switch (getDeploymentType())
            {
                //S
                case 0 -> 2;

                //M
                case 1 -> 4;

                //L
                case 2 -> 8;

                //XL
                case 3 -> 12;

                default -> 2;
            };
        }
    }

    public static boolean enableHTTPLogging()
    {

        return configs.getString("http.server.logging") != null && configs.getString("http.server.logging").equalsIgnoreCase(YES);


    }

    public static int getNetworkServiceWorkers()
    {
        if (configs.containsKey("network.service.workers"))
        {
            return Math.max(2, Math.min(20, configs.getInteger("network.service.workers")));
        }

        return 2;
    }

    public static int getRediscoveryWorkers()
    {
        if (configs.containsKey("rediscovery.workers"))
        {
            return Math.max(1, Math.min(3, configs.getInteger("rediscovery.workers")));
        }

        return 1;
    }

    public static int getTopologyWorkers()
    {
        if (configs.containsKey("topology.workers"))
        {
            return Math.max(2, Math.min(10, configs.getInteger("topology.workers")));
        }

        return 2;
    }

    public static int getMetricPollerWorkers()//it should always be in multiple of 13
    {
        if (configs.containsKey("metric.poller.workers"))
        {
            return Math.max(13, Math.min(52, configs.getInteger("metric.poller.workers")));
        }

        return 13;
    }

    public static int getPluginEngineWorkers()//it should always be in multiple of 15
    {
        if (configs.containsKey("plugin.engine.workers"))
        {
            return Math.max(15, Math.min(60, configs.getInteger("plugin.engine.workers")));
        }

        return 15;
    }

    public static int getHelperWorkers()
    {
        if (configs.containsKey("helper.workers"))
        {
            return Math.max(8, Math.min(configs.getInteger("helper.workers"), 100));
        }
        else
        {

            return switch (getDeploymentType())
            {
                //S
                case 0 -> 8;

                //M
                case 1 -> 16;

                //L
                case 2 -> 32;

                //XL
                case 3 -> 64;

                default -> 8;
            };
        }
    }

    public static int getInternalHelperWorkers()
    {
        if (configs.containsKey("internal.helper.workers"))
        {
            return Math.max(5, Math.min(configs.getInteger("internal.helper.workers"), 20));
        }

        return 10;
    }

    public static int getPollerHealthInspectionTimerSeconds() //it should always be in multiple of 5
    {
        if (configs.containsKey("poller.health.inspection.timer.seconds"))
        {
            return configs.getInteger("poller.health.inspection.timer.seconds");
        }
        return 1200 * 1000;
    }

    public static int getMaxPollerWorkers() //it should always be in multiple of 5
    {
        if (configs.containsKey("metric.poller.max.workers"))
        {
            return Math.max(5, Math.min(configs.getInteger("metric.poller.max.workers"), 20));
        }
        else
        {
            return switch (getDeploymentType())
            {
                //S
                case 0 -> 6;

                //M
                case 1 -> 5;

                //L
                case 2 -> 4;

                //XL
                case 3 -> 4;

                default -> 6;
            };
        }
    }

    public static int getEventPublisherPort()
    {
        // by default, master publisher will bind on 9444,9445 port.
        // by default, master subscriber will bind on 9449,9450 port.


        // for collector,slave,replica remote subscriber will connect on 9444,9445
        // for collector,slave,replica remote forwarder will connect on 9449,9450

        if (Bootstrap.bootstrapType() == BootstrapType.FLOW_COLLECTOR ||
                Bootstrap.bootstrapType() == BootstrapType.EVENT_PROCESSOR ||
                Bootstrap.bootstrapType() == BootstrapType.COLLECTOR ||
                Bootstrap.bootstrapType() == BootstrapType.EVENT_COLLECTOR)
        {
            return configs.getInteger(EVENT_PUBLISHER_PORT, 9449);
        }
        else if (Bootstrap.bootstrapType() == BootstrapType.APP)
        {
            if (getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) || getInstallationMode().equalsIgnoreCase(InstallationMode.FAILOVER.name()))
            {
                return configs.getInteger(EVENT_PUBLISHER_PORT, 9449);
            }
            else
            {
                return configs.getInteger(EVENT_PUBLISHER_PORT, 9444);                      // for standalone,primary
            }
        }

        return 9444;
    }

    public static String getRemoteEventSubscriber()
    {
        return configs.getString(EVENT_SUBSCRIBER_HOST, "127.0.0.1");
    }

    public static int getEventSubscriberPort()
    {
        // by default, master publisher will bind on 9444,9445 port.
        // by default, master subscriber will bind on 9449,9450 port.


        // for collector,slave,replica remote subscriber will connect on 9444,9445
        // for collector,slave,replica remote forwarder will connect on 9449,9450

        if (Bootstrap.bootstrapType() == BootstrapType.FLOW_COLLECTOR ||
                Bootstrap.bootstrapType() == BootstrapType.EVENT_PROCESSOR ||
                Bootstrap.bootstrapType() == BootstrapType.COLLECTOR ||
                Bootstrap.bootstrapType() == BootstrapType.EVENT_COLLECTOR)
        {
            return configs.getInteger(EVENT_SUBSCRIBER_PORT, 9444);
        }
        else if (Bootstrap.bootstrapType() == BootstrapType.APP)
        {
            if (getInstallationMode().equalsIgnoreCase(InstallationMode.SECONDARY.name()) || getInstallationMode().equalsIgnoreCase(InstallationMode.FAILOVER.name()))
            {
                return configs.getInteger(EVENT_SUBSCRIBER_PORT, 9444);
            }
            else
            {
                return configs.getInteger(EVENT_SUBSCRIBER_PORT, 9449);                     // for standalone,primary
            }
        }

        return 9449;
    }


    public static int getEventSubscribers()
    {
        if (configs.containsKey("event.subscribers"))
        {
            return Math.max(2, Math.min(configs.getInteger("event.subscribers"), 5));
        }

        return 2;
    }

    public static int getMotadataManagerEventPublisherPort()
    {
        if (Bootstrap.bootstrapType().equals(BootstrapType.MANAGER))
        {
            return configs.getInteger("motadata.manager.event.publisher.port", 9441);
        }
        else
        {
            return configs.getInteger("motadata.manager.event.publisher.port", 9440);
        }
    }

    public static int getMotadataObserverEventPublisherPort()
    {
        if (configs.containsKey("motadata.observer.event.publisher.port"))
        {
            return configs.getInteger("motadata.observer.event.publisher.port");
        }

        return 9458;
    }

    public static int getMotadataObserverEventSubscriberPort()
    {
        if (configs.containsKey("motadata.observer.event.subscriber.port"))
        {
            return configs.getInteger("motadata.observer.event.subscriber.port");
        }

        return 9459;
    }

    public static int getUDPPort()
    {
        if (configs.containsKey("log.listener.udp.port"))
        {
            return configs.getInteger("log.listener.udp.port");
        }

        return 514;
    }

    public static int getCollectorUDPPort()
    {
        if (configs.containsKey("log.collector.udp.port"))
        {
            return configs.getInteger("log.collector.udp.port");
        }

        return 515;
    }

    public static int getFlowPort()
    {
        if (configs.containsKey("flow.listener.udp.port"))
        {
            return configs.getInteger("flow.listener.udp.port");
        }

        return 3306;
    }

    public static int getTCPPort()
    {
        if (configs.containsKey("log.listener.tcp.port"))
        {
            return configs.getInteger("log.listener.tcp.port");
        }

        return 5140;
    }

    public static int getJobWorkers()
    {
        if (configs.containsKey("job.workers"))
        {
            return Math.max(1, Math.min(configs.getInteger("job.workers"), 6));
        }

        return 2;
    }

    public static int getLogRetentionDays()
    {
        if (configs.containsKey("log.retention.days"))
        {
            return Math.max(1, Math.min(configs.getInteger("log.retention.days"), 7));
        }

        return 2;
    }

    public static int getTrapDispatchers()
    {
        if (configs.containsKey("trap.dispatchers"))
        {
            return Math.max(1, Math.min(configs.getInteger("trap.dispatchers"), 4));
        }

        return 1;
    }

    public static int getWorkers()
    {
        return switch (getDeploymentType())
        {
            //S
            case 0 -> 1;

            //M
            case 1 -> 2;

            //L
            case 2 -> 3;

            //XL
            case 3 -> 5;

            default -> 1;
        };
    }

    public static boolean eventLoggingEnabled()
    {

        return configs.getString("event.logging") != null && configs.getString("event.logging").equalsIgnoreCase(YES);
    }

    public static int getLogLevelResetTimerSeconds()
    {
        if (configs.containsKey("log.level.reset.timer.seconds"))
        {
            return configs.getInteger("log.level.reset.timer.seconds");
        }

        return 900;
    }

    public static int getMetricPolicyWorkers()
    {
        if (configs.containsKey("metric.policy.workers"))
        {
            return Math.max(1, Math.min(configs.getInteger("metric.policy.workers"), 10));
        }
        else
        {

            return switch (getDeploymentType())
            {
                //S
                case 0 -> 2;

                //M
                case 1 -> 4;

                //L
                case 2 -> 6;

                //XL
                case 3 -> 8;

                default -> 2;
            };
        }
    }

    public static int getNetRoutePolicyWorkers()
    {
        if (configs.containsKey("netroute.policy.workers"))
        {
            return Math.max(1, Math.min(configs.getInteger("netroute.policy.workers"), 10));
        }
        else
        {

            return switch (getDeploymentType())
            {
                //S
                case 0 -> 2;

                //M
                case 1 -> 4;

                //L
                case 2 -> 6;

                //XL
                case 3 -> 8;

                default -> 2;
            };
        }
    }

    public static int getAIOpsMetricPolicyWorkers()
    {
        if (configs.containsKey("aiops.metric.policy.workers"))
        {
            return Math.max(1, Math.min(configs.getInteger("aiops.metric.policy.workers"), 6));
        }
        else
        {

            return switch (getDeploymentType())
            {
                //S
                case 0 -> 1;

                //M
                case 1 -> 2;

                //L
                case 2 -> 4;

                //XL
                case 3 -> 6;

                default -> 1;
            };
        }
    }

    public static int getEventPolicyQualifierWorkers()
    {
        if (configs.containsKey("event.policy.qualifier.workers"))
        {
            return Math.max(1, Math.min(configs.getInteger("event.policy.qualifier.workers"), 16));
        }
        else
        {

            return switch (getDeploymentType())
            {
                //S
                case 0 -> 1;

                //M
                case 1 -> 2;

                //L
                case 2 -> 4;

                //XL
                case 3 -> 8;

                default -> 1;
            };
        }
    }

    public static int getEventPolicyWorkers()
    {
        if (configs.containsKey("event.policy.workers"))
        {
            return Math.max(2, Math.min(configs.getInteger("event.policy.workers"), 16));
        }
        else
        {

            return switch (getDeploymentType())
            {
                //S
                case 0 -> 2;

                //M
                case 1 -> 4;

                //L
                case 2 -> 8;

                //XL
                case 3 -> 12;

                default -> 2;
            };
        }
    }


    public static int getPolicyCleanupTimerSeconds()
    {
        if (configs.containsKey("policy.cleanup.timer.seconds"))
        {
            return configs.getInteger("policy.cleanup.timer.seconds");
        }

        return 10;
    }

    public static int getPollingErrorCleanupTimerSeconds()
    {
        if (configs.containsKey("polling.error.cleanup.timer.seconds"))
        {
            return configs.getInteger("polling.error.cleanup.timer.seconds");
        }

        return 10;
    }

    public static int getTagRuleTriggerTimerSeconds()
    {
        if (configs.containsKey("tag.rule.trigger.timer.seconds"))
        {
            return configs.getInteger("tag.rule.trigger.timer.seconds");
        }

        return 120;
    }

    public static int getPolicySetupTimerSeconds()
    {
        if (configs.containsKey("policy.setup.timer.seconds"))
        {
            return configs.getInteger("policy.setup.timer.seconds");
        }

        return 60;
    }

    public static int getEventPolicyInspectionTimerSeconds()
    {
        if (configs.containsKey("event.policy.inspection.timer.seconds"))
        {
            return configs.getInteger("event.policy.inspection.timer.seconds");
        }

        return 10;
    }

    public static int getLogCollectionTimerSeconds()
    {
        if (configs.containsKey("log.collection.timer.seconds"))
        {
            return configs.getInteger("log.collection.timer.seconds");
        }

        return 10;
    }

    public static int getEventPolicyTopNGroups()
    {
        if (configs.containsKey("event.policy.top.groups"))
        {
            return Math.max(5, Math.min(configs.getInteger("event.policy.top.groups"), 100));
        }

        return 100;
    }

    public static int getEventPolicyMaxGroups()
    {
        if (configs.containsKey("event.policy.groups"))
        {
            return Math.max(10, Math.min(configs.getInteger("event.policy.groups"), 1000000));
        }

        return 10000;
    }

    public static int getAIOpsPolicyInspectionTimerSeconds()
    {
        if (configs.containsKey("aiops.policy.inspection.timer.seconds"))
        {
            return configs.getInteger("aiops.policy.inspection.timer.seconds");

        }

        return 900;
    }

    public static int getLogParsers()
    {
        if (configs.containsKey("log.parsers"))
        {
            return Math.max(2, Math.min(configs.getInteger("log.parsers"), 24));
        }
        else
        {
            return switch (getDeploymentType())
            {
                //S
                case 0 -> 2;

                //M
                case 1 -> 6;

                //L
                case 2 -> 12;

                //XL
                case 3 -> 16;

                default -> 2;
            };
        }
    }

    public static int getLogPatternBuilders()
    {
        if (configs.containsKey("log.pattern.builders"))
        {
            return Math.max(4, Math.min(configs.getInteger("log.pattern.builders"), 32));
        }

        return getLogParsers() * 2;
    }

    public static int getFlowProcessors()
    {
        if (configs.containsKey("flow.processors"))
        {
            return Math.max(1, Math.min(configs.getInteger("flow.processors"), 16));
        }
        else
        {
            return switch (getDeploymentType())
            {
                //S
                case 0 -> 2;

                //M
                case 1 -> 4;

                //L
                case 2 -> 6;

                //XL
                case 3 -> 8;

                default -> 2;
            };
        }
    }

    public static int getEventBacklogSize()
    {
        if (configs.containsKey("event.backlog.size"))
        {
            return Math.max(500000, Math.min(configs.getInteger("event.backlog.size"), 2000000));
        }

        return 500000;
    }

    public static boolean logProcessorEnabled()
    {
        if (configs.containsKey("log.processor"))
        {
            return configs.getString("log.processor").equalsIgnoreCase(YES);
        }

        return false;
    }

    public static boolean flowEnabled()
    {
        if (configs.containsKey("flow.enabled"))
        {
            return configs.getString("flow.enabled").equalsIgnoreCase(YES);
        }

        return false;
    }

    public static int getLogProcessorPort()
    {
        if (configs.containsKey("log.processor.port"))
        {
            return configs.getInteger("log.processor.port");
        }

        return 9443;
    }

    public static int getAIRequestPort()
    {
        if (configs.containsKey("ai.request.port"))
        {
            return configs.getInteger("ai.request.port");
        }

        return 9451;
    }

    public static int getEventPublishers()
    {
        if (configs.containsKey("event.publishers"))
        {
            return Math.max(2, Math.min(configs.getInteger("event.publishers"), 5));
        }

        return 2;
    }

    public static int getDatastoreEventSubscriberPort()
    {
        if (configs.containsKey("datastore.event.subscriber.port"))
        {
            return configs.getInteger("datastore.event.subscriber.port");
        }

        return 9456;
    }

    public static int getMotadataManagerEventSubscriberPort()
    {
        if (Bootstrap.bootstrapType().equals(BootstrapType.MANAGER))
        {
            return configs.getInteger("motadata.manager.event.publisher.port", 9440);
        }
        else
        {
            return configs.getInteger("motadata.manager.event.publisher.port", 9441);
        }
    }

    public static int getDiscoveryWorkers()
    {
        if (configs.containsKey("discovery.workers"))
        {
            return Math.max(5, Math.min(configs.getInteger("discovery.workers"), 30));
        }

        return 5;
    }

    public static int getAvailabilityWorkers()
    {
        if (configs.containsKey("availability.workers"))
        {
            return Math.max(1, Math.min(configs.getInteger("availability.workers"), 20));
        }

        return 2;
    }


    public static int getMaxLogSizeBytes()
    {
        if (configs.containsKey("log.max.size.bytes"))
        {
            return Math.max(1000, Math.min(configs.getInteger("log.max.size.bytes"), Integer.MAX_VALUE));
        }

        return 10000;
    }

    public static int getMaxTrapSizeBytes()
    {
        if (configs.containsKey("trap.max.size.bytes"))
        {
            return Math.max(1000, Math.min(configs.getInteger("trap.max.size.bytes"), Integer.MAX_VALUE));
        }

        return 10000;
    }

    public static int getDNSCacheFlushTimerHours()
    {
        if (configs.containsKey("dns.cache.flush.timer.hours")) //Hour
        {
            return Math.max(1, Math.min(configs.getInteger("dns.cache.flush.timer.hours"), 24));  //Hour
        }

        return 6; // hour

    }

    public static int getUserNotificationEntries()
    {
        if (configs.containsKey("user.notification.entries"))
        {
            return Math.max(100, Math.min(configs.getInteger("user.notification.entries"), 100000)); //user notification count
        }

        return 100000;
    }

    public static int getTopologyDiscoveryBatchSize()
    {
        if (configs.containsKey("topology.discovery.batch.size"))
        {
            return Math.max(5, Math.min(configs.getInteger("topology.discovery.batch.size"), 100));
        }

        return 50;
    }

    //in seconds

    public static long getConfigDBServiceTimeoutMillis()
    {
        if (configs.containsKey("config.db.service.timeout.millis"))
        {
            return Math.max(30 * 1000L, Math.min(configs.getInteger("config.db.service.timeout.millis"), 300 * 1000L));
        }

        return 300 * 1000L;
    }

    public static long getCacheServiceTimeoutMillis()
    {
        if (configs.containsKey("cache.service.timeout.millis"))
        {
            return Math.max(30 * 1000L, Math.min(configs.getInteger("cache.service.timeout.millis"), 300 * 1000L));
        }

        return 300 * 1000L;
    }

    public static long getEventAcknowledgeTimeoutMillis()
    {
        if (configs.containsKey("event.acknowledge.timeout.millis"))
        {
            return Math.max(5000, Math.min(30000, configs.getInteger("event.acknowledge.timeout.millis")));
        }

        return 10000;
    }

    public static long getStreamingSessionTimeoutMillis()
    {
        if (configs.containsKey("streaming.session.timeout.millis"))
        {
            return Math.max(5000, Math.min(300000, configs.getInteger("streaming.session.timeout.millis")));
        }

        return 5000;
    }

    public static long getFlowEngineCacheEntries()
    {
        if (configs.containsKey("flow.engine.cache.entries"))
        {
            return configs.getLong("flow.engine.cache.entries");
        }

        return 16411;
    }

    public static long getFlowEngineAggregationTimerMinutes() //in minutes
    {
        if (configs.containsKey("flow.engine.aggregation.timer.minutes"))
        {
            return configs.getLong("flow.engine.aggregation.timer.minutes");
        }

        return 3;
    }

    public static long getFlowEngineRefreshTimerSeconds() //in minutes
    {
        if (configs.containsKey("flow.engine.refresh.timer.seconds"))
        {
            return configs.getLong("flow.engine.refresh.timer.seconds");
        }

        return 180;
    }

    public static int getMetricEnricherInstances()
    {
        if (configs.containsKey("metric.enricher.instances"))
        {
            return Math.max(1, Math.min(configs.getInteger("metric.enricher.instances"), 10));
        }
        else
        {

            return switch (getDeploymentType())
            {
                //S
                case 0 -> 2;

                //M
                case 1 -> 4;

                //L
                case 2 -> 6;

                //XL
                case 3 -> 8;

                default -> 2;
            };
        }
    }

    public static int getRouterInstances()
    {
        if (configs.containsKey("router.instances"))
        {
            return Math.max(1, Math.min(configs.getInteger("router.instances"), 8));
        }
        else
        {

            return switch (getDeploymentType())
            {
                //S
                case 0 -> 1;

                //M
                case 1 -> 2;

                //L
                case 2 -> 4;

                //XL
                case 3 -> 8;

                default -> 1;
            };
        }
    }

    public static int getMetricDatastoreWorkerInstances()
    {
        if (configs.containsKey("metric.datastore.worker.instances"))
        {
            return Math.max(2, Math.min(configs.getInteger("metric.datastore.worker.instances"), 8));
        }
        else
        {

            return switch (getDeploymentType())
            {
                //S
                case 0 -> 2;

                //M
                case 1 -> 4;

                //L
                case 2 -> 6;

                //XL
                case 3 -> 8;

                default -> 2;
            };
        }
    }

    public static int getAvailabilityDatastoreWorkerInstances()
    {
        if (configs.containsKey("availability.datastore.worker.instances"))
        {
            return Math.max(2, Math.min(configs.getInteger("availability.datastore.worker.instances"), 8));
        }
        else
        {

            return switch (getDeploymentType())
            {
                //S
                case 0 -> 2;

                //M
                case 1 -> 4;

                //L
                case 2 -> 6;

                //XL
                case 3 -> 8;

                default -> 2;
            };
        }
    }

    public static int getEventDatastoreWorkerInstances()
    {
        if (configs.containsKey("event.datastore.worker.instances"))
        {
            return Math.max(2, Math.min(configs.getInteger("event.datastore.worker.instances"), 12));
        }
        else
        {

            return switch (getDeploymentType())
            {
                //S
                case 0 -> 2;

                //M
                case 1 -> 4;

                //L
                case 2 -> 6;

                //XL
                case 3 -> 8;

                default -> 2;
            };
        }
    }


    public static int getDependencyLocalDomainInstances()
    {
        if (configs.containsKey("dependency.local.domain.instances"))
        {
            return Math.max(1, Math.min(configs.getInteger("dependency.local.domain.instances"), 20));
        }
        else
        {

            return switch (getDeploymentType())
            {
                //S
                case 0 -> 1;

                //M
                case 1 -> 2;

                //L
                case 2 -> 4;

                //XL
                case 3 -> 8;

                default -> 1;
            };
        }
    }

    public static int getAvailabilityCorrelationEngineInstances()
    {
        if (configs.containsKey("availability.correlation.engine.instances"))
        {
            return Math.max(1, Math.min(configs.getInteger("availability.correlation.engine.instances"), 20));
        }

        return 1;
    }


    public static int getDependencyCrossDomainInstances()
    {
        var instances = 2;

        if (configs.containsKey("dependency.cross.domain.instances"))
        {
            instances = Math.max(1, Math.min(configs.getInteger("dependency.cross.domain.instances"), 20));
        }

        return LicenseUtil.LOG_ENABLED.get() || LicenseUtil.FLOW_ENABLED.get() ? instances : 1;
    }

    public static long getDependencyFlushTimerSeconds()
    {
        if (configs.containsKey("dependency.flush.timer.seconds"))
        {
            return Math.max(30, Math.min(3600, configs.getInteger("dependency.flush.timer.seconds")));
        }

        return 30L;
    }

    public static int getLogStatFlushTimerSeconds()
    {
        if (configs.containsKey("log.stat.flush.timer.seconds"))
        {
            return Math.max(10, Math.min(3600, configs.getInteger("log.stat.flush.timer.seconds")));
        }

        return 60;
    }

    public static int getFlowStatFlushTimerSeconds()
    {
        if (configs.containsKey("flow.stat.flush.timer.seconds"))
        {
            return Math.max(10, Math.min(3600, configs.getInteger("flow.stat.flush.timer.seconds")));
        }

        return 300;
    }

    public static int getEventEngineStatFlushTimerSeconds()
    {
        if (configs.containsKey("event.engine.stat.flush.timer.seconds"))
        {
            return Math.max(10, Math.min(3600, configs.getInteger("event.engine.stat.flush.timer.seconds")));
        }

        return 300;
    }

    public static long getCorrelatedResourceFlushTimerSeconds()
    {
        if (configs.containsKey("correlated.resource.flush.timer.seconds"))
        {
            return Math.max(30, Math.min(3600, configs.getInteger("correlated.resource.flush.timer.seconds")));
        }

        return 30L;
    }

    public static int getDBQueryAbortTimerSeconds()
    {
        if (configs.containsKey("db.query.abort.timer.seconds"))
        {
            return Math.max(1, Math.min(configs.getInteger("db.query.abort.timer.seconds"), 300));
        }

        return 300;
    }

    public static int getVisualizationCacheInvalidationTimerSeconds()
    {
        if (configs.containsKey("visualization.cache.invalidation.timer.seconds"))
        {
            return Math.max(1, Math.min(configs.getInteger("visualization.cache.invalidation.timer.seconds"), 300));
        }

        return 30;
    }

    public static int getVisualizationCacheRefreshTimerSeconds()
    {
        if (configs.containsKey("visualization.cache.refresh.timer.seconds"))
        {
            return Math.max(10, Math.min(configs.getInteger("visualization.cache.refresh.timer.seconds"), 300));
        }

        return 120;
    }

    public static int getVisualizationQueryAbortTimerSeconds()
    {
        if (configs.containsKey("visualization.query.abort.timer.seconds"))
        {
            return Math.max(1, Math.min(configs.getInteger("visualization.query.abort.timer.seconds"), 300));
        }

        return 30;
    }

    public static int getVisualizationManagerInstances()
    {
        if (configs.containsKey("visualization.manager.instances"))
        {
            return Math.max(2, Math.min(configs.getInteger("visualization.manager.instances"), 10));
        }
        else
        {

            return switch (getDeploymentType())
            {
                //S
                case 0 -> 2;

                //M
                case 1 -> 4;

                //L
                case 2 -> 6;

                //XL
                case 3 -> 10;

                default -> 2;
            };
        }
    }

    public static int getVisualizationNetRouteManagerInstances()
    {
        if (configs.containsKey("visualization.netroute.manager.instances"))
        {
            return Math.max(2, Math.min(configs.getInteger("visualization.netroute.manager.instances"), 10));
        }
        else
        {

            return switch (getDeploymentType())
            {
                //S
                case 0 -> 2;

                //M
                case 1 -> 4;

                //L
                case 2 -> 6;

                //XL
                case 3 -> 10;

                default -> 2;
            };
        }
    }

    /**
     * Method used to limit the report export process
     * Default will be 10 export process and user can configure upto 20 export process
     *
     * @return Report Engine Instances
     */
    public static int getReportEngineInstances()
    {
        if (configs.containsKey("report.engine.instances"))
        {
            return Math.max(5, Math.min(configs.getInteger("report.engine.instances"), 20));
        }

        return 10;
    }

    public static int getReportManagerInstances()
    {
        if (configs.containsKey("report.manager.instances"))
        {
            return Math.max(1, Math.min(configs.getInteger("report.manager.instances"), 16));
        }
        else
        {

            return switch (getDeploymentType())
            {
                //S
                case 0 -> 2;

                //M
                case 1 -> 4;

                //L
                case 2 -> 8;

                //XL
                case 3 -> 16;

                default -> 2;
            };
        }
    }

    /**
     * Updated method to change time out range from 30-300 to 30-600 seconds as, required while exporting report with more than 90000 rows.
     *
     * @return Report Engine Time out
     */
    public static int getReportEngineTimeoutSeconds()
    {
        if (configs.containsKey("report.engine.timeout.seconds"))
        {
            return Math.max(30, Math.min(configs.getInteger("report.engine.timeout.seconds"), 600));
        }

        return 120;
    }

    public static String getBrowserBinPath()
    {
        if (configs.containsKey("browser.bin.path"))
        {
            return configs.getString("browser.bin.path");
        }

        return "/usr/bin/google-chrome";
    }

    public static int getReportSocketTimeoutSeconds()
    {
        if (configs.containsKey("report.socket.timeout.seconds"))
        {
            return Math.max(60, Math.min(configs.getInteger("report.socket.timeout.seconds"), 300));
        }

        return 120;
    }

    public static int getUserSessionInactiveTimeoutSeconds()
    {

        if (configs.containsKey("user.inactive.session.timeout.seconds"))
        {
            return Math.min(configs.getInteger("user.inactive.session.timeout.seconds"), 3600);
        }

        return 1800;
    }

    public static int getGeoDBRequestTimeoutSeconds()
    {
        if (configs.containsKey("geo.db.request.timeout.seconds"))
        {
            return Math.max(60, Math.min(configs.getInteger("geo.db.request.timeout.seconds"), 3600));
        }

        return 120;
    }

    public static int getEventCacheEntries()
    {

        if (configs.containsKey("event.cache.entries"))
        {
            return Math.max(500, Math.min(1000000, configs.getInteger("event.cache.entries")));
        }

        return 2000;
    }


    public static Double getLogPatternTokenMatchingScore()
    {
        if (configs.containsKey("log.pattern.token.matching.score"))
        {
            return Math.max(0.5, Math.min(CommonUtil.getDouble(configs.getValue("log.pattern.token.matching.score")), 1));
        }

        return 0.5;
    }

    public static long getLogPatternFlushTimerSeconds()
    {
        if (configs.containsKey("log.pattern.flush.timer.seconds"))
        {
            return Math.max(30, Math.min(3600, configs.getInteger("log.pattern.flush.timer.seconds")));
        }

        return 30L;
    }

    public static long getEventOrdinalFlushTimerSeconds()
    {
        if (configs.containsKey("event.ordinal.flush.timer.seconds"))
        {
            return Math.max(30, Math.min(3600, configs.getInteger("event.ordinal.flush.timer.seconds")));
        }

        return 30L;
    }

    public static long getEventPolicyCacheFlushTimerSeconds()
    {
        if (configs.containsKey("event.policy.cache.flush.timer.seconds"))
        {
            return Math.max(120, Math.min(3600, configs.getInteger("event.policy.cache.flush.timer.seconds")));
        }

        return 120;
    }

    public static long getTrapAckCacheFlushTimerSeconds()
    {
        if (configs.containsKey("trap.ack.cache.flush.timer.seconds"))
        {
            return Math.max(120, Math.min(3600, configs.getInteger("trap.ack.cache.flush.timer.seconds")));
        }

        return 120;
    }


    public static int getEventHistoryQueryQueueSize()
    {
        if (configs.containsKey("event.history.query.queue.size"))
        {
            return Math.max(15, Math.min(100, configs.getInteger("event.history.query.queue.size")));
        }

        return 15;
    }

    public static int getEventQueryQueueSize()
    {
        if (configs.containsKey("event.query.queue.size"))
        {
            return Math.max(50, Math.min(200, configs.getInteger("event.query.queue.size")));
        }

        return 50;
    }

    public static int getMetricQueryQueueSize()
    {
        if (configs.containsKey("metric.query.queue.size"))
        {
            return Math.max(50, Math.min(200, configs.getInteger("metric.query.queue.size")));
        }

        return 50;
    }

    public static int getSNMPTrapForwarderRetries()
    {

        if (configs.containsKey("snmp.trap.forwarder.retries"))
        {
            return Math.max(0, Math.min(3, configs.getInteger("snmp.trap.forwarder.retries")));
        }

        return 1;
    }

    public static int getSNMPTrapForwarderTimeoutSeconds()
    {

        if (configs.containsKey("snmp.trap.forwarder.timeout.seconds"))
        {
            return Math.max(1, Math.min(5, configs.getInteger("snmp.trap.forwarder.timeout.seconds")));
        }

        return 1;
    }

    public static boolean cachingEnabled()
    {
        return configs.getString("visualization.caching") != null && configs.getString("visualization.caching").equalsIgnoreCase(YES);
    }

    public static boolean correlationEnabled()
    {
        return configs.getString("availability.correlation") != null && configs.getString("availability.correlation").equalsIgnoreCase(YES);
    }

    public static boolean archivedObjectEnabled()
    {
        return configs.getString("archived.object.enabled") != null && configs.getString("archived.object.enabled").equalsIgnoreCase(YES);
    }

    public static int getAnomalyPolicyTriggerTimerSeconds()
    {
        if (configs.containsKey("anomaly.policy.trigger.timer.seconds")) //sec
        {
            return Math.max(86400, Math.min(configs.getInteger("anomaly.policy.trigger.timer.seconds"), 604800)); //seconds
        }

        return 86400; // seconds
    }

    public static int getBaselinePolicyTriggerTimerSeconds()
    {
        if (configs.containsKey("baseline.policy.trigger.timer.seconds")) //sec
        {
            return Math.max(259200, Math.min(configs.getInteger("baseline.policy.trigger.timer.seconds"), 604800)); //seconds
        }

        return 259200; // seconds
    }

    public static int getDatastoreRequestBatchSize()
    {
        if (configs.containsKey("datastore.request.batch.size")) //sec
        {
            return Math.max(100, Math.min(configs.getInteger("datastore.request.batch.size"), 300)); //monitors or instances request at time
        }

        return 100; // monitors or instances request at time
    }

    public static int getDatastoreWriterPort()
    {
        if (configs.containsKey("datastore.writer.port"))
        {
            return configs.getInteger("datastore.writer.port");
        }

        return 9455;
    }

    public static int getAvailabilityDatastoreWriterPort()
    {
        if (configs.containsKey("availability.datastore.writer.port"))
        {
            return configs.getInteger("availability.datastore.writer.port");
        }

        return 9471;
    }

    public static int getMetricDatastoreWriterPort()
    {
        if (configs.containsKey("metric.datastore.writer.port"))
        {
            return configs.getInteger("metric.datastore.writer.port");
        }

        return 9472;
    }

    public static int getEventDatastoreWriterPort()
    {
        if (configs.containsKey("event.datastore.writer.port"))
        {
            return configs.getInteger("event.datastore.writer.port");
        }

        return 9473;
    }

    public static int getDatastoreReaderPort()
    {
        if (configs.containsKey("datastore.reader.port"))
        {
            return configs.getInteger("datastore.reader.port");
        }

        return 9457;
    }

    /**
     * Changed method to keep maximum 300 second limit for the ssh connection.
     * Changed Method to keep 1 day max time for the firmware upgrade operations.
     * In the future, we will change based on user feedback
     *
     * @param category Category
     * @return timeout for ssh client
     */
    public static int getConfigRequestTimeoutSeconds(NMSConstants.Category category, ConfigConstants.ConfigOperation configOperation)
    {

        if (configOperation == ConfigConstants.ConfigOperation.UPGRADE)
        {
            if (category == NMSConstants.Category.NETWORK)
            {
                return configs.containsKey("config.upgrade.timeout.seconds") ? Math.max(600, Math.min(configs.getInteger("config.upgrade.timeout.seconds"), 84600)) : 84600;
            }

            return 84600; // 1 day
        }
        else
        {
            if (category == NMSConstants.Category.NETWORK)
            {
                return configs.containsKey("config.request.timeout.seconds") ? Math.max(60, Math.min(configs.getInteger("config.request.timeout.seconds"), 300)) : 60;

            }
            else
            {
                return 60;
            }
        }

    }

    /**
     * Method will return Machine IP which will be used in FTP, SFTP etc.
     * -> First, we will check in the .json
     * -> If host not provided in json file, then we will consider first ip address of any physical interface
     *
     * @return Current VM IP
     */
    public static String getHost()
    {
        var host = "localhost";

        if (configs.containsKey("host") && CommonUtil.isNotNullOrEmpty(configs.getString("host")))
        {
            host = configs.getString("host");
        }
        else
        {
            var ipAddresses = CommonUtil.getIPAddresses();

            if (!ipAddresses.isEmpty())
            {
                host = ipAddresses.getFirst();
            }
        }

        LOGGER.info("Motadata host : " + host);

        return host;
    }

    public static int getConfigManagementResponseProcessorWorkers()
    {
        if (configs.containsKey("config.response.processor.workers"))
        {
            return Math.max(1, Math.min(configs.getInteger("config.response.processor.workers"), 10));
        }
        else
        {
            return switch (getDeploymentType())
            {
                //S
                case 0 -> 4;

                //M
                case 1 -> 6;

                //L
                case 2 -> 8;

                //XL
                case 3 -> 10;

                default -> 4;
            };
        }
    }

    public static int getStatusFlapDurationDumpTimerSeconds()
    {
        if (configs.containsKey("status.flap.duration.flush.timer.seconds"))
        {
            return Math.max(60, Math.min(configs.getInteger("status.flap.duration.flush.timer.seconds"), 3600));//it should be in multiples of 10
        }

        return 120;
    }


    public static int getObjectStatusFlushTimerSeconds()
    {
        if (configs.containsKey("object.status.flush.timer.seconds"))
        {
            return Math.max(60, Math.min(configs.getInteger("object.status.flush.timer.seconds"), 3600));//it should be in multiples of 10
        }

        return 60;
    }

    public static int getNetRouteStatusFlushTimerSeconds()
    {
        if (configs.containsKey("netroute.status.flush.timer.seconds"))
        {
            return Math.max(60, Math.min(configs.getInteger("netroute.status.flush.timer.seconds"), 3600));//it should be in multiples of 10
        }

        return 60;
    }

    public static int getInstanceStatusFlushTimerSeconds()
    {
        if (configs.containsKey("instance.status.flush.timer.seconds"))
        {
            return Math.max(60, Math.min(configs.getInteger("instance.status.flush.timer.seconds"), 3600));//it should be in multiples of 10
        }

        return 300;
    }

    public static int getDiscoveryEventQueueSize()
    {
        if (configs.containsKey("discovery.event.queue.size"))
        {
            return Math.max(5, Math.min(configs.getInteger("discovery.event.queue.size"), 20));
        }

        return 5;
    }

    public static int getPolicyFlapFlushTimerSeconds()
    {
        if (configs.containsKey("policy.flap.flush.timer.seconds"))
        {
            return Math.max(60, Math.min(configs.getInteger("policy.flap.flush.timer.seconds"), 3600));//it should be in multiples of 30
        }

        return 120;
    }


    /**
     * Gets the environment type from configuration.
     * <p>
     * The environment type determines various behaviors and settings throughout the application:
     * - "dev": Development environment with additional debugging and testing features
     * - "test": Testing environment with similar features to development
     * - "prod": Production environment with optimized settings and restricted debug features
     * <p>
     * This setting is used to determine if the application is running in development mode
     * via the devMode() method.
     *
     * @return The configured environment type, or "prod" if not configured
     */
    public static String getEnvironmentType()
    {
        if (CommonUtil.isNotNullOrEmpty(configs.getString("env.type")))
        {
            return configs.getString("env.type");
        }

        // Default to production environment if not configured
        return "prod";
    }

    public static String getDBServerHost()
    {
        if (configs.containsKey("db.host"))
        {
            return configs.getString("db.host");
        }

        return "localhost";
    }

    public static boolean loadArtifactVersions()
    {
        var result = false;

        try
        {
            var file = new File(CURRENT_DIR + PATH_SEPARATOR + VERSION_FILE);

            if (file.exists())
            {
                var lines = FileUtils.readFileToString(file, StandardCharsets.UTF_8).trim().split("\n");

                if (lines.length > 0 && StringUtils.countMatches(lines[0].trim(), DOT_SEPARATOR) >= 2)
                {
                    configs.put(VERSION, lines[0].trim());

                    result = true;

                    var tokens = Arrays.stream(lines).filter(item -> !item.isEmpty()).map(String::trim).toList();            // removed empty lines

                    // if any time version file structure get change, below piece of code needs to be refactored.
                    // current structure : master version , minimum compatible version by type , artifact info.

                    if (CommonUtil.isNotNullOrEmpty(tokens.get(1)) && tokens.get(1).contains("Artifact Versions"))
                    {
                        var i = 2;

                        while (!tokens.get(i).contains("Artifact Git Commits"))
                        {
                            // type : version
                            var typeVersion = tokens.get(i).trim().split(":");

                            if (typeVersion.length == 2 && !typeVersion[0].isEmpty() && StringUtils.countMatches(typeVersion[1].trim(), DOT_SEPARATOR) == 2)
                            {
                                versions.put(typeVersion[0].trim().toLowerCase(), typeVersion[1].trim());

                                i++;
                            }
                        }
                    }
                    else
                    {
                        LOGGER.debug("Artifact versions missing in version file");
                    }
                }
                else
                {
                    LOGGER.warn(String.format("version is not valid , %s ", lines[0].trim()));

                    System.exit(0);
                }
            }
            else
            {
                LOGGER.warn("version file does not exist");

                System.exit(0);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }

    public static String getVersion()
    {
        return configs.getString(VERSION);
    }

    public static JsonObject getConfigs()
    {
        return configs;
    }

    public static String getInstallationMode()
    {
        return configs.getString(INSTALLATION_MODE, InstallationMode.STANDALONE.name());
    }

    public static String getHAMode()
    {
        return configs.getString(HAConstants.HA_MODE, null);
    }

    public static int getInstallationType()
    {
        return configs.getInteger(INSTALLATION_TYPE, 0);
    }

    /**
     * Method will return ssh port
     * -> This may be used when user have different ssh port other than 22
     *
     * @return SSH Port
     */
    public static int getSSHPort()
    {
        return configs.getInteger("ssh.port", SSH_PORT);
    }

    public static int getProcessDetectionAttempts()
    {
        return CommonUtil.getInteger(configs.getValue("motadata.process.detection.attempts", 100));
    }

    public static int getDatastoreProcessDetectionAttempts()
    {
        return CommonUtil.getInteger(configs.getValue("motadata.datastore.process.detection.attempts", 70));
    }


    public static String getVIPCommand()
    {
        return CommonUtil.isNotNullOrEmpty(configs.getString("vip.command")) ? configs.getString("vip.command") : "sudo ifconfig ens160:2 %s netmask ************* %s";
    }

    public static String getVIPIPAddress()
    {
        return configs.containsKey("vip.ip") ? configs.getString("vip.ip") : "";
    }

    public static long getConfigDBPollTimerSeconds()
    {
        return configs.getInteger("config.db.poll.timer.seconds", 10) * 1000L;
    }

    public static long getDatastorePollTimerSeconds()
    {
        return configs.getInteger("datastore.poll.timer.seconds", 30) * 1000L;
    }

    public static long getCachePollTimerSeconds()
    {
        return configs.getInteger("cache.db.poll.timer.seconds", 10) * 1000L;
    }

    public static int getHAPrimaryNodePingRetries()
    {
        if (isHAOverWAN())
        {
            return configs.getInteger("ha.primary.node.ping.retries", 9);
        }
        else
        {
            return configs.getInteger("ha.primary.node.ping.retries", 3);
        }
    }

    public static long getHAPrimaryNodeHealthInspectionTimerSeconds()
    {
        return configs.getInteger("ha.primary.node.health.inspection.timer.seconds", 10) * 1000L;
    }

    public static boolean isHAOverWAN()
    {
        if (configs.containsKey("ha.over.wan") && CommonUtil.isNotNullOrEmpty(configs.getString("ha.over.wan")))
        {
            return configs.getString("ha.over.wan").equalsIgnoreCase(YES);
        }
        else
        {
            return false;
        }
    }

    // version should be greater than minimum version available in version file
    public static boolean compatible(String type, String version)
    {
        if (type.equalsIgnoreCase(BootstrapType.AGENT.name()))
        {
            return versions.containsKey(type.toLowerCase()) && !upgradable(versions.getString(type.toLowerCase()).trim(), version.trim(), 3);
        }
        else
        {
            return !upgradable(MotadataConfigUtil.getVersion().trim(), version.trim(), 3);
        }
    }

    /***
     *
     * @param newVersion : version to be compared
     * @param oldVersion : newVersion to be compared with
     * @param limit : defines the maximum number of versions to check (used in case of compatability check because
     *              we're not considering the hotfix version in the compatibility check), -1 if there's no limit
     * @return true if target is greater than source
     * if currentVersion > newVersion, then will show that agent/collector can be upgraded.
     */
    public static boolean upgradable(String newVersion, String oldVersion, int limit)
    {
        if (newVersion.trim().equalsIgnoreCase(oldVersion.trim()))
        {
            return false;
        }

        List<Integer> newVersionTokens;

        List<Integer> oldVersionTokens;

        if (limit > 0)
        {
            newVersionTokens = Arrays.stream(newVersion.split("\\.")).limit(limit).filter(item -> !item.isEmpty()).map(CommonUtil::getInteger).collect(Collectors.toList());

            oldVersionTokens = Arrays.stream(oldVersion.split("\\.")).limit(limit).filter(item -> !item.isEmpty()).map(CommonUtil::getInteger).collect(Collectors.toList());
        }
        else
        {
            newVersionTokens = Arrays.stream(newVersion.split("\\.")).filter(item -> !item.isEmpty()).map(CommonUtil::getInteger).collect(Collectors.toList());

            oldVersionTokens = Arrays.stream(oldVersion.split("\\.")).filter(item -> !item.isEmpty()).map(CommonUtil::getInteger).collect(Collectors.toList());
        }

        if (newVersionTokens.size() >= 3 && oldVersionTokens.size() >= 3)              // if size is not 3 then it could be possible that it has old version. in that need to show that update required.
        {
            var upgradable = false;

            // if "8.0.8" tries to compare with "8.0.8.1" then add manually 0 to the "8.0.8"
            for (var index = 0; index < Math.max(newVersionTokens.size(), oldVersionTokens.size()); index++)
            {
                if (index > (newVersionTokens.size() - 1))
                {
                    newVersionTokens.add(0);
                }

                if (index > (oldVersionTokens.size() - 1))
                {
                    oldVersionTokens.add(0);
                }

                if (newVersionTokens.get(index) > oldVersionTokens.get(index))                    // if targetVersion is greater, then update is required
                {
                    upgradable = true;

                    break;
                }

                if (newVersionTokens.get(index) < oldVersionTokens.get(index))                    // if sourceVersion is greater, then update is not required. it is already updated.
                {
                    break;
                }
            }

            return upgradable;
        }

        return true;
    }

    public static boolean upgradable(String newVersion, String oldVersion)
    {
        return upgradable(newVersion, oldVersion, -1);
    }

    public static int getPingCheckTimeoutSeconds()
    {
        if (configs.containsKey("ping.check.timeout.seconds"))
        {
            return Math.max(1, Math.min(configs.getInteger("ping.check.timeout.seconds"), 5));
        }

        return 1;
    }

    public static int getPingCheckPackets()
    {
        if (configs.containsKey("ping.check.packets"))
        {
            return Math.max(1, Math.min(configs.getInteger("ping.check.packets"), 10));
        }

        return 3;
    }

    public static int getLogParserPluginTimeoutSeconds()
    {
        if (configs.containsKey("log.parser.plugin.timeout.seconds"))
        {
            return Math.max(10, Math.min(configs.getInteger("log.parser.plugin.timeout.seconds"), 120));
        }

        return 20;
    }

    public static int getReportManagerCustomScriptTimeoutSeconds()
    {
        if (configs.containsKey("report.manager.custom.script.timeout.seconds"))
        {
            return Math.max(60, Math.min(configs.getInteger("report.manager.custom.script.timeout.seconds"), 300));
        }

        return 60;
    }

    public static int getUISessionTimeoutMinutes()
    {
        if (configs.containsKey("ui.session.timeout.minutes"))
        {
            return Math.max(1, Math.min(configs.getInteger("ui.session.timeout.minutes"), 120));
        }

        return 5;
    }

    public static int getRemoteSessionProbeMinutes()
    {
        return configs.getInteger("remote.session.probe.minutes", 5);
    }

    public static int getRemoteSessionTimeoutMinutes()
    {
        return configs.getInteger("remote.session.timeout.minutes", 2);
    }

    public static int getStatusFlapCacheFlushTimerSeconds()
    {
        if (configs.containsKey("status.flap.cache.flush.timer.seconds"))
        {
            return Math.max(60, Math.min(configs.getInteger("status.flap.cache.flush.timer.seconds"), 3600));
        }

        return 120;
    }

    public static int getShutdownHookTimeoutSeconds()
    {
        return configs.getInteger("shutdown.hook.timeout.seconds", 120);
    }

    public static int getHAFailoverNodes()
    {
        if (configs.containsKey("ha.failover.nodes"))
        {
            return configs.getInteger("ha.failover.nodes");
        }

        return 2;
    }

    public static int getIntegrationPingTimerSeconds()
    {
        if (configs.containsKey("integration.ping.timer.seconds"))
        {
            return Math.max(30, Math.min(configs.getInteger("integration.ping.timer.seconds"), 600));
        }

        return 60;
    }

    public static int getIntegrationPendingEventFlushTimerSeconds()
    {
        if (configs.containsKey("integration.pending.event.flush.timer.seconds"))
        {
            return Math.max(60, Math.min(configs.getInteger("integration.pending.event.flush.timer.seconds"), 3600));//it should be in multiples of 30
        }

        return 120;
    }

    public static int getIntegrationCacheFlushTimerSeconds()
    {
        if (configs.containsKey("integration.cache.flush.timer.seconds"))
        {
            return Math.max(60, Math.min(configs.getInteger("integration.cache.flush.timer.seconds"), 3600));//it should be in multiples of 30
        }

        return 120;
    }

    public static int getRunbookWorklogCacheFlushTimerSeconds()
    {
        if (configs.containsKey("runbook.worklog.cache.flush.timer.seconds"))
        {
            return Math.max(60, Math.min(configs.getInteger("runbook.worklog.cache.flush.timer.seconds"), 3600));//it should be in multiples of 60
        }

        return 1800;
    }

    public static int getIntegrationEventQueueSize()
    {
        if (configs.containsKey("integration.queue.size"))
        {
            return Math.max(1, Math.min(configs.getInteger("integration.queue.size"), 50));
        }

        return 1;
    }

    public static int getDatastoreRetentionDays()
    {
        if (configs.containsKey("datastore.retention.days"))
        {
            return Math.max(3, Math.min(configs.getInteger("datastore.retention.days"), 15));
        }

        return 7;
    }

    public static int getCacheFilesRetentionDays()
    {
        if (configs.containsKey("cache.files.retention.days"))
        {
            return Math.max(1, Math.min(configs.getInteger("cache.files.retention.days"), 14));
        }

        return 7;
    }

    public static int getPluginEngineCPUCores()
    {
        if (configs.containsKey("plugin.engine.cpu.cores"))
        {
            return Math.max(1, Math.min(configs.getInteger("plugin.engine.cpu.cores"), 24));
        }

        return 1;
    }

    public static String getDownInterfaceDiscoveryStatus()
    {
        if (configs.containsKey("discover.down.interface.status") && !configs.getString("discover.down.interface.status").isEmpty())
        {
            return configs.getString("discover.down.interface.status");
        }
        return NO;
    }

    public static int getEngineStatFlushTimerSeconds()
    {
        if (configs.containsKey("engine.stat.flush.timer.seconds"))
        {
            return Math.max(Math.min(configs.getInteger("engine.stat.flush.timer.seconds"), 600), 30);
        }

        return 60;
    }

    public static int getJVMStatPollTimerSeconds()
    {
        if (configs.containsKey("jvm.stat.poll.timer.seconds"))
        {
            return Math.max(Math.min(configs.getInteger("jvm.stat.poll.timer.seconds"), 600), 60);
        }

        return 300;
    }

    public static int getDatastoreEventQueueSize()
    {
        if (configs.containsKey("datastore.event.queue.size"))
        {
            return Math.max(500000, Math.min(configs.getInteger("datastore.event.queue.size"), 2000000));
        }

        return 500000;
    }

    public static int getAvailabilityDatastoreEngineQueueSize()
    {
        if (configs.containsKey("availability.datastore.engine.queue.size"))
        {
            return Math.max(2000, Math.min(configs.getInteger("availability.datastore.engine.queue.size"), 20000));
        }

        return 5000;
    }

    public static int getMetricDatastoreEngineQueueSize()
    {
        if (configs.containsKey("metric.datastore.engine.queue.size"))
        {
            return Math.max(2000, Math.min(configs.getInteger("metric.datastore.engine.queue.size"), 20000));
        }

        return 5000;
    }

    public static int getEventDatastoreEngineQueueSize()
    {
        if (configs.containsKey("event.datastore.engine.queue.size"))
        {
            return Math.max(2000, Math.min(configs.getInteger("event.datastore.engine.queue.size"), 20000));
        }

        return 5000;
    }

    public static int getDatastoreFileCloseTimerSeconds()
    {
        if (configs.containsKey("datastore.file.close.timer.seconds"))
        {
            return Math.max(90, Math.min(configs.getInteger("datastore.file.close.timer.seconds"), 360));
        }

        return 180;         // default 30 min
    }

    /**
     * Limiting users to view maximum 5000 records in the report preview
     * Default will be 1000 records
     *
     * @return Records
     */
    public static int getReportPreviewMaxRecords()
    {
        if (configs.containsKey("report.preview.max.records"))
        {
            return Math.max(1000, Math.min(configs.getInteger("report.preview.max.records"), 5000));
        }

        return 1000;
    }

    // Allow use to explicitly define the trusted origins (MOTADATA-2919)
    public static JsonArray getTrustedOrigins()
    {
        return configs.getJsonArray("trusted.origins", new JsonArray());
    }

    // Defines the set of error based on which the sms gateway test is failed or succeeded
    public static Set<String> getSMSGatewayErrors()
    {
        return MotadataConfigUtil.getConfigs().containsKey("sms.gateway.errors")
                ? new HashSet<String>(MotadataConfigUtil.getConfigs().getJsonArray("sms.gateway.errors").getList())
                : Set.of("failure", "failed", "invalid", "error", "unsuccessful");
    }

    // Defines the set of error based on which the o auth provider test is failed or succeeded
    public static Set<String> getOAuthProviderErrors()
    {
        return MotadataConfigUtil.getConfigs().containsKey("oauth.provider.errors")
                ? new HashSet<String>(MotadataConfigUtil.getConfigs().getJsonArray("oauth.provider.errors").getList())
                : Set.of("failure", "failed", "invalid", "error", "unsuccessful");
    }

    /**
     * MOTADATA-2701
     * Defines the number of connection for mail to be created in a connection pool
     * Default value is 1, for no concurrent connection to be formed
     *
     * @return PoolSize
     */
    public static int getSMTPMaxConnections()
    {
        return Math.max(1, Math.min(configs.getInteger("smpt.max.connections", 1), 10));
    }

    public static int getConfigUpgradeMaxWorkers()
    {
        if (configs.containsKey("config.upgrade.max.workers"))
        {
            return Math.max(Math.min(configs.getInteger("config.upgrade.max.workers"), 32), 8);
        }

        return 8;
    }

    /**
     * MOTADATA-970
     * Defines the retention days for flap cache backup
     * Default value is 1
     *
     * @return RetentionDays
     */
    public static int getFlapCacheRetentionDays()
    {
        return Math.max(1, Math.min(configs.getInteger("flap.cache.retention.days", 1), 3));
    }

    /**
     * We will allow user to connect 10 ssh client in parallel but user can configure it to maximum 50 sessions
     *
     * @return Max SSH Sessions
     */
    public static int getSSHMaxClients()
    {
        if (configs.containsKey("ssh.max.clients"))
        {
            return Math.max(Math.min(configs.getInteger("ssh.max.clients"), 50), 10);
        }

        return 10;
    }

    /**
     * We will allow ssh client to be connected for minimum 10 minutes and maximum user can configure to 20 minutes
     *
     * @return SSH client process expiration time
     */
    public static int getSSHClientTimeoutSeconds()
    {
        if (configs.containsKey("ssh.client.timeout.seconds"))
        {
            return Math.max(Math.min(configs.getInteger("ssh.client.timeout.seconds"), 1200), 600);
        }

        return 600;
    }

    public static int getLogForwarderTCPRetries()
    {
        if (configs.containsKey("log.forwarder.tcp.retries"))
        {
            return Math.max(1, Math.min(configs.getInteger("log.forwarder.tcp.retries", 1), 3));
        }

        return 3;
    }

    public static int getLogForwarderTCPRetrySeconds()
    {
        if (configs.containsKey("log.forwarder.tcp.retry.seconds"))
        {
            return Math.max(1, Math.min(configs.getInteger("log.forwarder.tcp.retry.seconds", 3), 10));
        }

        return 5;
    }

    public static int getComplianceRuleEngineWorkers()
    {
        if (configs.containsKey("compliance.rule.engine.max.workers"))
        {
            return Math.max(Math.min(configs.getInteger("compliance.rule.engine.max.workers"), 16), 2);
        }
        else
        {

            return switch (getDeploymentType())
            {
                //S
                case 0 -> 2;

                //M
                case 1 -> 4;

                //L
                case 2 -> 8;

                //XL
                case 3 -> 16;

                default -> 2;
            };
        }
    }

    public static int getIntegrationAPITimeoutSeconds()
    {
        return configs.getInteger("integration.api.timeout.seconds", 10);
    }

    public static int getMaxPolicyNotifications()
    {
        if (configs.containsKey("policy.max.notifications"))
        {
            return Math.max(20, Math.min(configs.getInteger("policy.max.notifications"), 500));
        }

        return LicenseUtil.LOG_ENABLED.get() || LicenseUtil.FLOW_ENABLED.get() ? 200 : 100;
    }

    public static int getIntegrationTimeoutSeconds()
    {
        if (configs.containsKey("integration.timeout.seconds"))
        {
            return Math.max(30, Math.min(configs.getInteger("integration.timeout.seconds"), 600));
        }

        return 60;
    }

    public static int getIntegrationMaxRetries()
    {
        if (configs.containsKey("integration.max.retries"))
        {
            return Math.max(2, Math.min(configs.getInteger("integration.max.retries"), 10));
        }

        return 2;
    }

    public static int getIntegrationRetryTimerSeconds()
    {
        if (configs.containsKey("integration.retry.timer.seconds"))
        {
            return Math.max(10, Math.min(configs.getInteger("integration.retry.timer.seconds"), 600));
        }

        return 10;
    }

    public static int getPostgresDatabasePort()
    {
        if (configs.containsKey("postgres.database.port"))
        {
            return configs.getInteger("postgres.database.port");
        }

        return 5432;
    }

    public static String getPostgresDatabaseHost()
    {
        if (configs.containsKey("postgres.database.host"))
        {
            return configs.getString("postgres.database.host");
        }

        return "localhost";
    }

    // used to configure max key length of json object
    public static int getMaxNameLength()
    {
        return configs.getInteger("max.name.length", 150000);
    }

    public static Integer getFlowRate() //per sec
    {
        if (configs.containsKey("flow.rate"))
        {
            return configs.getInteger("flow.rate"); //per sec
        }

        return CommonUtil.getInteger((CommonUtil.getInteger(LicenseUtil.LICENSED_FLOW_QUOTA_BYTES.get() / (400 * 86400)) / 10) * 10);//will be considering default flow size 400 bytes
    }

    public static long getFlowCacheQueryTimerSeconds()
    {

        if (configs.containsKey("flow.cache.query.timer.seconds"))
        {
            return configs.getInteger("flow.cache.query.timer.seconds");
        }

        return 60;
    }

    /**
     * Retrieves the set of status flap instances.
     * If the configuration contains the key `status.flap.instances`, it returns the set of instances
     * defined in the configuration. Otherwise, it defaults to a set containing "interface".
     *
     * @return A set of status flap instances.
     */
    public static Set<String> getStatusFlapInstances()
    {
        if (configs.containsKey("status.flap.instances"))
        {
            return new HashSet<>(configs.getJsonArray("status.flap.instances").getList());
        }

        return Set.of("interface");
    }

    public static String getPluginEngine()
    {
        if (configs.containsKey(PluginEngineConstants.PLUGIN_ENGINE))
        {
            return configs.getString(PluginEngineConstants.PLUGIN_ENGINE);
        }

        return "go";
    }

    // This parameter determines the number of times the command will probe the plugin to retrieve 'multipath' information.
    // A higher number increases the likelihood of discovering more nodes at the same TTL.
    //
    // Improvement: implement dynamic probing. Instead of using a fixed number, adjust the probe attempts dynamically based on initial results.
    // This would allow users to discover more multipaths in every time.
    public static int getNetRouteMaxProbeAttempts()
    {
        if (configs.containsKey("netroute.max.probe.attempts"))
        {
            return Math.max(1, Math.min(configs.getInteger("netroute.max.probe.attempts"), 10));
        }

        return 3;
    }

    // This specifies the number of packets to send per probe, this can also be done dynamically in future.
    public static int getNetRouteMaxProbePackets()
    {
        if (configs.containsKey("netroute.max.probe.packets"))
        {
            return Math.max(1, Math.min(configs.getInteger("netroute.max.probe.packets"), 5));
        }

        return 2;
    }

    //limit : max no of hops we'll receive in our result
    public static int getNetRouteMaxProbeHops()
    {
        if (configs.containsKey("netroute.max.probe.hops"))
        {
            return Math.max(1, Math.min(configs.getInteger("netroute.max.probe.hops"), 64));
        }

        return 30;
    }

    // for overall source to destination
    public static int getNetRoutePacketRetries()
    {
        if (configs.containsKey("netroute.packet.retries"))
        {
            return Math.max(1, Math.min(configs.getInteger("netroute.packet.retries"), 10));
        }

        return 3;
    }

    // this protocol is only for overall source to destination
    public static String getNetRouteProtocol()
    {
        if (configs.containsKey("netroute.protocol"))
        {
            return configs.getString("netroute.protocol").toLowerCase();
        }

        return "tcp";
    }

    public static int getStatusDownFlaps()
    {
        if (configs.containsKey("status.down.flaps"))
        {
            return Math.max(1, Math.min(configs.getInteger("status.down.flaps"), 10));
        }

        return 1;
    }
}
