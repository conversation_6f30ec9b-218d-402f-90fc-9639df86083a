/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			    Notes
 *  25-Feb-2025		Chandresh		    MOTADATA-3680: Added INSTANCE field to worklog
 *  24-Feb-2025		Darshan Parmar	    MOTADATA-5215: SonarQube Suggestions Resolution
 *  25-Feb-2025		<PERSON><PERSON>ATA-5218: Refactored the evaluateCondition() method to replace isParseable() with isCreateable() for evaluating floating values with 'E', hex, octal, and other numeric formats.
 *  28-Feb-2025		Bharat Chaudhari    MOTADATA-5233: Update getTimeStampFromEpochTimestamp method call and constant added.
 *  25-Mar-2025		Chopra Deven 		MOTADATA-5299: Changed message for forecast policy.
 *  21-Feb-2025		Pruthviraj          MOTADATA-4903: policy constant added for netroute , added method replaceing holder and method signature change for reusing code
 * 	4-Apr-2025		Sankalp     		MOTADATA-5565: Added support for instance counters to be used as macros in policy message.
 *  9-Apr-2025      Bharat              MOTADATA-5141: Alert Drill-down from email and Teams Notification
 *  23-Apr-2025     Sankalp             Replaced id with Object id in policy triggered notifications
 */

/**
 * PolicyEngineConstants serves as the core foundation for the policy engine framework.
 * <p>
 * This class provides:
 * 1. Constants and enumerations used throughout the policy package
 * 2. Utility methods for policy evaluation and condition checking
 * 3. Functions for processing policy flaps (state changes)
 * 4. Methods for triggering actions based on policy conditions
 * 5. Placeholder replacement utilities for customizing notification messages
 * <p>
 * The policy engine is responsible for evaluating metrics, events, and network routes
 * against defined policies and executing appropriate actions when conditions are met.
 */
package com.mindarray.policy;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.api.*;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.nms.SNMPTrapProcessor;
import com.mindarray.notification.MicrosoftTeamsNotification;
import com.mindarray.notification.Notification;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.Future;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.handler.sockjs.impl.StringEscapeUtils;
import io.vertx.ext.web.templ.freemarker.FreeMarkerTemplateEngine;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.text.StringSubstitutor;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.aiops.AIOpsConstants.CORRELATED_UNREACHABLE_OBJECTS;
import static com.mindarray.aiops.AIOpsConstants.CORRELATION_OBJECTS;
import static com.mindarray.api.APIConstants.ENTITY_ID;
import static com.mindarray.api.MetricPolicy.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.OBJECTS;
import static com.mindarray.notification.Notification.NOTIFICATION_TYPE;

/**
 * The PolicyEngineConstants class serves as a central repository for constants, enums, and utility methods
 * used throughout the policy system in the Motadata platform.
 * <p>
 * This class provides:
 * <ul>
 *   <li>String constants for policy attributes, fields, and configuration parameters</li>
 *   <li>Enums defining policy types, operators, threshold types, and action types</li>
 *   <li>Utility methods for condition evaluation and policy processing</li>
 *   <li>Functions for handling notifications and placeholder substitution</li>
 *   <li>Methods for managing policy triggers, flapping detection, and auto-clearing</li>
 * </ul>
 * <p>
 * The constants and methods in this class are used by various policy inspectors and processors
 * to ensure consistent behavior across different policy types and implementations.
 */
public class PolicyEngineConstants
{
    public static final String POLICY_NAME = "policy.name";

    public static final String POLICY_ARCHIVED = "policy.archived";

    public static final String POLICY_TYPE = "policy.type";

    public static final String POLICY_CONTEXT = "policy.context";

    public static final String POLICY_CREATION_TIME = "policy.creation.time";

    public static final String POLICY_STATE = "policy.state";

    public static final String POLICY_SEVERITY = "policy.severity";

    public static final String POLICY_TAGS = "policy.tags";

    public static final String POLICY_TITLE = "policy.title";

    public static final String POLICY_MESSAGE = "policy.message";

    public static final String POLICY_EMAIL_TEMPLATE = "policy.email.template";

    public static final String POLICY_ACTIONS = "policy.actions";

    public static final String ACTIVE_SINCE = "active.since";

    public static final String POLICY_NOTIFICATION_CONTEXT = "policy.notification.context";

    public static final String POLICY_NOTIFICATION_SEVERITY = "policy.notification.severity";

    public static final String POLICY_RENOTIFICATION_CONTEXT = "policy.renotification.context";

    public static final String POLICY_EMAIL_NOTIFICATION_RECIPIENTS = "policy.email.notification.recipients";

    public static final String POLICY_USER_NOTIFICATION_RECIPIENTS = "policy.user.notification.recipients";

    public static final String RENOTIFY_ACKNOWLEDGED = "renotify.acknowledged";

    public static final String METRIC_POLICY_EMAIL_NOTIFICATION_DEFAULT_SUBJECT = "$$$severity$$$ - $$$object.name$$$";

    public static final String EVENT_POLICY_EMAIL_NOTIFICATION_DEFAULT_SUBJECT = "$$$severity$$$ - $$$policy.type$$$ : $$$policy.name$$$ has been triggered";

    public static final String METRIC_POLICY_EMAIL_NOTIFICATION_DEFAULT_MESSAGE = "$$$counter$$$ has entered into $$$severity$$$ state with value $$$value$$$";

    public static final String EVENT_POLICY_EMAIL_NOTIFICATION_DEFAULT_MESSAGE = "$$$condition$$$ $$$aggregator$$$ $$$operator$$$ $$$value$$$";

    public static final String FLOW_POLICY_EMAIL_NOTIFICATION_DEFAULT_MESSAGE = "$$$condition$$$ $$$operator$$$ $$$value$$$";

    public static final String NETROUTE_SOURCE_TO_DESTINATION_POLICY_EMAIL_NOTIFICATION_DEFAULT_MESSAGE = "$$$counter$$$ has entered into $$$severity$$$ state with value $$$value$$$ on route $$$netroute.name$$$";

    public static final String NETROUTE_HOP_BY_HOP_POLICY_EMAIL_NOTIFICATION_DEFAULT_MESSAGE = "$$$counter$$$ has entered into $$$severity$$$ state indicating one or more hops exceeded threshold value $$$policy.condition$$$ $$$policy.threshold$$$";

    public static final String POLICY_RENOTIFICATION_MESSAGE = "$$$object.host$$$ - $$$object.ip$$$ is in $$$severity$$$ state since $$$active.since$$$";

    public static final String POLICY_ID = "policy.id";

    public static final String POLICY_KEY = "policy.key";

    public static final String POLICY_FIRST_TRIGGER_TICK = "policy.first.trigger.tick";

    public static final String POLICY_LAST_TRIGGER_TICK = "policy.last.trigger.tick";

    public static final String POLICY_SUPPRESSION_TIME = "policy.suppression.time";

    public static final String EVENT_FIELD = "event.field";

    public static final String INSTANCE_TYPE = "instance.type";

    public static final String DURATION = "duration";

    public static final String RECIPIENTS = "recipients";

    public static final String RECIPIENT = "recipient";

    public static final String RECIPIENT_TYPE = "type";

    public static final String[] PLACEHOLDERS = {"$$$severity.color$$$", "$$$policy.trigger.time$$$", "$$$object.name$$$", "$$$object.ip$$$", "$$$object.host$$$", "$$$object.type$$$", "$$$object.groups$$$", "$$$metric$$$", "$$$value$$$", "$$$instance$$$",
            "$$$severity$$$", "$$$policy.name$$$", "$$$policy.type$$$", "$$$policy.message$$$", "$$$counter$$$", "$$$active.since$$$", "$$$trigger.condition$$$", "$$$aggregator$$$", "$$$condition$$$", "$$$operator$$$", "$$$event.source$$$", "$$$trap.oid$$$",
            "$$$translated.message$$$", "$$$original.message$$$", "$$$policy.condition$$$", "$$$policy.threshold$$$", "$$$netroute.name$$$", "$$$netroute.source$$$", "$$$netroute.destination$$$"};

    public static final String INSTANCE_METRIC_CONTEXT = "instance.metric.context";

    public static final String RUNBOOK_WORKLOG_STATUS = "runbook.worklog.status";

    public static final int RUNBOOK_WORKLOG_RESULT_MAX_BYTES = 1000000; // Keeping runbook worklog output maximum limit ~ 1 MB

    public static final String RUNBOOK_WORKLOG_TYPE = "runbook.worklog.type";

    public static final String RUNBOOK_WORKLOG_ID = "runbook.worklog.id";

    public static final String RUNBOOK_WORKLOG_RESULT = "runbook.worklog.result";

    public static final String RUNBOOK_WORKLOG_ERROR = "runbook.worklog.error";

    public static final String POLICY_TRIGGER_ID = "policy.trigger.id";

    public static final String POLICY_TRIGGER_EVALUATION_WINDOW = "policy.trigger.evaluation.window";

    public static final String POLICY_TRIGGER_VALUE = "policy.trigger.value";

    public static final String POLICY_TRIGGER_POLICY_ID = "policy.trigger.policy.id";

    public static final String POLICY_EVALUATION_TYPE = "policy.evaluation.type";

    public static final String POLICY_URL = "policy.url";
    public static final String POLICY_URL_VALUE = (MotadataConfigUtil.httpsEnabled() ? "https" : "http") + "://" + MotadataConfigUtil.getHost() + COLON_SEPARATOR + MotadataConfigUtil.getHTTPServerPort(BootstrapType.APP.name()) + "/alerts/detail/Server/overview/%s";
    public static final String METRIC_POLICY_TRIGGER_TICKS = "metric-policy-trigger-ticks";
    public static final String EVENT_POLICY_TRIGGER_TICKS = "event-policy-trigger-ticks";
    public static final String NETROUTE_POLICY_TRIGGER_TICKS = "netroute-policy-trigger-ticks";
    public static final String METRIC_POLICY_FLAP_DURATIONS = "metric-policy-flap-durations";
    public static final String NETROUTE_POLICY_FLAP_DURATIONS = "netroute-policy-flap-durations";
    public static final Set<String> AIOPS_POLICY_TYPES = Set.of(PolicyType.BASELINE.getName(), PolicyType.FORECAST.getName(), PolicyType.ANOMALY.getName());
    public static final JsonArray POLICY_SEVERITIES = new JsonArray().add(GlobalConstants.Severity.CRITICAL.name()).add(GlobalConstants.Severity.MAJOR.name()).add(GlobalConstants.Severity.WARNING.name());
    public static final String POLICY_ACKNOWLEDGE = "policy.acknowledge";
    public static final String POLICY_ACKNOWLEDGE_TIME = "policy.acknowledge.time";
    public static final String POLICY_ACKNOWLEDGE_BY = "policy.acknowledge.by";
    public static final String POLICY_NOTE = "policy.note";
    public static final String SEVERITY_TYPE = "severity.type";
    public static final String TIMER_SECONDS = "timer.seconds"; // policy renotify timer seconds
    public static final String CHANNELS = "channels";
    public static final String POLICY_DRILL_DOWN_TEMPLATE = "policy.drill.down.template";
    private static final DeliveryOptions DELIVERY_OPTIONS = new DeliveryOptions().setSendTimeout(400000L);
    private static final FreeMarkerTemplateEngine templateEngine = FreeMarkerTemplateEngine.create(Bootstrap.vertx());

    private PolicyEngineConstants()
    {

    }

    /**
     * Evaluates a condition by comparing two values using the specified operator.
     *
     * @param inclusive      Flag indicating whether the condition is inclusive or exclusive
     * @param condition      The operator to use for comparison (e.g., "equal", "greater than")
     * @param conditionValue The reference value to compare against
     * @param operandValue   The value being evaluated
     * @return True if the condition is satisfied, false otherwise
     */
    public static boolean evaluateCondition(boolean inclusive, String condition, Object conditionValue, Object operandValue)
    {
        var result = false;

        condition = inclusive ? condition : invertCondition(condition);

        var value = CommonUtil.getString(operandValue).toLowerCase();

        if (condition != null && conditionValue != null)
        {
            result = switch (Operator.valueOfName(condition))
            {
                case EQUAL ->
                        NumberUtils.isCreatable(value) ? CommonUtil.getFloat(operandValue) == CommonUtil.getFloat(conditionValue) : value.equalsIgnoreCase(CommonUtil.getString(conditionValue).toLowerCase());

                case NOT_EQUAL ->
                        NumberUtils.isCreatable(value) ? CommonUtil.getFloat(operandValue) != CommonUtil.getFloat(conditionValue) : !value.equalsIgnoreCase(CommonUtil.getString(conditionValue).toLowerCase());

                case GREATER_THAN, ABOVE ->
                        NumberUtils.isCreatable(value) && CommonUtil.getFloat(operandValue) > CommonUtil.getFloat(conditionValue);

                case LESS_THAN, BELOW ->
                        NumberUtils.isCreatable(value) && CommonUtil.getFloat(operandValue) < CommonUtil.getFloat(conditionValue);

                case GREATER_THAN_OR_EQUAL ->
                        NumberUtils.isCreatable(value) && CommonUtil.getFloat(operandValue) >= CommonUtil.getFloat(conditionValue);

                case LESS_THAN_OR_EQUAL ->
                        NumberUtils.isCreatable(value) && CommonUtil.getFloat(operandValue) <= CommonUtil.getFloat(conditionValue);

                case ABOVE_OR_BELOW ->
                        CommonUtil.getFloat(operandValue) > CommonUtil.getFloat(CommonUtil.getString(conditionValue).split("or")[0]) || CommonUtil.getFloat(operandValue) < CommonUtil.getFloat(CommonUtil.getString(conditionValue).split("or")[1]);

                case CONTAINS -> value.contains(CommonUtil.getString(conditionValue).toLowerCase());

                case NOT_CONTAINS -> !value.contains(CommonUtil.getString(conditionValue).toLowerCase());

                case IN ->
                {
                    var entries = (JsonArray) conditionValue;

                    for (var index = 0; index < entries.size(); index++)
                    {
                        if (entries.getString(index).trim().toLowerCase().equalsIgnoreCase(value))
                        {
                            yield true;
                        }
                    }

                    yield false;
                }

                case NOT_IN ->
                {
                    var entries = (JsonArray) conditionValue;

                    for (var index = 0; index < entries.size(); index++)
                    {
                        if (entries.getString(index).trim().toLowerCase().equalsIgnoreCase(value))
                        {
                            yield false;
                        }
                    }

                    yield true;
                }

                case START_WITH -> value.startsWith(CommonUtil.getString(conditionValue).toLowerCase());

                case END_WITH -> value.endsWith(CommonUtil.getString(conditionValue).toLowerCase());

                case RANGE ->
                {
                    var values = (JsonArray) conditionValue;

                    yield values.size() == 2 && NumberUtils.isParsable(values.getString(0)) && NumberUtils.isParsable(values.getString(1)) &&
                            CommonUtil.getFloat(operandValue) >= CommonUtil.getFloat(values.getString(0)) && CommonUtil.getFloat(operandValue) <= CommonUtil.getFloat(values.getString(1));

                }

                default -> Boolean.FALSE;
            };
        }

        return result;
    }

    /**
     * Inverts a condition operator to its logical opposite.
     * For example, "equal" becomes "not equal", "greater than" becomes "less than or equal".
     *
     * @param condition The condition operator to invert
     * @return The inverted condition operator
     */
    public static String invertCondition(String condition)
    {
        String result;

        result = switch (Operator.valueOfName(condition))
        {
            case EQUAL -> Operator.NOT_EQUAL.getName();

            case NOT_EQUAL -> Operator.EQUAL.getName();

            case GREATER_THAN -> Operator.LESS_THAN.getName();

            case LESS_THAN -> Operator.GREATER_THAN.getName();

            case GREATER_THAN_OR_EQUAL -> Operator.LESS_THAN_OR_EQUAL.getName();

            case LESS_THAN_OR_EQUAL -> Operator.GREATER_THAN_OR_EQUAL.getName();

            case CONTAINS -> Operator.NOT_CONTAINS.getName();

            case NOT_CONTAINS -> Operator.CONTAINS.getName();

            case START_WITH -> Operator.END_WITH.getName();

            case END_WITH -> Operator.START_WITH.getName();

            case IN -> Operator.NOT_IN.getName();

            case NOT_IN -> Operator.IN.getName();

            default -> null;
        };

        return result;
    }

    /**
     * Replaces placeholders in metric policy messages with actual values from the context.
     * This allows for dynamic, context-aware notification messages.
     *
     * @param policy   The policy configuration
     * @param context  The context containing values to substitute for placeholders
     * @param template The template name
     * @param message  The message template with placeholders
     * @return The message with placeholders replaced by actual values
     */
    public static String replaceMetricPolicyPlaceholders(JsonObject policy, JsonObject context, String template, String message)
    {
        try
        {
            if (!template.isEmpty())
            {
                var object = ObjectConfigStore.getStore().getItem(context.getLong(ENTITY_ID));

                var key = context.getLong(ENTITY_ID) + SEPARATOR + context.getLong(ID) + SEPARATOR + (context.getValue(INSTANCE, null) != null && !CommonUtil.getString(context.getValue(INSTANCE)).isEmpty() ? context.getString(GlobalConstants.METRIC) + SEPARATOR + context.getValue(INSTANCE) : context.getString(GlobalConstants.METRIC));

                var timestamp = MetricPolicyFlapDurationCacheStore.getStore().getFlapTick(key);

                var groups = new JsonArray();

                var builder = new StringBuilder();

                var objectGroups = object.getJsonArray(AIOpsObject.OBJECT_GROUPS);

                for (var i = 0; i < objectGroups.size(); i++)
                {
                    builder.setLength(0);

                    CommonUtil.normalizeGroupHierarchy(GroupConfigStore.getStore().getItem(objectGroups.getLong(i)), builder, null);

                    groups.add(builder.toString());
                }

                var severity = context.getString(SEVERITY);

                for (var placeholder : PLACEHOLDERS)
                {
                    if (placeholder.equalsIgnoreCase("$$$object.name$$$"))
                    {
                        template = template.replace(placeholder, object.getString(AIOpsObject.OBJECT_NAME));
                    }
                    else if (object.getString(AIOpsObject.OBJECT_HOST) != null && placeholder.equalsIgnoreCase("$$$object.host$$$"))
                    {
                        template = template.replace(placeholder, object.getString(AIOpsObject.OBJECT_HOST));
                    }
                    else if (object.getString(AIOpsObject.OBJECT_IP) != null && placeholder.equalsIgnoreCase("$$$object.ip$$$"))
                    {
                        template = template.replace(placeholder, object.getString(AIOpsObject.OBJECT_IP));
                    }
                    else if (object.getString(AIOpsObject.OBJECT_TYPE) != null && placeholder.equalsIgnoreCase("$$$object.type$$$"))
                    {
                        template = template.replace(placeholder, object.getString(AIOpsObject.OBJECT_TYPE));
                    }
                    else if (object.getString(AIOpsObject.OBJECT_TYPE) != null && placeholder.equalsIgnoreCase("$$$object.groups$$$"))
                    {
                        template = template.replace(placeholder, StringUtils.join(groups, COMMA_SEPARATOR));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$severity$$$"))
                    {
                        template = template.replace(placeholder, severity.toLowerCase(Locale.ROOT));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$counter$$$"))
                    {
                        template = template.replace(placeholder, context.getString(GlobalConstants.METRIC));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$policy.name$$$"))
                    {
                        template = template.replace(placeholder, policy.getString(POLICY_NAME));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$policy.message$$$"))
                    {
                        template = template.replace(placeholder, message);
                    }
                    else if (placeholder.equalsIgnoreCase("$$$policy.type$$$"))
                    {
                        template = template.replace(placeholder, policy.getString(POLICY_TYPE));
                    }
                    else if (context.getString(INSTANCE) != null && placeholder.equalsIgnoreCase("$$$instance$$$"))
                    {
                        template = template.replace(placeholder, context.getString(INSTANCE));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$policy.trigger.time$$$"))
                    {
                        template = template.replace(placeholder, DateTimeUtil.timestamp(context.getLong(EventBusConstants.EVENT_TIMESTAMP) * 1000L));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$value$$$"))
                    {
                        template = template.replace(placeholder, CommonUtil.getString(context.getValue(VALUE)));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$active.since$$$"))
                    {
                        template = timestamp > 0 ? template.replace(placeholder, DateTimeUtil.getTimeStampFromEpochTimestamp(timestamp, true)) : template.replace(placeholder, EMPTY_VALUE);
                    }
                    else if (placeholder.equalsIgnoreCase("$$$trigger.condition$$$"))
                    {
                        if (!policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyEngineConstants.PolicyType.AVAILABILITY.getName()) && !severity.equalsIgnoreCase(Severity.CLEAR.name()))
                        {
                            var severityContext = policy.getJsonObject(POLICY_CONTEXT).getJsonObject(POLICY_SEVERITY).getJsonObject(context.getString(SEVERITY));

                            var operatorLiteral = PolicyEngineConstants.toOperatorLiteral(severityContext.getString(POLICY_CONDITION));

                            if (operatorLiteral != null)
                            {

                                template = template.replace(placeholder, String.format("metric value %s %s", operatorLiteral.toLowerCase(), severityContext.getString(POLICY_THRESHOLD)));
                            }
                        }
                        else
                        {
                            template = template.replace(placeholder, EMPTY_VALUE);
                        }
                    }
                    else
                    {
                        template = template.replace(placeholder, EMPTY_VALUE);
                    }
                }

                if (context.containsKey(INSTANCE_METRIC_CONTEXT) && context.getJsonObject(INSTANCE_METRIC_CONTEXT) != null)
                {
                    for (var metric : context.getJsonObject(INSTANCE_METRIC_CONTEXT))
                    {

                        if (metric.getKey().contains("~"))
                        { // this condition will ensure that only instance metrics qualify as placeholders and metric key specified with $$$ as prefix and suffix get replaced only.

                            var placeholder = "$$$" + metric.getKey().replace("~", ".") + "$$$";

                            template = template.contains(placeholder) ? template.replace(placeholder, metric.getValue().toString()) : template;
                        }
                    }
                }

                message = template;
            }

        }
        catch (Exception exception)
        {
//            logger.error(exception);
        }

        return message;
    }

    /**
     * Replaces placeholders in event policy messages with actual values from the context.
     * Similar to replaceMetricPolicyPlaceholders but specifically for event policies.
     *
     * @param policy   The policy configuration
     * @param context  The context containing values to substitute for placeholders
     * @param template The template name
     * @param message  The message template with placeholders
     * @return The message with placeholders replaced by actual values
     */
    public static String replaceEventPolicyPlaceholders(JsonObject policy, JsonObject context, String template, String message)
    {
        try
        {
            if (!template.isEmpty())
            {
                var severity = context.getString(SEVERITY);

                for (var placeholder : PLACEHOLDERS)
                {
                    if (placeholder.equalsIgnoreCase("$$$severity$$$") && severity != null)
                    {
                        template = template.replace(placeholder, severity.toLowerCase(Locale.ROOT));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$event.field$$$") && context.containsKey(EVENT_FIELD))
                    {
                        template = template.replace(placeholder, context.getString(EVENT_FIELD));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$policy.name$$$") && policy.containsKey(POLICY_NAME))
                    {
                        template = template.replace(placeholder, policy.getString(POLICY_NAME));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$policy.message$$$"))
                    {
                        template = template.replace(placeholder, message);
                    }
                    else if (placeholder.equalsIgnoreCase("$$$policy.type$$$") && policy.containsKey(POLICY_TYPE))
                    {
                        template = template.replace(placeholder, policy.getString(POLICY_TYPE));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$policy.trigger.time$$$") && context.containsKey(EventBusConstants.EVENT_TIMESTAMP))
                    {
                        template = template.replace(placeholder, DateTimeUtil.timestamp(context.getLong(EventBusConstants.EVENT_TIMESTAMP) * 1000L));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$value$$$") && context.containsKey(VALUE))
                    {
                        template = template.replace(placeholder, CommonUtil.getString(context.getValue(EventPolicy.TRIGGERED_VALUE)));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$aggregator$$$"))
                    {
                        template = template.replace(placeholder, policy.getJsonObject(POLICY_CONTEXT) != null && policy.getJsonObject(POLICY_CONTEXT).containsKey("aggregator") ? policy.getJsonObject(POLICY_CONTEXT).getString("aggregator") : EMPTY_VALUE);
                    }
                    else if (placeholder.equalsIgnoreCase("$$$condition$$$"))
                    {
                        template = template.replace(placeholder, policy.getJsonObject(POLICY_CONTEXT) != null && policy.getJsonObject(POLICY_CONTEXT).containsKey("data.point") ? policy.getJsonObject(POLICY_CONTEXT).getString("data.point") : EMPTY_VALUE);
                    }
                    else if (placeholder.equalsIgnoreCase("$$$operator$$$"))
                    {
                        if (policy.getJsonObject(POLICY_CONTEXT) != null && policy.getJsonObject(POLICY_CONTEXT).containsKey("operator"))
                        {
                            var operatorLiteral = PolicyEngineConstants.toOperatorLiteral(policy.getJsonObject(POLICY_CONTEXT).getString("operator"));

                            template = template.replace(placeholder, operatorLiteral != null ? operatorLiteral.toLowerCase() : EMPTY_VALUE);
                        }

                    }
                    else if (placeholder.equalsIgnoreCase("$$$trigger.condition$$$") && policy.getJsonObject(POLICY_CONTEXT) != null)
                    {
                        if (!severity.equalsIgnoreCase(Severity.CLEAR.name()))
                        {
                            if (policy.getJsonObject(POLICY_CONTEXT).containsKey("operator"))
                            {
                                var operator = PolicyEngineConstants.toOperatorLiteral(policy.getJsonObject(POLICY_CONTEXT).getString("operator"));

                                template = template.replace(placeholder, String.format("metric value %s %s", operator != null ? operator.toLowerCase() : EMPTY_VALUE, CommonUtil.getString(context.getValue(EventPolicy.TRIGGERED_VALUE))));
                            }
                        }
                        else
                        {
                            template = template.replace(placeholder, EMPTY_VALUE);
                        }
                    }
                    else
                    {
                        template = template.replace(placeholder, EMPTY_VALUE);
                    }
                }

                message = template;
            }

        }
        catch (Exception exception)
        {
//            logger.error(exception);
        }

        return message;
    }

    /**
     * Replaces placeholders in trap policy messages with actual values from the context and event.
     * Specifically designed for SNMP trap policies.
     *
     * @param policy   The policy configuration
     * @param context  The context containing values to substitute for placeholders
     * @param template The template name
     * @param message  The message template with placeholders
     * @param event    The trap event containing additional values for substitution
     * @return The message with placeholders replaced by actual values
     */
    public static String replaceTrapPolicyPlaceholders(JsonObject policy, JsonObject context, String template, String message, JsonObject event)
    {
        try
        {
            if (!template.isEmpty())
            {
                var severity = context.getString(SEVERITY);

                for (var placeholder : PLACEHOLDERS)
                {
                    if (placeholder.equalsIgnoreCase("$$$severity$$$") && severity != null)
                    {
                        template = template.replace(placeholder, severity.toLowerCase(Locale.ROOT));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$policy.name$$$") && policy.containsKey(POLICY_NAME))
                    {
                        template = template.replace(placeholder, policy.getString(POLICY_NAME));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$policy.message$$$"))
                    {
                        template = template.replace(placeholder, message);
                    }
                    else if (placeholder.equalsIgnoreCase("$$$policy.type$$$") && policy.containsKey(POLICY_TYPE))
                    {
                        template = template.replace(placeholder, policy.getString(POLICY_TYPE));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$policy.trigger.time$$$") && context.containsKey(EventBusConstants.EVENT_TIMESTAMP))
                    {
                        template = template.replace(placeholder, DateTimeUtil.timestamp(context.getLong(EventBusConstants.EVENT_TIMESTAMP) * 1000L));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$value$$$") && context.containsKey(VALUE))
                    {
                        template = template.replace(placeholder, CommonUtil.getString(context.getValue(EventPolicy.TRIGGERED_VALUE)));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$condition$$$"))
                    {
                        template = template.replace(placeholder, policy.getJsonObject(POLICY_CONTEXT) != null && policy.getJsonObject(POLICY_CONTEXT).containsKey("data.point") ? policy.getJsonObject(POLICY_CONTEXT).getString("data.point") : EMPTY_VALUE);
                    }
                    else if (placeholder.equalsIgnoreCase("$$$operator$$$"))
                    {

                        if (policy.getJsonObject(POLICY_CONTEXT) != null && policy.getJsonObject(POLICY_CONTEXT).containsKey("operator"))
                        {

                            var operatorLiteral = PolicyEngineConstants.toOperatorLiteral(policy.getJsonObject(POLICY_CONTEXT).getString("operator"));

                            template = template.replace(placeholder, operatorLiteral != null ? operatorLiteral.toLowerCase() : EMPTY_VALUE);
                        }
                        else
                        {
                            template = template.replace(placeholder, EMPTY_VALUE);
                        }

                    }
                    else if (placeholder.equalsIgnoreCase("$$$event.source$$$"))
                    {
                        template = template.replace(placeholder, event.getString(EVENT_SOURCE));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$trap.oid$$$"))
                    {
                        template = template.replace(placeholder, event.getString(SNMPTrapProcessor.SNMP_TRAP_OID));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$translated.message$$$"))
                    {
                        template = template.replace(placeholder, event.getString(SNMPTrapProcessor.SNMP_TRAP_MESSAGE));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$original.message$$$"))
                    {
                        template = template.replace(placeholder, event.getString(SNMPTrapProcessor.SNMP_TRAP_RAW_MESSAGE));
                    }
                    else
                    {
                        template = template.replace(placeholder, EMPTY_VALUE);
                    }
                }

                message = template;
            }

        }
        catch (Exception exception)
        {
            //logger.error(exception);
        }

        return message;
    }

    /**
     * Replaces placeholders in network route policy messages with actual values.
     * Specifically designed for network route policies with additional logging capabilities.
     *
     * @param policy   The policy configuration
     * @param context  The context containing values to substitute for placeholders
     * @param template The template name
     * @param message  The message template with placeholders
     * @param event    The network route event containing additional values for substitution
     * @param logger   Logger instance for recording any issues during placeholder replacement
     * @return The message with placeholders replaced by actual values
     */
    public static String replaceNetRoutePolicyPlaceholders(JsonObject policy, JsonObject context, String template, String message, JsonObject event, Logger logger)
    {
        try
        {
            if (!template.isEmpty())
            {
                var severity = context.getString(SEVERITY);

                for (var placeholder : PLACEHOLDERS)
                {
                    if (placeholder.equalsIgnoreCase("$$$severity$$$") && severity != null)
                    {
                        template = template.replace(placeholder, severity.toLowerCase(Locale.ROOT));
                    }
                    if (placeholder.equalsIgnoreCase("$$$counter$$$") && severity != null)
                    {
                        template = template.replace(placeholder, context.getString(METRIC));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$policy.name$$$") && policy.containsKey(POLICY_NAME))
                    {
                        template = template.replace(placeholder, policy.getString(POLICY_NAME));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$policy.message$$$"))
                    {
                        template = template.replace(placeholder, message);
                    }
                    else if (placeholder.equalsIgnoreCase("$$$policy.type$$$") && policy.containsKey(POLICY_TYPE))
                    {
                        template = template.replace(placeholder, policy.getString(POLICY_TYPE));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$policy.threshold$$$") && context.containsKey(POLICY_THRESHOLD))
                    {
                        template = template.replace(placeholder, context.getString(POLICY_THRESHOLD));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$policy.trigger.time$$$") && context.containsKey(EventBusConstants.EVENT_TIMESTAMP))
                    {
                        template = template.replace(placeholder, DateTimeUtil.timestamp(context.getLong(EventBusConstants.EVENT_TIMESTAMP) * 1000L));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$value$$$") && context.containsKey(VALUE))
                    {
                        template = template.replace(placeholder, CommonUtil.getString(context.getValue(VALUE)));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$condition$$$"))
                    {
                        template = template.replace(placeholder, policy.getJsonObject(POLICY_CONTEXT) != null && policy.getJsonObject(POLICY_CONTEXT).containsKey("data.point") ? policy.getJsonObject(POLICY_CONTEXT).getString("data.point") : EMPTY_VALUE);
                    }
                    else if (placeholder.equalsIgnoreCase("$$$policy.condition$$$") && context.containsKey(POLICY_CONDITION))
                    {
                        template = template.replace(placeholder, PolicyEngineConstants.toOperatorLiteral(context.getString(POLICY_CONDITION)));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$netroute.name$$$"))
                    {
                        template = template.replace(placeholder, event.getString(NetRoute.NETROUTE_NAME));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$netroute.source$$$"))
                    {
                        template = template.replace(placeholder, event.getString(NetRoute.NETROUTE_SOURCE));
                    }
                    else if (placeholder.equalsIgnoreCase("$$$netroute.destination$$$"))
                    {
                        template = template.replace(placeholder, event.getString(NetRoute.NETROUTE_DESTINATION));
                    }
                    else
                    {
                        template = template.replace(placeholder, EMPTY_VALUE);
                    }
                }

                message = template;
            }
        }
        catch (Exception exception)
        {
            logger.error(exception);
        }

        return message;
    }

    /**
     * Converts an operator code to its human-readable literal form.
     * This method translates internal operator representations to user-friendly text
     * that can be displayed in notifications and UI elements.
     *
     * @param operator The operator code to convert
     * @return The human-readable literal form of the operator
     */
    public static String toOperatorLiteral(String operator)
    {
        operator = switch (Operator.valueOfName(operator))
        {
            case EQUAL -> OperatorLiteral.EQUAL.getName();

            case NOT_EQUAL -> OperatorLiteral.NOT_EQUAL.getName();

            case GREATER_THAN -> OperatorLiteral.GREATER_THAN.getName();

            case LESS_THAN -> OperatorLiteral.LESS_THAN.getName();

            case GREATER_THAN_OR_EQUAL -> OperatorLiteral.GREATER_THAN_OR_EQUAL.getName();

            case LESS_THAN_OR_EQUAL -> OperatorLiteral.LESS_THAN_OR_EQUAL.getName();

            case CONTAINS -> OperatorLiteral.CONTAINS.getName();

            case NOT_CONTAINS -> OperatorLiteral.NOT_CONTAINS.getName();

            case START_WITH -> OperatorLiteral.START_WITH.getName();

            case END_WITH -> OperatorLiteral.END_WITH.getName();

            case IN -> OperatorLiteral.IN.getName();

            case NOT_IN -> OperatorLiteral.NOT_IN.getName();

            case RANGE -> OperatorLiteral.BETWEEN.getName();

            default -> null;
        };

        return operator;
    }

    /**
     * Processes policy state changes (flaps) and triggers appropriate actions.
     * This method handles the core logic of policy state transitions and manages
     * the lifecycle of policy alerts.
     *
     * @param context             The context containing policy state information
     * @param policy              The policy configuration
     * @param autoClear           Flag indicating if this is an automatic clearing operation
     * @param triggeredPolicies   Map of currently triggered policies
     * @param policyTimerContexts Map of policy timer contexts for managing timeouts
     * @param logger              Logger instance for recording the policy flap process
     * @param mappers             Set of mappers used for policy evaluation
     * @param builder             StringBuilder for constructing messages
     */
    public static void processPolicyFlap(JsonObject context, JsonObject policy, boolean autoClear, Map<String, JsonObject> triggeredPolicies, Map<String, JsonObject> policyTimerContexts, Logger logger, Set<String> mappers, StringBuilder builder)
    {
        var policyKey = context.getString(POLICY_KEY);

        var entityId = context.getLong(ENTITY_ID);

        var severity = context.getString(SEVERITY);

        var trigger = false;

        var clearSeverity = severity.equalsIgnoreCase(Severity.CLEAR.name());

        var policyContext = policy.getJsonObject(POLICY_CONTEXT);

        var previousSeverity = EMPTY_VALUE;

        try
        {
            if (triggeredPolicies.containsKey(policyKey))
            {
                var triggeredContext = triggeredPolicies.get(policyKey);

                if (!triggeredContext.getString(SEVERITY).equalsIgnoreCase(severity))
                {
                    trigger = true;
                }

                previousSeverity = triggeredContext.getString(SEVERITY);

                context = triggeredContext.mergeIn(context);
            }
            else
            {
                //if clear severity and first time entry so no need to send notification
                if (!clearSeverity)
                {
                    trigger = true;
                }
            }

            updatePolicyTriggerDuration(context);

            MetricPolicyCacheStore.getStore().updateItem(context.getString(POLICY_KEY).hashCode(), context);

            context.remove(CORRELATED_UNREACHABLE_OBJECTS);

            context.remove(CORRELATION_OBJECTS);

            triggeredPolicies.put(policyKey, context);

            var renotificationEmailRecipients = new HashSet<String>();

            var renotificationSMSRecipients = new HashSet<String>();

            var renotificationChannelRecipients = new HashMap<String, Set<String>>();

            if (severity.equalsIgnoreCase(Severity.UNREACHABLE.name()))
            {
                policyTimerContexts.remove(policyKey);
            }
            else
            {
                updateAutoClearTimer(clearSeverity, policyKey, policyContext.containsKey(POLICY_AUTO_CLEAR_TIMER_SECONDS) ? policyContext.getInteger(POLICY_AUTO_CLEAR_TIMER_SECONDS) : 0, context, policyTimerContexts);

                if (policy.containsKey(POLICY_ACTIONS) && !policy.getJsonObject(POLICY_ACTIONS).isEmpty()
                        && policy.getJsonObject(POLICY_ACTIONS).containsKey(PolicyTriggerActionType.RENOTIFICATION.getName())
                        && !policy.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.RENOTIFICATION.getName()).isEmpty()
                        && policy.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.RENOTIFICATION.getName()).containsKey(severity)
                        && !policy.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.RENOTIFICATION.getName()).getJsonObject(severity).isEmpty())
                {
                    updateRenotificationTimer(severity, policyKey, policyTimerContexts, policy, renotificationEmailRecipients, renotificationSMSRecipients, renotificationChannelRecipients);
                }
            }

            var object = ObjectConfigStore.getStore().getItem(entityId);

            var message = "";

            var objectName = object.getString(AIOpsObject.OBJECT_NAME);

            if (!severity.equalsIgnoreCase(Severity.UNREACHABLE.name()))
            {
                if (policy.getString(POLICY_TYPE).equals(PolicyType.AVAILABILITY.getName()))
                {
                    //some device types such as azure cloud, ofice 365,etc don;t require deprovisioning
                    if (!object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.EXCHANGE_ONLINE.getName()) && !object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.ONEDRIVE.getName()) && !object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.MICROSOFT_TEAMS.getName()) && !object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.OFFICE_365.getName()) && !object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.AWS_CLOUD.getName()) && !object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.AZURE_CLOUD.getName()))
                    {
                        updateUnprovisionTimer(policyKey, policy, clearSeverity, policy.getInteger(POLICY_UNPROVISION_OBJECT_TIMER_SECONDS, 0), policyTimerContexts);
                    }

                    message = String.format(InfoMessageConstants.AVAILABILITY_POLICY_DEFAULT_MESSAGE, objectName, context.getString(INSTANCE) != null ? context.getString(INSTANCE) : EMPTY_VALUE, context.getString(VALUE).equalsIgnoreCase(STATUS_UP) ? "Available" : "Unavailable");
                }

                else
                {
                    if (autoClear)
                    {
                        if (policyTimerContexts.containsKey(policyKey) && policyTimerContexts.get(policyKey).containsKey(POLICY_AUTO_CLEAR_TIMER_SECONDS))
                        {
                            policyTimerContexts.get(policyKey).remove(POLICY_AUTO_CLEAR_TIMER_SECONDS);
                        }

                        else
                        {
                            policyTimerContexts.remove(policyKey);
                        }

                        message = String.format(InfoMessageConstants.METRIC_THRESHOLD_POLICY_AUTO_CLEAR_MESSAGE, context.getString(GlobalConstants.METRIC), context.getString(INSTANCE) != null ? context.getString(INSTANCE) : EMPTY_VALUE, objectName);
                    }


                    else
                    {
                        if (policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.ANOMALY.getName()))
                        {
                            message = String.format(InfoMessageConstants.METRIC_AIOPS_ANOMALY_POLICY_DEFAULT_MESSAGE, context.getString(GlobalConstants.METRIC), context.getString(INSTANCE) != null ? context.getString(INSTANCE) : EMPTY_VALUE, objectName, severity, StringEscapeUtils.escapeJavaScript(CommonUtil.getString(context.getValue(VALUE))), severity.equalsIgnoreCase(Severity.CLEAR.name()) ? " within range of " : (policyContext.getJsonObject(POLICY_SEVERITY).getJsonObject(severity) != null ? policyContext.getJsonObject(POLICY_SEVERITY).getJsonObject(severity).getString(POLICY_CONDITION) : EMPTY_VALUE));
                        }

                        else if (policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.FORECAST.getName()))
                        {
                            if (severity.equalsIgnoreCase(Severity.CLEAR.name()))
                            {
                                message = String.format(InfoMessageConstants.METRIC_AIOPS_FORECAST_POLICY_CLEAR_MESSAGE, context.getString(GlobalConstants.METRIC), context.getString(INSTANCE) != null ? context.getString(INSTANCE) : EMPTY_VALUE, severity, context.getString(POLICY_THRESHOLD) + "%");
                            }
                            else
                            {
                                message = String.format(InfoMessageConstants.METRIC_AIOPS_FORECAST_POLICY_TRIGGER_MESSAGE, context.getString(GlobalConstants.METRIC), context.getString(INSTANCE) != null ? context.getString(INSTANCE) : EMPTY_VALUE, severity, context.getString(POLICY_THRESHOLD) + "%", StringEscapeUtils.escapeJavaScript(CommonUtil.getString(context.getValue(VALUE))));
                            }
                        }

                        else if (policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.BASELINE.getName()))
                        {
                            message = String.format(InfoMessageConstants.METRIC_AIOPS_BASELINE_POLICY_DEFAULT_MESSAGE, context.getString(GlobalConstants.METRIC), context.getString(INSTANCE) != null ? context.getString(INSTANCE) : EMPTY_VALUE, objectName, severity, StringEscapeUtils.escapeJavaScript(CommonUtil.getString(context.getValue(VALUE))), severity.equalsIgnoreCase(Severity.CLEAR.name()) ? " falls below " : " is " + (policyContext.getJsonObject(POLICY_SEVERITY).getJsonObject(severity) != null ? policyContext.getJsonObject(POLICY_SEVERITY).getJsonObject(severity).getString(POLICY_CONDITION) : EMPTY_VALUE), context.getValue(POLICY_THRESHOLD));
                        }

                        else
                        {
                            message = String.format(InfoMessageConstants.METRIC_THRESHOLD_POLICY_DEFAULT_MESSAGE, context.getString(GlobalConstants.METRIC), context.getString(INSTANCE) != null ? context.getString(INSTANCE) : EMPTY_VALUE, objectName, policyContext.getJsonObject(POLICY_SEVERITY).getJsonObject(severity) != null ? policyContext.getJsonObject(POLICY_SEVERITY).getJsonObject(severity).getString(POLICY_CONDITION) : EMPTY_VALUE, policyContext.getJsonObject(POLICY_SEVERITY).getJsonObject(severity) != null ? policyContext.getJsonObject(POLICY_SEVERITY).getJsonObject(severity).getString(POLICY_THRESHOLD) : EMPTY_VALUE, policyContext.getInteger(POLICY_TRIGGER_TIME) / 60, policyContext.getInteger(POLICY_TRIGGER_OCCURRENCES));
                        }
                    }
                }
            }

            else
            {
                message = String.format(InfoMessageConstants.POLICY_UNKNOWN_DEFAULT_MESSAGE, context.getString(GlobalConstants.METRIC), context.getString(INSTANCE) != null ? context.getString(INSTANCE) : EMPTY_VALUE, objectName, severity, context.getString(REASON));
            }

            /*DatastoreConstants.write(new JsonObject()
                    .put(EventBusConstants.EVENT_SOURCE, object.getString(AIOpsObject.OBJECT_IP, AIOpsObject.OBJECT_TARGET)).put(SEVERITY, severity).put(POLICY_TYPE, context.getString(POLICY_TYPE))
                    .put(MESSAGE, StringEscapeUtils.escapeJavaScript(message)).put(AIOpsObject.OBJECT_ID, object.getInteger(AIOpsObject.OBJECT_ID))
                    .put(AIOpsObject.OBJECT_CATEGORY, context.getString(AIOpsObject.OBJECT_CATEGORY)).put(POLICY_ID, policy.getLong(ID)).put(GlobalConstants.METRIC, context.getString(GlobalConstants.METRIC))
                    .put(VALUE, StringEscapeUtils.escapeJavaScript(CommonUtil.getString(context.getValue(VALUE))))
                    .put(INSTANCE, context.getString(INSTANCE, EMPTY_VALUE))
                    .put(POLICY_THRESHOLD, CommonUtil.getString(context.getValue(POLICY_THRESHOLD, EMPTY_VALUE)))
                    .put(GlobalConstants.PLUGIN_ID, DatastoreConstants.PluginId.POLICY_METRIC.getName())
                    .put(EventBusConstants.EVENT_TIMESTAMP, context.getLong(EVENT_TIMESTAMP))
                    .put(DatastoreConstants.DATASTORE_TYPE, DatastoreConstants.DatastoreType.METRIC_POLICY.ordinal()), VisualizationConstants.VisualizationDataSource.POLICY.getName(), mappers, builder);*/

            // if previous severity unreachable then we are not triggering action for it
            if (previousSeverity.equalsIgnoreCase(EMPTY_VALUE) || !previousSeverity.equalsIgnoreCase(Severity.UNREACHABLE.name()))
            {
                triggerAction(severity, trigger, policy, context.put(AIOpsObject.OBJECT_ID, object.getInteger(AIOpsObject.OBJECT_ID)).put(AIOpsObject.OBJECT_NAME, objectName).put(AIOpsObject.OBJECT_TYPE, object.getValue(AIOpsObject.OBJECT_TYPE)).put(AIOpsObject.OBJECT_IP, object.getString(AIOpsObject.OBJECT_IP, AIOpsObject.OBJECT_TARGET)), renotificationEmailRecipients, renotificationSMSRecipients, renotificationChannelRecipients, entityId, message, logger, mappers, builder);
            }
            else
            {
                logger.info(String.format("Not triggering action for %s ", objectName));
            }
        }

        catch (Exception exception)
        {
            logger.error(exception);
        }
    }

    /**
     * Updates the duration tracking for a policy trigger.
     * This method sends a message to the duration calculator to update
     * the time that a policy has been in its current state.
     *
     * @param context The context containing policy state information
     */
    public static void updatePolicyTriggerDuration(JsonObject context)
    {
        Bootstrap.vertx().eventBus().send(EVENT_METRIC_POLICY_SEVERITY_DURATION_CALCULATE, context);
    }

    /**
     * Triggers policy actions based on the policy configuration and current context.
     * This method handles notifications, runbooks, and other actions defined in the policy.
     *
     * @param severity                        The severity level of the triggered policy
     * @param trigger                         Flag indicating if actions should be triggered
     * @param policy                          The policy configuration
     * @param context                         The context containing policy state information
     * @param renotificationEmailRecipients   Set of email recipients for renotification
     * @param renotificationSMSRecipients     Set of SMS recipients for renotification
     * @param renotificationChannelRecipients Map of channel recipients for renotification
     * @param entityId                        ID of the entity associated with the policy
     * @param message                         The notification message
     * @param logger                          Logger instance for recording the action triggering process
     * @param mappers                         Set of mappers used for policy evaluation
     * @param builder                         StringBuilder for constructing messages
     */
    private static void triggerAction(String severity, boolean trigger, JsonObject policy, JsonObject context, Set<String> renotificationEmailRecipients, Set<String> renotificationSMSRecipients, Map<String, Set<String>> renotificationChannelRecipients, long entityId, String message, Logger logger, Set<String> mappers, StringBuilder builder)
    {

        var actions = policy.getJsonObject(POLICY_ACTIONS);

        //as unreachable its for parent down resource severity and none no threshold qualified and unknown as node is down all its metric will be unknown so no notification should be triggered for it
        if (trigger && !severity.equalsIgnoreCase(Severity.UNREACHABLE.name()))
        {
            // notification
            if (actions != null && actions.containsKey(PolicyTriggerActionType.NOTIFICATION.getName())
                    && !actions.getJsonObject(PolicyTriggerActionType.NOTIFICATION.getName()).isEmpty())
            {
                try
                {
                    context.put(POLICY_URL, String.format(POLICY_URL_VALUE, Base64.getEncoder().encodeToString(new JsonObject().put(POLICY_DRILL_DOWN_TEMPLATE, YES).put(PolicyEngineConstants.POLICY_ID, context.getString(PolicyEngineConstants.POLICY_ID, EMPTY_VALUE)).put(ENTITY_ID, context.getString(ENTITY_ID, EMPTY_VALUE)).put(INSTANCE, context.getString(INSTANCE, EMPTY_VALUE)).put(METRIC, context.getString(METRIC, EMPTY_VALUE)).put(POLICY_TYPE, policy.getString(POLICY_TYPE, EMPTY_VALUE)).encode().getBytes())));

                    var notificationContext = actions.getJsonObject(PolicyTriggerActionType.NOTIFICATION.getName());

                    // email & sms
                    if (notificationContext.containsKey(Notification.NotificationType.EMAIL.getName())
                            && !notificationContext.getJsonObject(Notification.NotificationType.EMAIL.getName()).isEmpty()
                            && notificationContext.getJsonObject(Notification.NotificationType.EMAIL.getName()).containsKey(severity)
                            && !notificationContext.getJsonObject(Notification.NotificationType.EMAIL.getName()).getJsonArray(severity).isEmpty())
                    {
                        var emailRecipients = new HashSet<String>();

                        var smsRecipients = new HashSet<String>();

                        setRecipients(emailRecipients, smsRecipients, notificationContext.getJsonObject(Notification.NotificationType.EMAIL.getName()).getJsonArray(severity));

                        if (!emailRecipients.isEmpty())
                        {
                            var targets = new JsonArray(new ArrayList<>(emailRecipients));

                            var subject = replaceMetricPolicyPlaceholders(policy, context, policy.getString(POLICY_TITLE) != null && !policy.getString(POLICY_TITLE).trim().isEmpty() ? policy.getString(POLICY_TITLE) : InfoMessageConstants.POLICY_DEFAULT_EMAIL_SUBJECT, message);

                            // MOTADATA-3974
                            context.put(RunbookPlugin.RUNBOOK_PLUGIN_NOTIFICATION_EMAIL_RECIPIENTS, targets);

                            context.put(RunbookPlugin.RUNBOOK_PLUGIN_NOTIFICATION_EMAIL_SUBJECT, subject);

                            Future<Buffer> future;

                            context.put(POLICY_MESSAGE, replaceMetricPolicyPlaceholders(policy, context, policy.getString(POLICY_MESSAGE) != null && !policy.getString(POLICY_MESSAGE).isEmpty() ? policy.getString(POLICY_MESSAGE) : EMPTY_VALUE, message));

                            if (AIOPS_POLICY_TYPES.contains(policy.getString(POLICY_TYPE)))
                            {
                                future = templateEngine.render(new JsonObject().mergeIn(ObjectConfigStore.getStore().getItem(entityId)).mergeIn(context).put(TIME_STAMP, DateTimeUtil.timestamp(context.getLong(EVENT_TIMESTAMP) * 1000L)).put(POLICY_NAME, policy.getString(POLICY_NAME)).put(SEVERITY, context.getString(SEVERITY).toLowerCase()).mergeIn(context).put(POLICY_SEVERITY, policy.getJsonObject(POLICY_CONTEXT).getJsonObject(POLICY_SEVERITY)).put(THRESHOLD_TYPE, policy.getJsonObject(POLICY_CONTEXT).getString(THRESHOLD_TYPE)), policy.getJsonObject(POLICY_CONTEXT).containsKey(POLICY_EMAIL_TEMPLATE) ? DB_DIR + PATH_SEPARATOR + "email-templates" + PATH_SEPARATOR + policy.getJsonObject(POLICY_CONTEXT).getString(POLICY_EMAIL_TEMPLATE) : DB_DIR + PATH_SEPARATOR + "email-templates" + PATH_SEPARATOR + "aiops-policy-default-template");
                            }
                            else
                            {
                                future = Future.succeededFuture(Buffer.buffer(new StringSubstitutor(new JsonObject().mergeIn(ObjectConfigStore.getStore().getItem(entityId)).mergeIn(context).put(TIME_STAMP, DateTimeUtil.timestamp(context.getLong(EVENT_TIMESTAMP) * 1000L)).put(POLICY_NAME, policy.getString(POLICY_NAME)).put(SEVERITY, context.getString(SEVERITY).toLowerCase()).getMap()).replace(Notification.EMAIL_NOTIFICATION_METRIC_POLICY_HTML_TEMPLATE)));
                            }

                            future.onComplete(result ->
                            {
                                var content = future.result() != null ? future.result().toString() : EMPTY_VALUE;

                                if (future.failed() || content.isEmpty())
                                {
                                    content = new StringSubstitutor(new JsonObject().mergeIn(ObjectConfigStore.getStore().getItem(entityId)).mergeIn(context).put(TIME_STAMP, DateTimeUtil.timestamp(context.getLong(EVENT_TIMESTAMP) * 1000L)).put(POLICY_NAME, policy.getString(POLICY_NAME)).put(SEVERITY, context.getString(SEVERITY).toLowerCase()).getMap()).replace(Notification.EMAIL_NOTIFICATION_METRIC_POLICY_HTML_TEMPLATE);

                                    logger.debug(String.format("Failed to render template for policy having name %s. Failure reason %s", policy.getString(POLICY_NAME), result.cause().getMessage()));
                                }

                                if (MotadataConfigUtil.devMode())
                                {
                                    Notification.sendEmail(new JsonObject()
                                            .put(EventBusConstants.EVENT_REPLY, YES).put(NOTIFICATION_TYPE, Notification.NotificationType.EMAIL.getName())
                                            .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                                            .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add(context.getString(SEVERITY).toLowerCase() + ".png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                                            .put(Notification.EMAIL_NOTIFICATION_SUBJECT, subject)
                                            .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, targets)
                                            .put(Notification.EMAIL_NOTIFICATION_CONTENT, content.getBytes()));
                                }
                                else
                                {
                                    Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_NOTIFICATION, new JsonObject()
                                            .put(EventBusConstants.EVENT_REPLY, YES).put(NOTIFICATION_TYPE, Notification.NotificationType.EMAIL.getName())
                                            .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                                            .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add(context.getString(SEVERITY).toLowerCase() + ".png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                                            .put(Notification.EMAIL_NOTIFICATION_SUBJECT, subject)
                                            .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, targets)
                                            .put(Notification.EMAIL_NOTIFICATION_CONTENT, content.getBytes()), DELIVERY_OPTIONS, reply ->
                                    {
                                        try
                                        {
                                            if (reply.succeeded())
                                            {
                                                writeTriggeredActionEvent(targets, context, entityId, policy.getLong(ID), reply.result().body().put(RUNBOOK_WORKLOG_TYPE, PolicyTriggerActionType.NOTIFICATION.getName()), logger, mappers, builder);
                                            }
                                        }
                                        catch (Exception exception)
                                        {
                                            logger.error(exception);
                                        }
                                    });
                                }
                            });
                        }

                        if (!smsRecipients.isEmpty())
                        {
                            var targets = new JsonArray(new ArrayList<>(smsRecipients));

                            Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_REPLY, YES).put(NOTIFICATION_TYPE, Notification.NotificationType.SMS.getName()).put(Notification.SMS_NOTIFICATION_RECIPIENTS, targets)
                                    .put(Notification.SMS_NOTIFICATION_MESSAGE, replaceMetricPolicyPlaceholders(policy, context, policy.getString(POLICY_MESSAGE) != null && !policy.getString(POLICY_MESSAGE).trim().isEmpty() ? policy.getString(POLICY_MESSAGE) : InfoMessageConstants.POLICY_DEFAULT_SMS, message)), DELIVERY_OPTIONS, reply ->
                            {
                                try
                                {
                                    if (reply.succeeded())
                                    {
                                        writeTriggeredActionEvent(targets, context, entityId, policy.getLong(ID), reply.result().body().put(RUNBOOK_WORKLOG_TYPE, PolicyTriggerActionType.NOTIFICATION.getName()), logger, mappers, builder);
                                    }
                                }

                                catch (Exception exception)
                                {
                                    logger.error(exception);
                                }
                            });
                        }

                        if (MotadataConfigUtil.devMode() && !emailRecipients.isEmpty())
                        {
                            Notification.sendEmail(new JsonObject()
                                    .put(EventBusConstants.EVENT_REPLY, YES).put(NOTIFICATION_TYPE, Notification.NotificationType.EMAIL.getName())
                                    .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                                    .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add(severity.toLowerCase() + ".png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                                    .put(Notification.EMAIL_NOTIFICATION_SUBJECT, replaceMetricPolicyPlaceholders(policy, context, policy.getString(POLICY_TITLE) != null && !policy.getString(POLICY_TITLE).trim().isEmpty() ? policy.getString(POLICY_TITLE) : InfoMessageConstants.POLICY_DEFAULT_EMAIL_SUBJECT, message))
                                    .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, new JsonArray(new ArrayList<>(emailRecipients)))
                                    .put(Notification.EMAIL_NOTIFICATION_CONTENT, new StringSubstitutor(new JsonObject().mergeIn(ObjectConfigStore.getStore().getItem(entityId)).mergeIn(context).put(TIME_STAMP, DateTimeUtil.timestamp(context.getLong(EVENT_TIMESTAMP) * 1000L)).put(POLICY_NAME, policy.getString(POLICY_NAME)).put(SEVERITY, context.getString(SEVERITY).toLowerCase()).getMap()).replace(Notification.EMAIL_NOTIFICATION_METRIC_POLICY_HTML_TEMPLATE).getBytes(StandardCharsets.UTF_8)));

                        }
                    }

                    // sound
                    if (notificationContext.containsKey(Notification.NotificationType.SOUND.getName())
                            && !notificationContext.getJsonObject(Notification.NotificationType.SOUND.getName()).isEmpty()
                            && notificationContext.getJsonObject(Notification.NotificationType.SOUND.getName()).containsKey(severity))
                    {
                        Notification.playSound(new JsonObject().mergeIn(context));
                    }

                    if (notificationContext.containsKey(CHANNELS)
                            && !notificationContext.getJsonObject(CHANNELS).isEmpty()
                            && notificationContext.getJsonObject(CHANNELS).containsKey(severity)
                            && !notificationContext.getJsonObject(CHANNELS).getJsonArray(severity).isEmpty())
                    {
                        context.put(POLICY_MESSAGE, replaceMetricPolicyPlaceholders(policy, context, policy.getString(POLICY_MESSAGE) != null && !policy.getString(POLICY_MESSAGE).isEmpty() ? policy.getString(POLICY_MESSAGE) : EMPTY_VALUE, message));

                        var recipients = actions.getJsonObject(PolicyTriggerActionType.NOTIFICATION.getName()).getJsonObject(CHANNELS).
                                getJsonArray(context.getString(SEVERITY));

                        var channelRecipients = new HashMap<String, Set<String>>();

                        for (var index = 0; index < recipients.size(); index++)
                        {
                            var recipient = recipients.getJsonObject(index);

                            var item = IntegrationConfigStore.getStore().getItem(recipient.getLong(IntegrationProfile.INTEGRATION));

                            if (item != null)
                            {
                                // Groups the handle list based on the integration type and forwards it to the corresponding notification type.
                                channelRecipients.computeIfAbsent(item.getString(Integration.INTEGRATION_TYPE), key -> new HashSet<>())
                                        .add(recipient.getString(ID));
                            }
                        }

                        var runbook = new HashSet<String>();

                        if (actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()) != null && !actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).isEmpty()
                                && actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).containsKey(severity) && !actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).getJsonArray(severity).isEmpty())
                        {
                            var entries = actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).getJsonArray(severity);

                            for (var index = 0; index < entries.size(); index++)
                            {
                                if (RunbookPluginConfigStore.getStore().existItem(entries.getJsonObject(index).getLong(ID)))
                                {
                                    runbook.add(RunbookPluginConfigStore.getStore().getItem(entries.getJsonObject(index).getLong(ID)).getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME));
                                }
                            }
                        }

                        channelRecipients.forEach((channelType, targets) ->
                        {
                            if (!targets.isEmpty())
                            {
                                Notification.send(new JsonObject()
                                        .mergeIn(context)
                                        .put(NOTIFICATION_TYPE, channelType)
                                        .put(CHANNELS, new JsonArray(new ArrayList<>(targets)))
                                        .put(Notification.CHANNEL_NOTIFICATION_CONTENT, new StringSubstitutor(new JsonObject()
                                                .mergeIn(ObjectConfigStore.getStore().getItem(entityId))
                                                .mergeIn(context)
                                                .put(TIME_STAMP, DateTimeUtil.timestamp(context.getLong(EVENT_TIMESTAMP) * 1000L))
                                                .put(POLICY_NAME, policy.getString(POLICY_NAME))
                                                .put(SEVERITY, context.getString(SEVERITY).toLowerCase())
                                                .put("policy.action", String.join(", ", runbook))
                                                .getMap()).replace(Notification.getChannelNotificationTemplate(policy.getString(POLICY_TYPE), channelType, false))));
                            }
                        });

                    }
                }
                catch (Exception exception)
                {
                    logger.error(exception);
                }
            }

            // runbook
            severity = policy.getString(POLICY_TYPE).equalsIgnoreCase(PolicyType.AVAILABILITY.getName()) && context.containsKey(INSTANCE) && !context.getString(INSTANCE).isEmpty() ? (severity.equalsIgnoreCase(Severity.CRITICAL.name()) ? Severity.DOWN.name() : severity) : severity;//runbook

            if (actions != null && actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()) != null && !actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).isEmpty()
                    && actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).containsKey(severity) && !actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).getJsonArray(severity).isEmpty())
            {
                var entries = actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).getJsonArray(severity);

                for (var index = 0; index < entries.size(); index++)
                {
                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_RUNBOOK, prepareContext(entries.getJsonObject(index).getLong(ID), context, entityId, policy));
                }
            }

            // integration

            var integrationContext = new JsonObject().mergeIn(context).put(POLICY_ID, policy.getLong(ID)).put(MESSAGE, message);

            // will put integration profile id if present
            integrationContext.remove(ID);

            if (actions != null && actions.getJsonObject(PolicyTriggerActionType.INTEGRATION.getName()) != null && !actions.getJsonObject(PolicyTriggerActionType.INTEGRATION.getName()).isEmpty()
                    && actions.getJsonObject(PolicyTriggerActionType.INTEGRATION.getName()).containsKey(severity) && !actions.getJsonObject(PolicyTriggerActionType.INTEGRATION.getName()).getJsonArray(severity).isEmpty())
            {
                // in one severity at max one integration profile should be there.
                integrationContext.put(ID, actions.getJsonObject(PolicyTriggerActionType.INTEGRATION.getName()).getJsonArray(severity).getJsonObject(0).getLong(ID));
            }

            Bootstrap.vertx().eventBus().send(EVENT_INTEGRATION, integrationContext);
        }

        // renotification
        if (actions != null && (!renotificationEmailRecipients.isEmpty() || !renotificationSMSRecipients.isEmpty()))
        {
            var key = context.getLong(ENTITY_ID) + SEPARATOR + context.getLong(ID) + SEPARATOR + (context.getValue(INSTANCE, null) != null && !CommonUtil.getString(context.getValue(INSTANCE)).isEmpty() ? context.getString(GlobalConstants.METRIC) + SEPARATOR + context.getValue(INSTANCE) : context.getString(GlobalConstants.METRIC));

            var duration = MetricPolicyFlapDurationCacheStore.getStore().getFlapTick(key) != null ? DateTimeUtil.getTimeStampFromEpochTimestamp(MetricPolicyFlapDurationCacheStore.getStore().getFlapTick(key), true) : EMPTY_VALUE;

            var acknowledged = MetricPolicyFlapDurationCacheStore.getStore().getAcknowledgment(key) != null ? MetricPolicyFlapDurationCacheStore.getStore().getAcknowledgment(key) : NO;

            if (actions.containsKey(PolicyTriggerActionType.RENOTIFICATION.getName())
                    && (actions.getJsonObject(PolicyTriggerActionType.RENOTIFICATION.getName()).getString(RENOTIFY_ACKNOWLEDGED).equalsIgnoreCase(NO) || (actions.getJsonObject(PolicyTriggerActionType.RENOTIFICATION.getName()).getString(RENOTIFY_ACKNOWLEDGED).equalsIgnoreCase(YES) && acknowledged.equalsIgnoreCase(NO))))
            {
                var emailRecipients = new JsonArray(new ArrayList<>(renotificationEmailRecipients));

                var smsRecipients = new JsonArray(new ArrayList<>(renotificationSMSRecipients));

                if (!emailRecipients.isEmpty())
                {
                    context.put(POLICY_MESSAGE, replaceMetricPolicyPlaceholders(policy, context, POLICY_RENOTIFICATION_MESSAGE, message));

                    Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_NOTIFICATION, new JsonObject()
                            .put(EventBusConstants.EVENT_REPLY, YES).put(NOTIFICATION_TYPE, Notification.NotificationType.EMAIL.getName())
                            .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                            .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add(severity.toLowerCase() + ".png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                            .put(Notification.EMAIL_NOTIFICATION_SUBJECT, replaceMetricPolicyPlaceholders(policy, context, policy.getString(POLICY_TITLE) != null && !policy.getString(POLICY_TITLE).trim().isEmpty() ? policy.getString(POLICY_TITLE) : InfoMessageConstants.POLICY_DEFAULT_EMAIL_SUBJECT, message))
                            .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, emailRecipients)
                            .put(Notification.EMAIL_NOTIFICATION_CONTENT, new StringSubstitutor(new JsonObject().mergeIn(ObjectConfigStore.getStore().getItem(entityId)).mergeIn(context).put(ACTIVE_SINCE, duration).put(TIME_STAMP, DateTimeUtil.timestamp(context.getLong(EVENT_TIMESTAMP) * 1000L)).put(POLICY_NAME, policy.getString(POLICY_NAME)).put(SEVERITY, context.getString(SEVERITY).toLowerCase()).getMap()).replace(Notification.EMAIL_RENOTIFICATION_METRIC_POLICY_HTML_TEMPLATE).getBytes(StandardCharsets.UTF_8)), DELIVERY_OPTIONS, reply ->
                    {
                        try
                        {
                            if (reply.succeeded())
                            {
                                writeTriggeredActionEvent(emailRecipients, context, entityId, policy.getLong(ID), reply.result().body().put(RUNBOOK_WORKLOG_TYPE, PolicyTriggerActionType.NOTIFICATION.getName()), logger, mappers, builder);
                            }
                        }
                        catch (Exception exception)
                        {
                            logger.error(exception);
                        }
                    });

                }

                if (!smsRecipients.isEmpty())
                {
                    Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_REPLY, YES).put(NOTIFICATION_TYPE, Notification.NotificationType.SMS.getName()).put(Notification.SMS_NOTIFICATION_RECIPIENTS, smsRecipients)
                            .put(Notification.SMS_NOTIFICATION_MESSAGE, replaceMetricPolicyPlaceholders(policy, context, policy.getString(POLICY_MESSAGE) != null && !policy.getString(POLICY_MESSAGE).trim().isEmpty() ? policy.getString(POLICY_MESSAGE) : InfoMessageConstants.POLICY_DEFAULT_SMS, message)), DELIVERY_OPTIONS, reply ->
                    {
                        try
                        {
                            if (reply.succeeded())
                            {
                                writeTriggeredActionEvent(smsRecipients, context, entityId, policy.getLong(ID), reply.result().body().put(RUNBOOK_WORKLOG_TYPE, PolicyTriggerActionType.NOTIFICATION.getName()), logger, mappers, builder);
                            }
                        }

                        catch (Exception exception)
                        {
                            logger.error(exception);
                        }
                    });
                }

                if (MotadataConfigUtil.devMode() && (!emailRecipients.isEmpty()))
                {
                    Notification.sendEmail(new JsonObject()
                            .put(EventBusConstants.EVENT_REPLY, YES).put(NOTIFICATION_TYPE, Notification.NotificationType.EMAIL.getName())
                            .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                            .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add(severity.toLowerCase() + ".png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                            .put(Notification.EMAIL_NOTIFICATION_SUBJECT, replaceMetricPolicyPlaceholders(policy, context, policy.getString(POLICY_TITLE) != null && !policy.getString(POLICY_TITLE).trim().isEmpty() ? policy.getString(POLICY_TITLE) : InfoMessageConstants.POLICY_DEFAULT_EMAIL_SUBJECT, message))
                            .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, emailRecipients)
                            .put(Notification.EMAIL_NOTIFICATION_CONTENT, new StringSubstitutor(new JsonObject().mergeIn(ObjectConfigStore.getStore().getItem(entityId)).mergeIn(context).put(TIME_STAMP, DateTimeUtil.timestamp(context.getLong(EVENT_TIMESTAMP) * 1000L)).put(POLICY_NAME, policy.getString(POLICY_NAME)).put(SEVERITY, context.getString(SEVERITY).toLowerCase()).getMap()).replace(Notification.EMAIL_RENOTIFICATION_METRIC_POLICY_HTML_TEMPLATE).getBytes(StandardCharsets.UTF_8)));

                }

                if (!renotificationChannelRecipients.isEmpty())
                {
                    var runbook = new HashSet<String>();

                    if (actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()) != null && !actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).isEmpty()
                            && actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).containsKey(severity) && !actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).getJsonArray(severity).isEmpty())
                    {
                        var entries = actions.getJsonObject(PolicyTriggerActionType.RUNBOOK.getName()).getJsonArray(severity);

                        for (var index = 0; index < entries.size(); index++)
                        {
                            if (RunbookPluginConfigStore.getStore().existItem(entries.getJsonObject(index).getLong(ID)))
                            {
                                runbook.add(RunbookPluginConfigStore.getStore().getItem(entries.getJsonObject(index).getLong(ID)).getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME));
                            }
                        }
                    }

                    renotificationChannelRecipients.forEach((channelType, targets) ->
                    {
                        if (!targets.isEmpty())
                        {
                            Notification.send(new JsonObject()
                                    .mergeIn(context)
                                    .put(NOTIFICATION_TYPE, channelType)
                                    .put(CHANNELS, new JsonArray(new ArrayList<>(targets)))
                                    .put(Notification.CHANNEL_NOTIFICATION_CONTENT, new StringSubstitutor(new JsonObject()
                                            .mergeIn(ObjectConfigStore.getStore().getItem(entityId))
                                            .mergeIn(context)
                                            .put(TIME_STAMP, DateTimeUtil.timestamp(context.getLong(EVENT_TIMESTAMP) * 1000L))
                                            .put(POLICY_NAME, policy.getString(POLICY_NAME))
                                            .put(SEVERITY, context.getString(SEVERITY).toLowerCase())
                                            .put(ACTIVE_SINCE, duration)
                                            .put("policy.action", String.join(", ", runbook))
                                            .put("severity.color", MicrosoftTeamsNotification.MicrosoftTeamsColorCode.valueOf(context.getString(SEVERITY)).getColorCode())
                                            .getMap()).replace(Notification.getChannelNotificationTemplate(policy.getString(POLICY_TYPE), channelType, true))));
                        }
                    });
                }
            }
        }
    }

    /**
     * Updates the renotification timer for a policy.
     * This method manages the timing of renotifications for policies that remain
     * in a triggered state, ensuring that notifications are sent at appropriate intervals.
     *
     * @param severity            The severity level of the triggered policy
     * @param policyKey           The unique key identifying the policy
     * @param policyTimerContexts Map of policy timer contexts for managing timeouts
     * @param policy              The policy configuration
     * @param emailRecipients     Set of email recipients for renotification
     * @param smsRecipients       Set of SMS recipients for renotification
     * @param handleRecipients    Map of channel recipients for renotification
     */
    static void updateRenotificationTimer(String severity, String policyKey, Map<String, JsonObject> policyTimerContexts, JsonObject policy, Set<String> emailRecipients, Set<String> smsRecipients, Map<String, Set<String>> handleRecipients)
    {
        var renotificationContext = policy.getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.RENOTIFICATION.getName());

        policyTimerContexts.computeIfAbsent(policyKey, value -> new JsonObject());

        if (policyTimerContexts.get(policyKey) == null
                || !policyTimerContexts.get(policyKey).containsKey(POLICY_ACTIONS)
                || !policyTimerContexts.get(policyKey).getJsonObject(POLICY_ACTIONS).containsKey(PolicyTriggerActionType.RENOTIFICATION.getName())) // for first time we have to add timer
        {
            policyTimerContexts.get(policyKey).mergeIn(policy.copy());
        }

        if (policyTimerContexts.containsKey(policyKey)
                && policyTimerContexts.get(policyKey).getJsonObject(POLICY_ACTIONS).containsKey(PolicyTriggerActionType.RENOTIFICATION.getName())
                && !policyTimerContexts.get(policyKey).getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.RENOTIFICATION.getName()).isEmpty()
                && policyTimerContexts.get(policyKey).getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.RENOTIFICATION.getName()).containsKey(severity)
                && !policyTimerContexts.get(policyKey).getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.RENOTIFICATION.getName()).getJsonObject(severity).isEmpty())
        {
            var context = policyTimerContexts.get(policyKey).getJsonObject(POLICY_ACTIONS).getJsonObject(PolicyTriggerActionType.RENOTIFICATION.getName());

            if (context.getJsonObject(severity).getInteger(TIMER_SECONDS) <= 0)
            {
                setRecipients(emailRecipients, smsRecipients, handleRecipients, context.getJsonObject(severity).getJsonArray(RECIPIENTS));

                context.getJsonObject(severity).put(TIMER_SECONDS, renotificationContext.getJsonObject(severity).getInteger(TIMER_SECONDS));
            }

            for (var value : Severity.values())
            {
                if (!severity.equalsIgnoreCase(value.name()) && context.containsKey(value.name()))
                {
                    context.getJsonObject(value.name()).put(TIMER_SECONDS, renotificationContext.getJsonObject(value.name()).getInteger(TIMER_SECONDS));
                }
            }
        }
    }

    /**
     * Updates the auto-clear timer for a policy.
     * This method manages the timing for automatically clearing policies
     * after a specified duration, reducing the need for manual intervention.
     *
     * @param clearSeverity       Flag indicating if this is a severity clearing operation
     * @param policyKey           The unique key identifying the policy
     * @param seconds             The number of seconds before auto-clearing
     * @param context             The context containing policy state information
     * @param policyTimerContexts Map of policy timer contexts for managing timeouts
     */
    private static void updateAutoClearTimer(boolean clearSeverity, String policyKey, int seconds, JsonObject context, Map<String, JsonObject> policyTimerContexts)
    {
        //clearseverity so remove context from map
        //if policy is cleared and it is in autoclear and renotify need to remove that particular metric from it
        if (seconds > 0)
        {
            if (!clearSeverity)
            {
                policyTimerContexts.computeIfAbsent(policyKey, value -> new JsonObject());

                if (!policyTimerContexts.get(policyKey).containsKey(POLICY_AUTO_CLEAR_TIMER_SECONDS) || !policyTimerContexts.get(policyKey).getString(SEVERITY, Severity.UNKNOWN.name()).equalsIgnoreCase(context.getString(SEVERITY)))
                {
                    policyTimerContexts.get(policyKey).mergeIn(context).put(POLICY_AUTO_CLEAR_TIMER_SECONDS, seconds);
                }
            }

            else if (policyTimerContexts.containsKey(policyKey) && policyTimerContexts.get(policyKey).containsKey(POLICY_AUTO_CLEAR_TIMER_SECONDS))
            {
                if (policyTimerContexts.get(policyKey).containsKey(TIMER_SECONDS))
                {
                    policyTimerContexts.get(policyKey).remove(POLICY_AUTO_CLEAR_TIMER_SECONDS);
                }

                else
                {
                    policyTimerContexts.remove(policyKey);
                }
            }
        }
    }

    /**
     * Updates the unprovision timer for a policy.
     * This method manages the timing for automatically unprovisioning objects
     * that have been in a failed state for an extended period, helping to
     * clean up resources that are no longer functioning properly.
     *
     * @param policyKey           The unique key identifying the policy
     * @param policy              The policy configuration
     * @param clearSeverity       Flag indicating if this is a severity clearing operation
     * @param seconds             The number of seconds before unprovisioning
     * @param policyTimerContexts Map of policy timer contexts for managing timeouts
     */
    private static void updateUnprovisionTimer(String policyKey, JsonObject policy, boolean clearSeverity, int seconds, Map<String, JsonObject> policyTimerContexts)
    {
        //clear severity so remove context from map
        if (seconds > 0)
        {
            if (!clearSeverity)
            {
                policyTimerContexts.computeIfAbsent(policyKey, value -> new JsonObject());

                if (!policyTimerContexts.get(policyKey).containsKey(POLICY_UNPROVISION_OBJECT_TIMER_SECONDS))
                {
                    policyTimerContexts.get(policyKey).mergeIn(policy).put(POLICY_UNPROVISION_OBJECT_TIMER_SECONDS, seconds);
                }
            }
            else if (policyTimerContexts.containsKey(policyKey) && policyTimerContexts.get(policyKey).containsKey(POLICY_UNPROVISION_OBJECT_TIMER_SECONDS))
            {
                policyTimerContexts.get(policyKey).remove(POLICY_UNPROVISION_OBJECT_TIMER_SECONDS);
            }
        }
    }

    /**
     * Records policy action trigger events to the datastore.
     * This method logs information about policy actions (notifications, runbooks)
     * that have been triggered, providing an audit trail and historical record
     * of policy-driven activities.
     *
     * @param recipients The recipients of the notification
     * @param context    The context containing policy state information
     * @param entityId   ID of the entity associated with the policy
     * @param policyId   ID of the policy that triggered the action
     * @param result     The result of the triggered action
     * @param logger     Logger instance for recording any issues
     * @param mappers    Set of mappers used for datastore writing
     * @param builder    StringBuilder for constructing messages
     */
    public static void writeTriggeredActionEvent(JsonArray recipients, JsonObject context, long entityId, long policyId, JsonObject result, Logger logger, Set<String> mappers, StringBuilder builder)
    {
        var value = EMPTY_VALUE;

        try
        {

            if (PolicyTriggerActionType.valueOfName(result.getString(RUNBOOK_WORKLOG_TYPE)) == PolicyTriggerActionType.RUNBOOK)
            {
                var response = result.containsKey(EventBusConstants.EVENT_REPLY_CONTEXTS) && !result.getJsonArray(EventBusConstants.EVENT_REPLY_CONTEXTS).isEmpty() ? result.getJsonArray(EventBusConstants.EVENT_REPLY_CONTEXTS).getJsonObject(0) : null;

                if (response != null && !response.isEmpty())
                {
                    if (response.containsKey(RESULT))
                    {
                        value = new JsonObject().put(RESULT, CommonUtil.getString(response.getValue(RESULT))).encode();
                    }

                    result.put(STATUS, response.getValue(STATUS));
                }

                result.put(RUNBOOK_WORKLOG_TYPE, PolicyTriggerActionType.RUNBOOK.ordinal());
            }

            else
            {

                value = CommonUtil.getString(result.getJsonArray("recipients"));

                if (value == null)
                {
                    value = CommonUtil.getString(recipients);
                }

                result.put(RUNBOOK_WORKLOG_TYPE, PolicyTriggerActionType.NOTIFICATION.ordinal());
            }

            var error = result.getString(ERROR);

            DatastoreConstants.write(new JsonObject().put(RUNBOOK_WORKLOG_STATUS, result.getValue(STATUS, STATUS_FAIL))
                            .put(RUNBOOK_WORKLOG_RESULT, value.length() > RUNBOOK_WORKLOG_RESULT_MAX_BYTES ? value.substring(0, RUNBOOK_WORKLOG_RESULT_MAX_BYTES) : value)
                            .put(RUNBOOK_WORKLOG_ID, result.containsKey(ID) ? result.getLong(ID) : 0)
                            .put(RUNBOOK_WORKLOG_TYPE, result.getValue(RUNBOOK_WORKLOG_TYPE))
                            .put(POLICY_ID, policyId)
                            .put(AIOpsObject.OBJECT_ID, ObjectConfigStore.getStore().getObjectId(entityId))
                            .put(GlobalConstants.PLUGIN_ID, DatastoreConstants.PluginId.RUNBOOK_WORKLOG.getName())
                            .put(EVENT_TIMESTAMP, context.getLong(EVENT_TIMESTAMP))
                            .put(DatastoreConstants.DATASTORE_TYPE, DatastoreConstants.DatastoreType.RUNBOOK_WORKLOG.ordinal())
                            .put(USER_NAME, DEFAULT_USER)
                            .put(RUNBOOK_WORKLOG_ERROR, error != null ? (error.length() > RUNBOOK_WORKLOG_RESULT_MAX_BYTES ? error.substring(0, RUNBOOK_WORKLOG_RESULT_MAX_BYTES) : error) : EMPTY_VALUE)
                            .put(EVENT_SOURCE, context.getString(AIOpsObject.OBJECT_IP))
                            .put(INSTANCE, context.getString(INSTANCE, EMPTY_VALUE))
                    , VisualizationConstants.VisualizationDataSource.RUNBOOK_WORKLOG.getName(), mappers, builder);
        }

        catch (Exception exception)
        {
            logger.error(exception);
        }
    }

    /**
     * Prepares the context for runbook execution.
     * This method creates a context object with all necessary information
     * for executing a runbook in response to a policy trigger, including
     * entity information, policy details, and user credentials.
     *
     * @param id       ID of the runbook to execute
     * @param context  The context containing policy state information
     * @param entityId ID of the entity associated with the policy
     * @param policy   The policy configuration
     * @return A JsonObject containing the prepared context for runbook execution
     */
    static JsonObject prepareContext(long id, JsonObject context, long entityId, JsonObject policy)
    {
        var runbookContext = new JsonObject().mergeIn(context).put(ID, id)
                .put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, new JsonArray().add(entityId))
                .put(EventBusConstants.EVENT_REPLY, YES)
                .put(POLICY_ID, policy.getLong(ID))
                .put(POLICY_NAME, policy.getString(POLICY_NAME))
                .put(User.USER_NAME, DEFAULT_USER);

        if (context.containsKey(INSTANCE) && !context.getString(INSTANCE).isEmpty())
        {
            runbookContext.put(OBJECTS, new JsonArray().add(new JsonObject().put(AIOpsObject.OBJECT_NAME, context.getString(INSTANCE))));
        }

        // MOTADATA-4481
        if (context.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_NOTIFICATION_EMAIL_RECIPIENTS))
        {
            context.remove(RunbookPlugin.RUNBOOK_PLUGIN_NOTIFICATION_EMAIL_RECIPIENTS);

            context.remove(RunbookPlugin.RUNBOOK_PLUGIN_NOTIFICATION_EMAIL_SUBJECT);
        }

        return runbookContext;
    }

    /**
     * Finds applicable policies for an object based on its groups and tags.
     * This method aggregates policies from different mapping sources to create a complete
     * set of policies that should be evaluated for the object, sorting them by creation time
     * to ensure that newer policies take precedence.
     *
     * @param groups           The groups associated with the object
     * @param tags             The tags associated with the object
     * @param objectId         ID of the object to find policies for
     * @param policies         Map of all available policies
     * @param policiesByObject Map of policies assigned directly to objects
     * @param policiesByGroup  Map of policies assigned to groups
     * @param policiesByTag    Map of policies assigned to tags
     * @return A map of metrics to sets of policy IDs that should be evaluated
     */
    public static Map<String, LinkedHashSet<Long>> qualify(JsonArray groups, JsonArray tags, long objectId, Map<Long, JsonObject> policies, Map<Long, Map<String, List<Long>>> policiesByObject, Map<Long, Map<String, List<Long>>> policiesByGroup, Map<Long, Map<String, List<Long>>> policiesByTag)
    {
        var qualifiedPolicies = new HashMap<String, LinkedHashSet<Long>>();

        var keys = new HashSet<String>();

        policiesByObject.getOrDefault(objectId, Collections.emptyMap()).forEach((key, policyIds) ->
        {
            if (!policyIds.isEmpty())
            {
                qualifiedPolicies.computeIfAbsent(key, value -> new LinkedHashSet<>()).addAll(policyIds);
            }
        });

        if (groups != null)
        {
            for (var index = 0; index < groups.size(); index++)
            {
                var group = groups.getLong(index);

                policiesByGroup.getOrDefault(group, Collections.emptyMap()).forEach((key, policyIds) ->
                {
                    if (!policyIds.isEmpty())
                    {
                        if (qualifiedPolicies.containsKey(key))
                        {
                            keys.add(key);
                        }

                        qualifiedPolicies.computeIfAbsent(key, value -> new LinkedHashSet<>()).addAll(policyIds);
                    }
                });
            }
        }

        if (tags != null && !tags.isEmpty())
        {
            for (var index = 0; index < tags.size(); index++)
            {
                var tag = tags.getLong(index);

                policiesByTag.getOrDefault(tag, Collections.emptyMap()).forEach((key, policyIds) ->
                {
                    if (!policyIds.isEmpty())
                    {
                        if (qualifiedPolicies.containsKey(key))
                        {
                            keys.add(key);
                        }

                        qualifiedPolicies.computeIfAbsent(key, value -> new LinkedHashSet<>()).addAll(policyIds);
                    }
                });
            }
        }

        if (!keys.isEmpty())
        {
            keys.forEach(key ->
            {
                var values = qualifiedPolicies.get(key);

                if (values.size() > 1)
                {
                    qualifiedPolicies.put(key, values.stream()
                            .sorted(Comparator.comparing(item -> policies.get(CommonUtil.getLong(item)).getLong(POLICY_CREATION_TIME)).reversed())
                            .collect(Collectors.toCollection(LinkedHashSet::new)));
                }
            });
        }

        return qualifiedPolicies;
    }

    /**
     * Populates email and SMS recipient sets from a JSON array of recipients.
     * This method processes a list of recipients and adds them to the appropriate
     * recipient sets based on their type (email or user), extracting contact information
     * from user profiles when necessary.
     *
     * @param emailRecipients Set to populate with email addresses
     * @param smsRecipients   Set to populate with SMS numbers
     * @param recipients      JSON array of recipient configurations
     */
    public static void setRecipients(Set<String> emailRecipients, Set<String> smsRecipients, JsonArray recipients)
    {
        if (recipients != null)
        {
            for (var index = 0; index < recipients.size(); index++)
            {
                var recipient = recipients.getJsonObject(index);

                if (recipient.getString(RECIPIENT_TYPE, EMPTY_VALUE).equalsIgnoreCase(RecipientType.EMAIl.getName()))
                {
                    emailRecipients.add(recipient.getString(RECIPIENT));
                }
                else if (recipient.getString(RECIPIENT_TYPE, EMPTY_VALUE).equalsIgnoreCase(RecipientType.USER.getName()))
                {
                    var item = UserConfigStore.getStore().getItemByValue(User.USER_NAME, CommonUtil.getString(recipient.getString(RECIPIENT)));

                    if (item != null && !item.isEmpty())
                    {
                        if (item.containsKey(User.USER_EMAIL) && !item.getString(User.USER_EMAIL).isEmpty())
                        {
                            emailRecipients.add(item.getString(User.USER_EMAIL));
                        }

                        if (item.containsKey(User.USER_MOBILE) && !item.getString(User.USER_MOBILE).isEmpty() && !item.getString(User.USER_MOBILE).equals("0"))
                        {
                            smsRecipients.add(item.getString(User.USER_MOBILE));
                        }
                    }
                }
            }
        }
    }

    /**
     * Populates email, SMS, and channel recipient collections from a JSON array of recipients.
     * This method extends the basic setRecipients method by also handling channel recipients
     * for integration with external notification systems like Teams, Slack, etc.
     *
     * @param emailRecipients  Set to populate with email addresses
     * @param smsRecipients    Set to populate with SMS numbers
     * @param handleRecipients Map to populate with channel recipients by integration type
     * @param recipients       JSON array of recipient configurations
     */
    public static void setRecipients(Set<String> emailRecipients, Set<String> smsRecipients, Map<String, Set<String>> handleRecipients, JsonArray recipients)
    {
        if (recipients != null)
        {
            for (var index = 0; index < recipients.size(); index++)
            {
                var recipient = recipients.getJsonObject(index);

                if (recipient.getString(RECIPIENT_TYPE, EMPTY_VALUE).equalsIgnoreCase(RecipientType.EMAIl.getName()))
                {
                    emailRecipients.add(recipient.getString(RECIPIENT));
                }
                else if (recipient.getString(RECIPIENT_TYPE, EMPTY_VALUE).equalsIgnoreCase(RecipientType.USER.getName()))
                {
                    var item = UserConfigStore.getStore().getItemByValue(User.USER_NAME, CommonUtil.getString(recipient.getString(RECIPIENT)));

                    if (item != null && !item.isEmpty())
                    {
                        if (item.containsKey(User.USER_EMAIL) && !item.getString(User.USER_EMAIL).isEmpty())
                        {
                            emailRecipients.add(item.getString(User.USER_EMAIL));
                        }

                        if (item.containsKey(User.USER_MOBILE) && !item.getString(User.USER_MOBILE).isEmpty() && !item.getString(User.USER_MOBILE).equals("0"))
                        {
                            smsRecipients.add(item.getString(User.USER_MOBILE));
                        }
                    }
                }
                else if (recipient.getString(RECIPIENT_TYPE, EMPTY_VALUE).equalsIgnoreCase(RecipientType.CHANNEL.getName()))
                {
                    var item = IntegrationConfigStore.getStore().getItem(recipient.getLong(IntegrationProfile.INTEGRATION));

                    if (item != null)
                    {
                        handleRecipients.computeIfAbsent(item.getString(Integration.INTEGRATION_TYPE), key -> new HashSet<>())
                                .add(recipient.getString(ID));
                    }
                }
            }
        }
    }

    /**
     * Filters SNMP trap events based on specified conditions.
     * This method evaluates trap variable bindings against filter conditions
     * to determine if a trap event matches the criteria defined in a policy.
     *
     * @param filter The filter configuration containing conditions to evaluate
     * @param event  The trap event to filter
     * @return True if the trap event passes the filter, false otherwise
     */
    public static boolean filterTrap(JsonObject filter, JsonObject event)
    {
        var variables = event.getJsonArray(SNMPTrapProcessor.SNMP_TRAP_VARIABLES);

        var groupConditionSatisfied = true;

        if (filter != null && !filter.isEmpty())
        {
            var groupOperator = filter.getString(OPERATOR);

            var conditionGroups = filter.getJsonArray(CONDITION_GROUPS);

            //in trap there will be single group filter base on oid Variable Bindings
            var conditionGroup = conditionGroups.getJsonObject(0);

            var operator = conditionGroup.getString(OPERATOR);

            var conditions = conditionGroup.getJsonArray(CONDITIONS);

            var satisfied = false;

            for (var j = 0; j < conditions.size(); j++)
            {
                var condition = conditions.getJsonObject(j);

                var operand = condition.getInteger(OPERAND);

                if (variables.size() >= operand)
                {
                    satisfied = PolicyEngineConstants.evaluateCondition(conditionGroup.getString(FILTER).equalsIgnoreCase("include"), condition.getString(OPERATOR), condition.getValue(VALUE), variables.getString(operand - 1));

                    if ((satisfied && operator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.OR.getName())) || (!satisfied && operator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.AND.getName())))
                    {
                        break;
                    }
                }
                else
                {
                    satisfied = false;
                }
            }

            if (!satisfied && groupOperator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.AND.getName()))
            {
                //if main condition is "AND" and any counter group condition is not true so breaking for loop as in "AND" condition all condition is needed to be true
                return false;
            }
            else if (satisfied && groupOperator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.OR.getName()))
            {
                //if main condition is "OR" and any counter group condition is true so breaking for loop as in "OR" condition only one condition needed to be true
                return true;
            }
            else if (!satisfied && groupOperator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.OR.getName()))
            {
                return false;
            }

        }

        return groupConditionSatisfied;
    }

    /**
     * Updates column mappings in the data model.
     * This method processes column mapping information and updates the internal
     * data structures that track relationships between columns, plugins, and data types.
     *
     * @param columns The column mapping data structure to update
     * @param tokens  Array of tokens containing mapping information
     * @param metric  Flag indicating if this is a metric column (true) or event column (false)
     */
    public static void update(JsonObject columns, String[] tokens, boolean metric)
    {
        var column = tokens[DatastoreConstants.MetricWriterOrdinal.COLUMN.ordinal()];

        if (!columns.containsKey(column))
        {
            columns.put(column, new JsonObject());
        }

        var mapper = columns.getJsonObject(column);

        var plugins = mapper.getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS);

        if (plugins == null)
        {
            plugins = new JsonArray(new ArrayList<>(1));
        }

        if (!plugins.contains(CommonUtil.getInteger(tokens[1])))
        {
            mapper.put(DatastoreConstants.MAPPER_PLUGIN_IDS, plugins.add(CommonUtil.getInteger(tokens[1])));
        }

        var categories = mapper.getJsonArray(DatastoreConstants.MAPPER_DATA_CATEGORIES);

        if (categories == null)
        {
            categories = new JsonArray(new ArrayList<>(1));
        }

        if (!categories.contains(CommonUtil.getInteger(tokens[0])))
        {
            mapper.put(DatastoreConstants.MAPPER_DATA_CATEGORIES, categories.add(CommonUtil.getInteger(tokens[0])));
        }

        if (plugins.contains(DatastoreConstants.PluginId.CORRELATED_METRIC.getName()))
        {
            mapper.put(DatastoreConstants.MAPPER_CORRELATED, tokens[3]);
        }
        else
        {
            mapper.put(DatastoreConstants.MAPPER_STATUS, tokens[3]);
        }

        if (!metric && tokens.length > 4)
        {
            mapper.put(DatastoreConstants.MAPPER_EVENT_CATEGORY, tokens[4]);
        }

        if (column.contains(GlobalConstants.INSTANCE_SEPARATOR))
        {
            mapper.put(DatastoreConstants.MAPPER_INSTANCE, column.split(GlobalConstants.INSTANCE_SEPARATOR)[0]);
        }

        if (mapper.containsKey(DatastoreConstants.MAPPER_CORRELATED) && mapper.getString(DatastoreConstants.MAPPER_CORRELATED).equalsIgnoreCase(GlobalConstants.YES))
        {
            mapper.put(DatastoreConstants.MAPPER_GROUP, tokens[DatastoreConstants.EventWriterOrdinal.GROUP.ordinal()]);
        }
    }

    //
    public enum PolicyType
    {
        AVAILABILITY("Availability"),

        STATIC("Metric Threshold"),

        SUDDEN_SHIFT("Sudden Shift"),

        BASELINE("Metric Baseline"),

        ANOMALY("Metric Anomaly"),

        FORECAST("Forecast"),

        OUTLIER("Outlier"),

        LOG("Log"),

        FLOW("Flow"),

        APM("APM"),

        TRAP("Trap"),

        NETROUTE("NetRoute");

        private static final Map<String, PolicyType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(PolicyType::getName, e -> e)));
        private final String name;

        PolicyType(String name)
        {
            this.name = name;
        }

        public static PolicyType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    public enum Operator
    {
        GREATER_THAN(">"),

        LESS_THAN("<"),

        GREATER_THAN_OR_EQUAL(">="),

        LESS_THAN_OR_EQUAL("<="),

        EQUAL("="),

        NOT_EQUAL("!="),

        IN("in"),

        NOT("not"),

        NOT_IN("not in"),

        START_WITH("start with"),

        NOT_START_WITH("not start with"),

        END_WITH("end with"),

        NOT_END_WITH("not end with"),

        CONTAINS("contain"),

        NOT_CONTAINS("not contain"),

        ABOVE("above"),

        BELOW("below"),

        ABOVE_OR_BELOW("above or below"),

        RANGE("range");

        private static final Map<String, Operator> VALUES = Collections.unmodifiableMap(Arrays.stream(values()).collect(Collectors.toMap(Operator::getName, e -> e)));
        private final String name;

        Operator(String name)
        {
            this.name = name;
        }

        public static Operator valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    public enum OperatorLiteral
    {
        GREATER_THAN("Greater than"),

        LESS_THAN("Less than"),

        GREATER_THAN_OR_EQUAL("Greater than or equal"),

        LESS_THAN_OR_EQUAL("Less than or equal"),

        EQUAL("Equal"),

        NOT_EQUAL("Not equal"),

        IN("In"),

        NOT("Not"),

        BETWEEN("Between"),

        NOT_IN("Not in"),

        START_WITH("Starts with"),

        NOT_START_WITH("Not starts with"),

        END_WITH("Ends with"),

        NOT_END_WITH("Not ends with"),

        CONTAINS("Contains"),

        NOT_CONTAINS("Not Contains"),

        ABOVE("Above"),

        BELOW("Below"),

        ABOVE_OR_BELOW("Above or below");

        private static final Map<String, OperatorLiteral> VALUES = Collections.unmodifiableMap(Arrays.stream(values()).collect(Collectors.toMap(OperatorLiteral::getName, e -> e)));
        private final String name;

        OperatorLiteral(String name)
        {
            this.name = name;
        }

        public static OperatorLiteral valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    public enum ThresholdType
    {
        RELATIVE("relative"),
        ABSOLUTE("absolute");

        private static final Map<String, ThresholdType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(ThresholdType::getName, entity -> entity)));

        private final String name;

        ThresholdType(String name)
        {
            this.name = name;
        }

        public static ThresholdType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    public enum PolicyTriggerActionType
    {
        NOTIFICATION("Notification"),

        RENOTIFICATION("Renotification"),

        RUNBOOK("Runbook"),

        INTEGRATION("Integration"),

        CORRELATION("correlation");

        private static final Map<String, PolicyTriggerActionType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(PolicyTriggerActionType::getName, e -> e)));
        private final String name;

        PolicyTriggerActionType(String name)
        {
            this.name = name;
        }

        public static PolicyTriggerActionType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    public enum RecipientType
    {
        USER("user"),

        EMAIl("email"),

        CHANNEL("channel");

        private static final Map<String, RecipientType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(RecipientType::getName, e -> e)));
        private final String name;

        RecipientType(String name)
        {
            this.name = name;
        }

        public static RecipientType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

}
