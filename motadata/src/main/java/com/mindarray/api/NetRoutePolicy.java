/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			        Notes
 *  20-Feb-2025		Pruthviraj Jadeja		Initial commit
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.NetRouteConfigStore;
import com.mindarray.store.NetRoutePolicyConfigStore;
import com.mindarray.store.TagConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.HashSet;

import static com.mindarray.ErrorMessageConstants.INTERNAL_SERVER_EXCEPTION;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.EVENT_AUDIT;
import static com.mindarray.policy.PolicyEngineConstants.*;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;

public class NetRoutePolicy extends AbstractAPI
{
    private static final Logger LOGGER = new Logger(NetRoutePolicy.class, MOTADATA_API, "NetRoute Policy API");

    public NetRoutePolicy()
    {
        super("netroute-policies", NetRoutePolicyConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);

            router.get("/" + endpoint + "/:id/references").handler(this::getReferences);

            router.put("/" + endpoint + "/:id/state").handler(this::updateState); //update  policy state API
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            var item = NetRoutePolicyConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID))).getJsonObject(POLICY_CONTEXT);

            var items = new JsonArray();

            if (item.getJsonArray(ENTITIES) == null || item.getJsonArray(ENTITIES).isEmpty())
            {
                items = NetRouteConfigStore.getStore().getItems();
            }
            else
            {
                if (item.getString(ENTITY_TYPE).equalsIgnoreCase(Entity.NETROUTE.getName()))
                {
                    items = NetRouteConfigStore.getStore().getItems(item.getJsonArray(ENTITIES));
                }
                else if (item.getString(ENTITY_TYPE).equalsIgnoreCase(Tag.TAG))
                {
                    try
                    {
                        var entities = TagConfigStore.getStore().getIdsByItems(item.getJsonArray(ENTITIES)).stream().map(CommonUtil::getInteger).toList();

                        var counts = new HashSet<Long>();

                        var objects = NetRouteConfigStore.getStore().getItemsByMultiValueFieldAny(NetRoute.NETROUTE_TAGS, new JsonArray(entities));

                        for (var index = 0; index < objects.size(); index++)
                        {
                            var id = objects.getJsonObject(index).getLong(ID);

                            if (counts.add(id))
                            {
                                items.add(NetRouteConfigStore.getStore().getItem(id));
                            }
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }
            }

            promise.complete(response.put(Entity.NETROUTE.getName(), items));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
        return promise.future();
    }

    @Override
    protected Future<JsonObject> getEntityCountPreHook(JsonObject response)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            try
            {
                var ids = NetRoutePolicyConfigStore.getStore().getIds();

                for (var index = 0; index < ids.size(); index++)
                {
                    try
                    {
                        var item = NetRoutePolicyConfigStore.getStore().getItem(ids.getLong(index)).getJsonObject(POLICY_CONTEXT);

                        if (item.containsKey(METRIC))
                        {
                            if (item.getJsonArray(ENTITIES) == null || item.getJsonArray(ENTITIES).isEmpty())
                            {
                                try
                                {
                                    response.put(CommonUtil.getString(ids.getLong(index)), NetRouteConfigStore.getStore().getIds().size());
                                }

                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            }

                            else
                            {
                                if (item.getString(ENTITY_TYPE).equalsIgnoreCase(Entity.NETROUTE.getName()))
                                {
                                    response.put(CommonUtil.getString(ids.getLong(index)), item.getJsonArray(ENTITIES).size());
                                }
                                else if (item.getString(ENTITY_TYPE).equalsIgnoreCase(Tag.TAG))
                                {
                                    var entities = TagConfigStore.getStore().getIdsByItems(item.getJsonArray(ENTITIES)).stream().map(CommonUtil::getInteger).toList();

                                    var counts = new HashSet<Long>();

                                    var objects = NetRouteConfigStore.getStore().getItemsByMultiValueFieldAny(NetRoute.NETROUTE_TAGS, new JsonArray(entities));

                                    for (var i = 0; i < objects.size(); i++)
                                    {
                                        counts.add(objects.getJsonObject(i).getLong(ID));
                                    }

                                    response.put(CommonUtil.getString(ids.getLong(index)), counts.size());
                                }
                            }
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                }

                promise.complete(response);
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                promise.fail(exception);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return promise.future();
    }

    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        var promise = Promise.<JsonObject>promise();

        var items = NetRoutePolicyConfigStore.getStore().getItems();

        var params = routingContext.body().asJsonObject();

        var valid = true;

        // Need to check manually as we are not deleting policy just archiving so if that policy is archived so user can create policy with same archived one..
        for (var index = 0; index < items.size(); index++)
        {
            var item = items.getJsonObject(index);

            if ((!item.containsKey(POLICY_ARCHIVED) || item.getString(POLICY_ARCHIVED).equalsIgnoreCase(NO)) && item.getString(POLICY_NAME).equalsIgnoreCase(params.getString(POLICY_NAME)))
            {
                valid = false;

                send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                        .put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, POLICY_NAME)));

                promise.fail(String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, POLICY_NAME));

                break;
            }
        }

        if (valid)
        {
            promise.complete(params.put(POLICY_ARCHIVED, NO).put(POLICY_CREATION_TIME, DateTimeUtil.currentSeconds()).put(POLICY_STATE, YES));
        }

        return promise.future();
    }

    @Override
    protected Future<Void> afterCreate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, entity.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_POLICY.name()));

        return Future.succeededFuture();
    }

    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        return Future.succeededFuture(routingContext.body().asJsonObject().put(POLICY_CREATION_TIME, DateTimeUtil.currentSeconds()));
    }

    @Override
    protected Future<JsonObject> afterUpdate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, entity.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_POLICY.name()));

        return Future.succeededFuture();
    }

    @Override
    protected void delete(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var collection = schema.getString(APIConstants.ENTITY_COLLECTION);

                this.beforeDelete(routingContext).compose(value ->
                        Future.<JsonObject>future(promise ->
                        {
                            var id = CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID));

                            var item = this.configStore.getItem(id);

                            if (item != null && !item.isEmpty())
                            {
                                var validRequest = true;

                                if (item.getString(ConfigDBConstants.FIELD_TYPE) != null && item.getString(ConfigDBConstants.FIELD_TYPE).equalsIgnoreCase(ConfigDBConstants.ENTITY_TYPE_SYSTEM))
                                {
                                    validRequest = false;

                                    var message = String.format(ErrorMessageConstants.ENTITY_DELETE_NOT_ALLOWED, schema.getString(APIConstants.ENTITY_NAME));

                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                            .put(GlobalConstants.MESSAGE, message).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL));

                                    promise.fail(message);

                                    Bootstrap.vertx().eventBus().send(EVENT_AUDIT,
                                            new JsonObject().put(USER_NAME, routingContext.user().principal().getString(User.USER_NAME)).put(ENTITY_COLLECTION, collection)
                                                    .put(REQUEST, REQUEST_DELETE).put(MESSAGE, message).put(STATUS, Boolean.FALSE));

                                }

                                if (validRequest)
                                {
                                    item.getJsonObject(POLICY_CONTEXT).put(ENTITIES, new JsonArray());

                                    Bootstrap.configDBService().update(collection,
                                            new JsonObject().put(ConfigDBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, id),
                                            item.put(POLICY_ARCHIVED, YES),
                                            routingContext.user().principal().getString(User.USER_NAME), routingContext.request().remoteAddress().host(),
                                            result ->
                                            {
                                                if (result.succeeded())
                                                {
                                                    if (!result.result().isEmpty())
                                                    {
                                                        this.configStore.updateItem(id);

                                                        promise.complete(new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                                                .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_DELETED, schema.getString(APIConstants.ENTITY_NAME))).put(ID, id));

                                                    }

                                                    else
                                                    {
                                                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                                                .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), UNKNOWN)).put(GlobalConstants.STATUS, STATUS_FAIL));

                                                        promise.fail(String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), UNKNOWN));
                                                    }
                                                }

                                                else
                                                {
                                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                                                            .put(GlobalConstants.STATUS, STATUS_FAIL)
                                                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), result.cause().getMessage()))
                                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                            .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));

                                                    promise.fail(result.cause());

                                                }
                                            });
                                }
                            }

                            else
                            {
                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, collection))));

                                promise.fail(String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, collection));
                            }
                        }).compose(entity -> this.afterDelete(entity, routingContext))
                );
            }

            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
        }
    }

    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, entity.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_POLICY.name()));

        return Future.succeededFuture();
    }

    private void updateState(RoutingContext routingContext)
    {
        var state = routingContext.body().asJsonObject().getString(POLICY_STATE);

        var id = CommonUtil.getLong(routingContext.request().getParam(ID));

        try
        {
            Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_NETROUTE_POLICY, new JsonObject().put(ConfigDBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, id),
                    new JsonObject().put(POLICY_STATE, state), routingContext.user().principal().getString(USER_NAME)
                    , routingContext.request().remoteAddress().host(), result ->
                    {
                        if (result.succeeded())
                        {
                            NetRoutePolicyConfigStore.getStore().updateItem(id).onComplete(asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject().put(ID, id).put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_POLICY.name()));
                                }
                            });

                            this.send(routingContext, new JsonObject().put(MESSAGE, NetRoutePolicyConfigStore.getStore().getItem(id).getString(POLICY_NAME) + (state.equalsIgnoreCase(YES) ? " Enabled" : " Disabled") + " successfully...").put(RESPONSE_CODE, HttpStatus.SC_OK)
                                    .put(GlobalConstants.STATUS, STATUS_SUCCEED).put(ID, id));
                        }

                        else
                        {

                            this.send(routingContext, new JsonObject().put(MESSAGE, "Failed to " + (state.equalsIgnoreCase(YES) ? "Enable" : "Disable") + " policy " + NetRoutePolicyConfigStore.getStore().getItem(id).getString(POLICY_NAME)).put(RESPONSE_CODE, HttpStatus.SC_OK)
                                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, result.cause().getMessage()))
                                    .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace()))
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                    .put(GlobalConstants.STATUS, STATUS_FAIL).put(ID, id));
                        }
                    });
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
        }
    }
}
