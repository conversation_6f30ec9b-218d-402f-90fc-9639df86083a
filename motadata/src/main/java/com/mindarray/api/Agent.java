/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 *  28-Feb-2025		Sankalp		        Allowed Tags and Groups assignment to health agents
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.agent.AgentConstants;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.LicenseUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.http.HttpHeaders;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpStatus;

import java.io.FileInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.agent.AgentConstants.*;
import static com.mindarray.api.AIOpsObject.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.db.ConfigDBConstants.COLLECTION_AGENT;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.util.CommonUtil.isNotNullOrEmpty;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_OK;

public class Agent extends AbstractAPI
{
    public static final String AGENT_METRIC = "metric.agent";
    public static final String AGENT_PACKET = "packet.agent";
    public static final String AGENT_LOG = "log.agent";
    public static final String AGENT_UUID = "agent.id"; //agent uuid
    public static final String AGENT_VERSION = "agent.version";
    public static final String AGENT_CONFIGS = "agent.configs";
    public static final String AGENT_DEFAULT_CONFIG = "agent.default.configs";
    public static final String AGENT_OS_NAME = "agent.os.name";
    public static final String AGENT_BUSINESS_HOUR_PROFILE = "agent.business.hour.profile";
    public static final String AGENT_DISABLE = "agent.disable";
    public static final String AGENT_STATE = "agent.state"; // enable/disable state
    public static final String AGENT_STATUS_TYPE = "agent.status.type";
    public static final String AGENT_STATUS = "agent.status"; // if agent is monitor with heartbeat and heartbeat is missing in that case status will be considered based on this
    public static final String AGENT_CONFIGURATION_FILE = "agent.configuration.file";
    public static final String AGENT_FILE = "agent.json";
    public static final String AGENT_DELETION_STATUS = "agent.deactivation.status";
    private static final Logger LOGGER = new Logger(Agent.class, MOTADATA_API, "Agent API");

    public Agent()
    {
        super("agents", AgentConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router, Set.of(REQUEST_UPDATE));

            router.put("/" + endpoint + "/:id").handler(this::updateAgent);

            router.put("/" + endpoint + "/:id/state").handler(this::updateState); //update  object state API

            router.post("/" + endpoint + "/state").handler(this::updateStateAll); // update All objects state API

            router.put("/" + endpoint + "/:id/configure").handler(this::validate).handler(this::validateAction).handler(this::configure);

            router.get("/" + endpoint + "/:id/configuration").handler(this::exportConfig);

            router.post("/" + endpoint + "/:id/start").handler(this::validateAction).handler(this::start);

            router.post("/" + endpoint + "/:id/stop").handler(this::validateAction).handler(this::stop);

            router.post("/" + endpoint + "/:id/restart").handler(this::validateAction).handler(this::restart);

            router.post("/" + endpoint + "/:id/reset").handler(this::validateAction).handler(this::reset);

            router.get("/" + endpoint + "/:id/log").handler(this::validate).handler(this::validateAction).handler(this::downloadLog);

            router.post("/" + endpoint + "/:id/configuration").handler(this::validateAction).handler(this::importConfigs);

            router.post("/" + endpoint + "/start").handler(this::startAll);

            router.post("/" + endpoint + "/stop").handler(this::stopAll);

            router.post("/" + endpoint + "/restart").handler(this::restartAll);

            router.post("/" + endpoint + "/reset").handler(this::resetAll);

            router.post("/" + endpoint + "/configure").handler(this::configureAll);

            router.post("/" + endpoint + "/configuration").handler(this::importConfigAll);

            router.post("/" + endpoint + "/update").handler(this::updateAll);

            router.post("/" + endpoint).handler(this::deleteAll);
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    protected void updateAll(RoutingContext routingContext)
    {
        try
        {
            var params = routingContext.body().asJsonObject();

            if (params.containsKey(OBJECT_TAGS))
            {
                params.put(OBJECT_TAGS, TagConfigStore.getStore().addItems(params.getJsonArray(OBJECT_TAGS), Tag.TagType.OBJECT.getName(), ConfigDBConstants.ENTITY_TYPE_USER));

                updateAll(routingContext, params);
            }
            else if (params.containsKey(OBJECT_GROUPS))
            {
                updateAll(routingContext, params);
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    protected void updateAll(RoutingContext routingContext, JsonObject params)
    {
        try
        {
            var param = params.containsKey(AIOpsObject.OBJECT_TAGS) ? AIOpsObject.OBJECT_TAGS : AIOpsObject.OBJECT_GROUPS;

            var ids = params.getJsonArray(REQUEST_PARAM_IDS);

            var futures = new ArrayList<Future<Void>>();

            for (var id : ids)
            {
                var objects = new JsonArray().addAll(params.getJsonArray(param));

                var promise = Promise.<Void>promise();

                futures.add(promise.future());

                var items = ObjectConfigStore.getStore().getItemByAgentId(CommonUtil.getLong(id)).getJsonArray(param, new JsonArray());

                if (items != null && !items.isEmpty())
                {
                    for (var item : items)
                    {
                        if (!objects.contains(CommonUtil.getLong(item)))
                        {
                            objects.add(CommonUtil.getLong(item));
                        }
                    }
                }

                Bootstrap.configDBService().update(COLLECTION_AGENT, new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, id),
                        new JsonObject().put(param, objects),
                        routingContext.user().principal().getString(USER_NAME),
                        routingContext.request().remoteAddress().host(),
                        result ->
                        {
                            if (result.succeeded() && !result.result().isEmpty())
                            {
                                if (param.equalsIgnoreCase(AIOpsObject.OBJECT_TAGS))
                                {
                                    Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION,
                                            params.put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_AGENT_TAGS).put(ID, id).put(param, objects));
                                }
                                else
                                {
                                    Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION,
                                            params.put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_AGENT_GROUPS).put(ID, id).put(param, objects));
                                }

                                AgentConfigStore.getStore().updateItem(CommonUtil.getLong(id)).onComplete(asyncResult ->
                                {
                                    if (asyncResult.succeeded())
                                    {
                                        AgentCacheStore.getStore().updateItem(CommonUtil.getLong(id), AgentCacheStore.getStore().getItem(CommonUtil.getLong(id))
                                                .put(param, objects));

                                        promise.complete();
                                    }
                                    else
                                    {
                                        promise.fail(asyncResult.cause());
                                    }
                                });

                            }
                            else
                            {
                                promise.fail(result.cause());
                            }
                        });

            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    this.send(routingContext, new JsonObject().put(STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, SC_OK)
                            .put(REQUEST_PARAM_IDS, new JsonArray().add(ids))
                            .put(MESSAGE, String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.AGENT.getName())));
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                            .put(GlobalConstants.ERROR, result.failed() ? CommonUtil.formatStackTrace(result.cause().getStackTrace()) : UNKNOWN)
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_UPDATE_FAILED, Entity.AGENT.getName(), result.failed() ? result.cause().getMessage() : UNKNOWN)));
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected void deleteAll(RoutingContext routingContext)
    {
        try
        {
            var ids = routingContext.body().asJsonObject().getJsonArray(REQUEST_PARAM_IDS);

            var deletedItems = new JsonArray();

            if (ids != null && !ids.isEmpty())
            {
                var futures = new ArrayList<Future<Void>>();

                var references = new JsonArray();

                for (var id : ids)
                {
                    if ((RemoteEventProcessorConfigStore.getStore().getItemByValue(REMOTE_EVENT_PROCESSOR_UUID, AgentConfigStore.getStore().getItem(CommonUtil.getLong(id)).getString(Agent.AGENT_UUID)) == null))
                    {
                        var promise = Promise.<Void>promise();

                        futures.add(promise.future());

                        this.configStore.getReferenceEntities(CommonUtil.getLong(id)).onComplete(result ->
                        {
                            if (result.succeeded() && !result.result().isEmpty())
                            {
                                // contains references...
                                result.result().getMap().keySet().forEach(key ->
                                {
                                    for (var index = 0; index < result.result().getJsonArray(key).size(); index++)
                                    {
                                        var reference = result.result().getJsonArray(key).getJsonObject(index);

                                        references.add(new JsonObject().put(ENTITY_PROPERTY_TYPE, key)
                                                .put(ENTITY_NAME, reference.getString(REF_PROPS_BY_ENTITY.get(key)))
                                                .put(ENTITY_PROPERTY_NAME, reference.getString(REF_PROPS_BY_ENTITY.get(key))));
                                    }
                                });

                                promise.complete();
                            }
                            else
                            {
                                deletedItems.add(id);

                                var agent = AgentConfigStore.getStore().getItem(CommonUtil.getLong(id));

                                Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.DELETE_ARTIFACT.name()).put(ID, CommonUtil.getLong(id)).put(MotadataApp.ARTIFACT_TYPE, BootstrapType.AGENT.name()));

                                Bootstrap.vertx().eventBus().publish(EVENT_CHANGE_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.DELETE_AGENT.name()).put(AGENT_UUID, agent.getString(AGENT_UUID)));

                                Bootstrap.configDBService().delete(COLLECTION_AGENT,
                                        new JsonObject().put(ConfigDBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, CommonUtil.getLong(id)), routingContext.user().principal().getString(User.USER_NAME), routingContext.request().remoteAddress().host(),
                                        asyncResult ->
                                        {
                                            try
                                            {
                                                if (asyncResult.succeeded() && !asyncResult.result().isEmpty())
                                                {
                                                    this.configStore.deleteItem(CommonUtil.getLong(id));

                                                    AgentCacheStore.getStore().deleteItem(CommonUtil.getLong(id));

                                                    var item = ObjectConfigStore.getStore().getItemByAgentId(CommonUtil.getLong(id));

                                                    publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC).put(EVENT_TYPE, EventBusConstants.EVENT_AGENT_DELETE).put(ID, CommonUtil.getLong(id))
                                                            .put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(AGENT_DISABLE, YES)
                                                            .put(USER_NAME, routingContext.user().principal().getString(USER_NAME))
                                                            .put(Agent.AGENT_UUID, agent.getString(AGENT_UUID)));

                                                    var configs = new JsonObject(agent.getString(AGENT_CONFIGS));

                                                    if (YES.equalsIgnoreCase(configs.getJsonObject(AGENT).getString(METRIC_AGENT_STATUS)))
                                                    {
                                                        LicenseCacheStore.getStore().update(NMSConstants.AGENTS, false);
                                                    }

                                                    if (item != null)
                                                    {

                                                        //unprovision object
                                                        Bootstrap.vertx().eventBus().send(EVENT_OBJECT_UNPROVISION, item);

                                                        //mark availability as down after agent deleted
                                                        ObjectStatusCacheStore.getStore().updateItem(item.getLong(ID), STATUS_DOWN, DateTimeUtil.currentSeconds());
                                                    }

                                                    promise.complete();
                                                }
                                                else
                                                {
                                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                                                            .put(GlobalConstants.STATUS, STATUS_FAIL)
                                                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, Entity.AGENT.getName(), asyncResult.failed() ? asyncResult.cause().getMessage() : UNKNOWN))
                                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                            .put(GlobalConstants.ERROR, asyncResult.failed() ? CommonUtil.formatStackTrace(asyncResult.cause().getStackTrace()) : UNKNOWN));

                                                    promise.fail(asyncResult.failed() ? asyncResult.cause().getMessage() : UNKNOWN);
                                                }
                                            }
                                            catch (Exception exception)
                                            {
                                                LOGGER.error(exception);

                                                promise.fail(exception);
                                            }
                                        });
                            }
                        });
                    }
                }

                Future.join(futures).onComplete(result ->
                {
                    if (!references.isEmpty())
                    {
                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                .put(RESULT, references) // ui will display this references in tooltip
                                .put(REQUEST_PARAM_IDS, deletedItems)
                                .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED_IN_USED, Entity.OBJECT.getName()))
                                .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL));
                    }
                    else
                    {
                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(MESSAGE, "Agent deleted successfully")
                                .put(REQUEST_PARAM_IDS, deletedItems));
                    }
                });
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(STATUS, STATUS_FAIL)
                        .put(MESSAGE, ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void validateAction(RoutingContext routingContext)
    {
        var item = AgentCacheStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID)));

        if (item.containsKey(AGENT_DISABLE) && item.getString(AGENT_DISABLE).equalsIgnoreCase(YES)) //if any operation is running on agent system will not allow user to perform operation
        {
            this.send(routingContext, new JsonObject().put(ID, CommonUtil.getLong(routingContext.request().getParam(ID)))
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_AGENT_BUSY)
                    .put(STATUS, STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(MESSAGE, String.format(ErrorMessageConstants.AGENT_BUSY,
                            ObjectConfigStore.getStore().getAgentObjectName(CommonUtil.getLong(routingContext.request().getParam(ID))))));
        }

        else
        {
            routingContext.next();
        }
    }

    private void updateAgent(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var objectSchema = CommonUtil.getEntitySchema(NMSConstants.OBJECTS);

                if (objectSchema != null)
                {
                    var id = CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID));

                    var oldItem = this.configStore.getItem(id);

                    var oldObjectItem = ObjectConfigStore.getStore().getItemByAgentId(id);

                    if (oldObjectItem != null)
                    {
                        if (oldObjectItem.containsKey(AIOpsObject.OBJECT_CONTEXT))
                        {
                            oldObjectItem.mergeIn(oldObjectItem.getJsonObject(AIOpsObject.OBJECT_CONTEXT));

                            oldObjectItem.remove(AIOpsObject.OBJECT_CONTEXT);
                        }

                        oldItem.mergeIn(oldObjectItem); //merge item from `object and agent collection

                        var parameters = routingContext.body().asJsonObject();

                        var agentContext = new JsonObject()
                                .put(AGENT_BUSINESS_HOUR_PROFILE, parameters.getLong(AGENT_BUSINESS_HOUR_PROFILE))
                                .put(AGENT_STATUS_TYPE, parameters.getString(AGENT_STATUS_TYPE));

                        if (parameters.containsKey(AGENT_STATUS))
                        {
                            agentContext.put(AGENT_STATUS, parameters.getString(AGENT_STATUS));
                        }

                        //#24771
                        if (parameters.getJsonArray(AIOpsObject.OBJECT_TAGS) != null)
                        {
                            var items = TagConfigStore.getStore().addItems(parameters.getJsonArray(OBJECT_TAGS), Tag.TagType.OBJECT.getName(), ConfigDBConstants.ENTITY_TYPE_USER);

                            agentContext.put(AIOpsObject.OBJECT_TAGS, items);

                            parameters.put(OBJECT_TAGS, items);

                            Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION,
                                    parameters.put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_AGENT_TAGS).put(ID, id));
                        }

                        // update case of monitor name
                        if (parameters.containsKey(OBJECT_NAME) && !parameters.getString(OBJECT_NAME).equalsIgnoreCase(oldObjectItem.getString(OBJECT_NAME)) && ObjectConfigStore.getStore().getIdByObjectName(parameters.getString(OBJECT_NAME)) != null)
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_UPDATE_FAILED, Entity.AGENT.getName(), "provided monitor name is already in use")).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL));
                        }
                        else
                        {
                            ObjectConfigStore.getStore().updateItem(oldObjectItem.getString(OBJECT_NAME), parameters.getString(OBJECT_NAME));
                        }

                        var objectContext = new JsonObject().put(AIOpsObject.OBJECT_NAME, parameters.getString(AIOpsObject.OBJECT_NAME))
                                .put(AIOpsObject.OBJECT_BUSINESS_HOUR_PROFILE, parameters.getLong(AGENT_BUSINESS_HOUR_PROFILE))
                                .put(AIOpsObject.OBJECT_GROUPS, parameters.getJsonArray(AIOpsObject.OBJECT_GROUPS))
                                .put(AIOpsObject.OBJECT_IP, parameters.getString(AIOpsObject.OBJECT_IP));

                        Bootstrap.configDBService().update(schema.getString(ENTITY_COLLECTION),
                                new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, id),
                                agentContext, routingContext.user().principal().getString(USER_NAME), routingContext.request().remoteAddress().host(),
                                result ->
                                {
                                    if (result.succeeded() && !result.result().isEmpty())
                                    {
                                        Bootstrap.configDBService().update(objectSchema.getString(ENTITY_COLLECTION),
                                                new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, oldItem.getLong(ID)),
                                                objectContext, routingContext.user().principal().getString(USER_NAME), routingContext.request().remoteAddress().host(),
                                                asyncResult -> update(id, routingContext, oldItem, agentContext).onComplete(response -> this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                                        .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_UPDATED, schema.getString(APIConstants.ENTITY_NAME))).put(ID, id))));
                                    }
                                    else
                                    {
                                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                                                .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_UPDATE_FAILED, schema.getString(APIConstants.ENTITY_NAME), UNKNOWN)));

                                    }
                                });
                    }

                    else
                    {
                        this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                                .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, Entity.OBJECT.getName())));
                    }
                }

                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, NMSConstants.OBJECTS)));
                }
            }

            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    /*
     * update agent store and then update object collection....if changes in business.hour.profile will publish event to update particular business.hour.profile context in agent config file
     */
    private Future<Void> update(long id, RoutingContext routingContext, JsonObject oldItem, JsonObject agentContext)
    {
        var promise = Promise.<Void>promise();

        try
        {
            AgentConfigStore.getStore().updateItem(id).onComplete(result ->
            {
                var agent = AgentConfigStore.getStore().getItem(id);

                ObjectConfigStore.getStore().deleteItem(oldItem.getString(OBJECT_IP));

                ObjectConfigStore.getStore().updateItem(oldItem.getLong(ID)).onComplete(asyncResult ->
                {
                    if (asyncResult.succeeded())
                    {
                        AgentCacheStore.getStore().updateItem(id, AgentCacheStore.getStore().getItem(id).mergeIn(agentContext)
                                .put(AIOpsObject.OBJECT_GROUPS, routingContext.body().asJsonObject().getJsonArray(AIOpsObject.OBJECT_GROUPS)).put(AIOpsObject.OBJECT_NAME, routingContext.body().asJsonObject().getString(AIOpsObject.OBJECT_NAME)));
                    }
                });

                if (!Objects.equals(routingContext.body().asJsonObject().getLong(AGENT_BUSINESS_HOUR_PROFILE), oldItem.getLong(AGENT_BUSINESS_HOUR_PROFILE)))
                {
                    // update business hour profile in json file
                    var businessHour = BusinessHourConfigStore.getStore().getItem(agent.getLong(AGENT_BUSINESS_HOUR_PROFILE));

                    if (businessHour != null)
                    {
                        LOGGER.debug(String.format("agent %s updated...", id));

                        var configs = new JsonObject(agent.getString(AGENT_CONFIGS));

                        configs.getJsonObject(AGENT_METRIC).put(AGENT_BUSINESS_HOUR_PROFILE, businessHour);

                        publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, AGENT_TOPIC).put(EVENT_TYPE, EventBusConstants.EVENT_AGENT_CONFIGURATION_CHANGE).put(ID, id).put(AGENT_UUID, agent.getString(AGENT_UUID))
                                .put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                                .put(USER_NAME, routingContext.user().principal().getString(USER_NAME))
                                .put(AGENT_DISABLE, YES)
                                .put(AGENT_CONFIGS, configs.encode()));
                    }
                }

                var metric = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(oldItem.getLong(ID), NMSConstants.MetricPlugin.AVAILABILITY.getName()));

                if (metric != null)
                {
                    var state = agentContext.getString(AGENT_STATUS_TYPE).equalsIgnoreCase(AgentConstants.AgentStatusType.PING.getName()) ? NMSConstants.State.ENABLE.name() : NMSConstants.State.DISABLE.name();

                    if (!metric.getString(Metric.METRIC_STATE).equalsIgnoreCase(state))
                    {
                        Bootstrap.vertx().eventBus().send(state.equalsIgnoreCase(NMSConstants.State.ENABLE.name()) ? EVENT_METRIC_ENABLE : EVENT_METRIC_DISABLE, metric.put(Metric.METRIC_STATE, state));
                    }
                }

                promise.complete();
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    @Override
    protected Future<Void> afterGet(JsonObject item, RoutingContext routingContext)
    {
        try
        {
            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, enrich(item)));
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }

        return Future.succeededFuture();
    }

    @Override
    protected Future<Void> afterGetAll(JsonArray entities, RoutingContext routingContext)
    {
        var items = new JsonArray();

        for (var index = 0; index < entities.size(); index++)
        {
            items.add(enrich(entities.getJsonObject(index)));
        }

        this.send(routingContext, new JsonObject().put(STATUS, STATUS_SUCCEED).put(RESPONSE_CODE, SC_OK).put(RESULT, items));

        return Future.succeededFuture();
    }

    /**
     * Enrich object and agent collection...as well as enrich agent heartbeat,status and health context from agent cache
     */
    private JsonObject enrich(JsonObject item)
    {
        var agent = new JsonObject().mergeIn(item);

        try
        {
            agent.mergeIn(AgentCacheStore.getStore().getItem(agent.getLong(ID)));

            var object = ObjectConfigStore.getStore().getItemByAgentId(agent.getLong(ID));

            if (object != null)
            {
                object.put(ENTITY_ID, object.getLong(ID)); // put object id in entity.id key... so ui team will use this id for metric collection time API

                APIUtil.removeDefaultParameters(object);

                agent.mergeIn(object);

                agent.put(NMSConstants.APPS, enrich(MetricConfigStore.getStore().getItemsByObject(object.getLong(ENTITY_ID))));

                agent.put(OBJECT_INSTANCES, MetricConfigStore.getStore().getInstances(object.getLong(ENTITY_ID)));

                if (agent.getJsonArray(OBJECT_TAGS) != null)
                {
                    agent.put(AIOpsObject.OBJECT_TAGS, TagConfigStore.getStore().getItems(agent.getJsonArray(OBJECT_TAGS)));
                }
            }

            if (agent.containsKey(DURATION))
            {
                agent.put(DURATION, DateTimeUtil.convertTime(agent.getLong(DURATION)));
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return agent;
    }

    private JsonArray enrich(List<JsonObject> items)
    {
        var metrics = new JsonArray();

        for (var item : items)
        {
            if (!metrics.contains(item.getString(Metric.METRIC_TYPE)) && NMSConstants.APPLICATION_TYPES.contains(item.getString(Metric.METRIC_TYPE)))
            {
                metrics.add(item.getString(Metric.METRIC_TYPE));
            }
        }

        return metrics;
    }

    private void configureAll(RoutingContext routingContext)
    {
        try
        {
            var agents = AgentConfigStore.getStore().getItems(routingContext.body().asJsonObject().getJsonArray(REQUEST_PARAM_IDS));

            if (agents != null && !agents.isEmpty())
            {
                var configs = new JsonObject(routingContext.body().asJsonObject().getString(AGENT_CONFIGS));

                var requestParameters = routingContext.body().asJsonObject();

                var sessionId = routingContext.user().principal().getString(SESSION_ID);

                var userName = routingContext.user().principal().getString(USER_NAME);

                for (var index = 0; index < agents.size(); index++)
                {
                    var agent = agents.getJsonObject(index);

                    if (agent != null)
                    {
                        if (RemoteEventProcessorConfigStore.getStore().getItemByValue(REMOTE_EVENT_PROCESSOR_UUID, agent.getString(Agent.AGENT_UUID)) == null)
                        {
                            LOGGER.debug(String.format("configuring agent %s", agent.getLong(ID)));

                            configs.put(AGENT_UUID, agent.getString(AGENT_UUID));

                            updateConfigs(agent, new JsonObject(agent.getString(AGENT_CONFIGS)).mergeIn(configs, true), sessionId, userName, requestParameters);
                        }
                        else
                        {
                            LOGGER.debug(String.format("%s is a health agent", ObjectConfigStore.getStore().getAgentObjectName(agent.getLong(ID))));
                        }
                    }
                }

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(RESULT, InfoMessageConstants.AGENT_CONFIGURATION_CHANGE_ALL_REQUESTED));
            }

            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(STATUS, STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                        .put(MESSAGE, String.format(ErrorMessageConstants.AGENT_NOT_FOUND, routingContext.body().asJsonObject().getJsonArray(REQUEST_PARAM_IDS).toString())));
            }
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void exportConfig(RoutingContext routingContext)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(ID));

            var agent = AgentConfigStore.getStore().getItem(id);

            var object = ObjectConfigStore.getStore().getItemByAgentId(id);

            if (agent != null && object != null)
            {
                routingContext.response().setStatusCode(SC_OK).putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_STREAM)
                        .putHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + object.getString(AIOpsObject.OBJECT_NAME) + "\"")
                        .end(Buffer.buffer(new JsonObject(agent.getString(AGENT_CONFIGS)).encodePrettily()));
            }

            else
            {
                LOGGER.debug(String.format(ErrorMessageConstants.AGENT_NOT_FOUND, CommonUtil.getString(id)));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(STATUS, GlobalConstants.STATUS_FAIL)
                        .put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                        .put(MESSAGE, String.format(ErrorMessageConstants.AGENT_NOT_FOUND, CommonUtil.getString(id))));
            }
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void configure(RoutingContext routingContext)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(ID));

            var agent = AgentConfigStore.getStore().getItem(id);

            if (agent != null && !agent.isEmpty())
            {
                var configs = new JsonObject(routingContext.body().asJsonObject().getString(AGENT_CONFIGS));

                LOGGER.debug(String.format("configuring agent %s", id));

                updateConfigs(agent, new JsonObject(agent.getString(AGENT_CONFIGS)).mergeIn(configs, true), routingContext.user().principal().getString(SESSION_ID), routingContext.user().principal().getString(USER_NAME), routingContext.body().asJsonObject());

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(RESULT, InfoMessageConstants.AGENT_CONFIGURATION_CHANGE_REQUESTED));
            }

            else
            {
                LOGGER.debug(String.format(ErrorMessageConstants.AGENT_NOT_FOUND, CommonUtil.getString(id)));

                this.send(routingContext, new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(RESPONSE_CODE, SC_BAD_REQUEST).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                        .put(MESSAGE, String.format(ErrorMessageConstants.AGENT_NOT_FOUND, CommonUtil.getString(id))));
            }
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void resetAll(RoutingContext routingContext)
    {
        try
        {
            var agentIds = routingContext.body().asJsonObject().getJsonArray(REQUEST_PARAM_IDS);

            var agents = AgentConfigStore.getStore().getItems(agentIds);

            if (agents != null && !agents.isEmpty())
            {
                var requestParameters = routingContext.body().asJsonObject();

                var sessionId = routingContext.user().principal().getString(SESSION_ID);

                var userName = routingContext.user().principal().getString(USER_NAME);

                for (var index = 0; index < agents.size(); index++)
                {
                    var agent = agents.getJsonObject(index);

                    if (agent != null)
                    {
                        if (RemoteEventProcessorConfigStore.getStore().getItemByValue(REMOTE_EVENT_PROCESSOR_UUID, agent.getString(Agent.AGENT_UUID)) == null)
                        {
                            LOGGER.debug(String.format("resetting agent %s", agent.getLong(ID)));

                            updateConfigs(agent, new JsonObject(agent.getString(AGENT_DEFAULT_CONFIG)), sessionId, userName, requestParameters);
                        }
                        else
                        {
                            LOGGER.debug(String.format("%s is a health agent", ObjectConfigStore.getStore().getAgentObjectName(agent.getLong(ID))));
                        }
                    }
                }

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(RESULT, InfoMessageConstants.AGENT_RESET_ALL_REQUESTED));
            }

            else
            {
                LOGGER.debug(String.format(ErrorMessageConstants.AGENT_NOT_FOUND, agentIds.toString()));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                        .put(MESSAGE, String.format(ErrorMessageConstants.AGENT_NOT_FOUND, agentIds)));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void reset(RoutingContext routingContext)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(ID));

            var agent = AgentConfigStore.getStore().getItem(id);

            if (agent != null && !agent.isEmpty())
            {
                LOGGER.debug(String.format("resetting agent %s", id));

                updateConfigs(agent, new JsonObject(agent.getString(AGENT_DEFAULT_CONFIG)), routingContext.user().principal().getString(SESSION_ID), routingContext.user().principal().getString(USER_NAME), routingContext.body().asJsonObject());

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(RESULT, InfoMessageConstants.AGENT_RESET_REQUESTED));
            }

            else
            {
                LOGGER.debug(String.format(ErrorMessageConstants.AGENT_NOT_FOUND, CommonUtil.getString(id)));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                        .put(MESSAGE, String.format(ErrorMessageConstants.AGENT_NOT_FOUND, CommonUtil.getString(id))));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void downloadLog(RoutingContext routingContext)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(ID));

            var agent = AgentConfigStore.getStore().getItem(id);

            if (agent != null && !agent.isEmpty())
            {
                LOGGER.debug(String.format("downloading log for agent %s", id));

                publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, AGENT_TOPIC).put(UI_EVENT_UUID, routingContext.request().getParam(UI_EVENT_UUID)).put(EVENT_TYPE, EventBusConstants.EVENT_AGENT_DOWNLOAD_LOG).put(ID, id)
                        .put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(AGENT_DISABLE, YES)
                        .put(USER_NAME, routingContext.user().principal().getString(USER_NAME))
                        .put(AGENT_UUID, agent.getString(AGENT_UUID)));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(RESULT, InfoMessageConstants.AGENT_DOWNLOAD_LOG_REQUESTED));
            }

            else
            {
                LOGGER.debug(String.format(ErrorMessageConstants.AGENT_NOT_FOUND, CommonUtil.getString(id)));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                        .put(MESSAGE, String.format(ErrorMessageConstants.AGENT_NOT_FOUND, CommonUtil.getString(id))));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        try
        {
            var object = ObjectConfigStore.getStore().getItemByAgentId(CommonUtil.getLong(routingContext.request().getParam(ID)));

            if (object != null)
            {
                var ids = new JsonArray(new ArrayList<Long>(1)).add(object.getLong(ID));

                var items = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, ids);

                for (var index = 0; index < items.size(); index++)
                {
                    var metric = items.getJsonObject(index);

                    if (NMSConstants.CUSTOM_METRIC_TYPES.contains(metric.getString(Metric.METRIC_TYPE)))
                    {
                        if (!response.containsKey(Entity.METRIC.getName()))
                        {
                            response.put(Entity.METRIC.getName(), new JsonArray());
                        }

                        response.getJsonArray(Entity.METRIC.getName()).add(metric);
                    }
                }

                items = SchedulerConfigStore.getStore().getItems(SchedulerConfigStore.getStore().getItems(Scheduler.SCHEDULER_CONTEXT, NMSConstants.OBJECTS, ids, JobScheduler.JobType.REDISCOVER.getName()));

                if (items != null && !items.isEmpty())
                {
                    if (!response.containsKey(Entity.SCHEDULER.getName()))
                    {
                        response.put(Entity.SCHEDULER.getName(), new JsonArray());
                    }

                    response.getJsonArray(Entity.SCHEDULER.getName()).addAll(items);
                }

                items = RunbookPluginConfigStore.getStore().getItemsByMultiValueFieldAny(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, ids);

                if (!items.isEmpty())
                {
                    if (!response.containsKey(Entity.RUNBOOK_PLUGIN.getName()))
                    {
                        response.put(Entity.RUNBOOK_PLUGIN.getName(), new JsonArray());
                    }

                    response.getJsonArray(Entity.RUNBOOK_PLUGIN.getName()).addAll(items);
                }
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
        return Future.succeededFuture(response);
    }

    @Override
    protected Future<JsonObject> getEntityCountPreHook(JsonObject response)
    {
        try
        {
            var agentByObjects = new HashMap<Long, Long>();

            ObjectConfigStore.getStore().getItemsByAgentIds().stream().filter(item -> item.containsKey(AIOpsObject.OBJECT_AGENT))
                    .collect(Collectors.groupingBy(item -> item.getLong(ID), Collectors.mapping(item -> item.getLong(AIOpsObject.OBJECT_AGENT), Collectors.toList())))
                    .forEach((key, value) -> agentByObjects.put(key, value.getFirst()));

            var ids = new JsonArray(new ArrayList<>(agentByObjects.keySet()));

            var items = MetricConfigStore.getStore().getItemsByValues(Metric.METRIC_OBJECT, ids);

            for (var index = 0; index < items.size(); index++)
            {
                var metric = items.getJsonObject(index);

                if (NMSConstants.CUSTOM_METRIC_TYPES.contains(metric.getString(Metric.METRIC_TYPE)))
                {
                    var id = CommonUtil.getString(agentByObjects.get(metric.getLong(Metric.METRIC_OBJECT)));

                    response.put(id, response.containsKey(id) ? response.getInteger(id) + 1 : 1);
                }
            }

            items = SchedulerConfigStore.getStore().getItems(SchedulerConfigStore.getStore().getItems(Scheduler.SCHEDULER_CONTEXT, NMSConstants.OBJECTS, ids, JobScheduler.JobType.REDISCOVER.getName()));

            if (items != null)
            {
                for (var index = 0; index < items.size(); index++)
                {
                    var scheduler = items.getJsonObject(index);

                    if (scheduler.containsKey(Scheduler.SCHEDULER_CONTEXT))
                    {
                        scheduler.mergeIn(scheduler.getJsonObject(Scheduler.SCHEDULER_CONTEXT));
                    }

                    for (var object : scheduler.getJsonArray(NMSConstants.OBJECTS))
                    {
                        var id = CommonUtil.getString(agentByObjects.get(CommonUtil.getLong(object)));

                        response.put(id, response.containsKey(id) ? response.getInteger(id) + 1 : 1);
                    }
                }
            }

            items = RunbookPluginConfigStore.getStore().getItemsByValues(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, ids);

            for (var index = 0; index < items.size(); index++)
            {
                for (var object : items.getJsonObject(index).getJsonArray(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES))
                {
                    var id = CommonUtil.getString(agentByObjects.get(CommonUtil.getLong(object)));

                    response.put(id, response.containsKey(id) ? response.getInteger(id) + 1 : 1);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture(response);
    }

    @Override
    protected Future<JsonObject> beforeDelete(RoutingContext routingContext)
    {
        return Future.succeededFuture(new JsonObject().put(SHALLOW_DELETE, YES));
    }

    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        var id = entity.getLong(ID);

        var item = AgentConfigStore.getStore().getItem(id);

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.DELETE_ARTIFACT.name()).put(ID, id).put(MotadataApp.ARTIFACT_TYPE, BootstrapType.AGENT.name()));

        Bootstrap.vertx().eventBus().publish(EVENT_CHANGE_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.DELETE_AGENT.name()).put(AGENT_UUID, item.getString(AGENT_UUID)));

        Bootstrap.configDBService().delete(COLLECTION_AGENT,
                new JsonObject().put(ConfigDBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, id), routingContext.user().principal().getString(User.USER_NAME), routingContext.request().remoteAddress().host(),
                result ->
                {
                    try
                    {
                        if (result.succeeded() && !result.result().isEmpty())
                        {
                            this.configStore.deleteItem(id);

                            AgentCacheStore.getStore().deleteItem(id);

                            var object = ObjectConfigStore.getStore().getItemByAgentId(id);

                            publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC).put(EVENT_TYPE, EventBusConstants.EVENT_AGENT_DELETE).put(ID, id)
                                    .put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(AGENT_DISABLE, YES)
                                    .put(USER_NAME, routingContext.user().principal().getString(USER_NAME))
                                    .put(Agent.AGENT_UUID, item.getString(AGENT_UUID)));

                            var configs = new JsonObject(item.getString(AGENT_CONFIGS));

                            if (YES.equalsIgnoreCase(configs.getJsonObject(AGENT).getString(METRIC_AGENT_STATUS)))
                            {
                                LicenseCacheStore.getStore().update(NMSConstants.AGENTS, false);
                            }

                            if (object != null)
                            {

                                //unprovision object
                                Bootstrap.vertx().eventBus().send(EVENT_OBJECT_UNPROVISION, object);

                                //mark availability as down after agent deleted
                                ObjectStatusCacheStore.getStore().updateItem(object.getLong(ID), STATUS_DOWN, DateTimeUtil.currentSeconds());
                            }

                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(RESULT, InfoMessageConstants.AGENT_DELETE_REQUESTED));
                        }
                        else
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                                    .put(GlobalConstants.STATUS, STATUS_FAIL)
                                    .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, Entity.AGENT.getName(), result.failed() ? result.cause().getMessage() : UNKNOWN))
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                    .put(GlobalConstants.ERROR, result.failed() ? CommonUtil.formatStackTrace(result.cause().getStackTrace()) : UNKNOWN));
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        APIUtil.sendResponse(exception, routingContext);
                    }
                });

        return Future.succeededFuture();
    }

    private void start(RoutingContext routingContext)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(ID));

            var agent = AgentConfigStore.getStore().getItem(id);

            if (agent != null && !agent.isEmpty())
            {
                LOGGER.debug(String.format("starting agent %s", id));

                publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC).put(EVENT_TYPE, EventBusConstants.EVENT_AGENT_START).put(ID, id)
                        .put(USER_NAME, routingContext.user().principal().getString(USER_NAME)).put(AGENT_DISABLE, YES)
                        .put(AGENT_UUID, agent.getString(AGENT_UUID))
                        .put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(RESULT, String.format(InfoMessageConstants.START_REQUESTED, Entity.AGENT.getName())));
            }

            else
            {
                LOGGER.debug(String.format(ErrorMessageConstants.AGENT_NOT_FOUND, CommonUtil.getString(id)));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                        .put(MESSAGE, String.format(ErrorMessageConstants.AGENT_NOT_FOUND, CommonUtil.getString(id))));
            }
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void stop(RoutingContext routingContext)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(ID));

            var agent = AgentConfigStore.getStore().getItem(id);

            if (agent != null && !agent.isEmpty())
            {
                LOGGER.debug(String.format("stopping agent %s", id));

                publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC).put(EVENT_TYPE, EventBusConstants.EVENT_AGENT_STOP).put(ID, id)
                        .put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(AGENT_DISABLE, YES)
                        .put(USER_NAME, routingContext.user().principal().getString(USER_NAME))
                        .put(AGENT_UUID, agent.getString(AGENT_UUID)));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(RESULT, String.format(InfoMessageConstants.STOP_REQUESTED, Entity.AGENT.getName())));
            }

            else
            {
                LOGGER.debug(String.format(ErrorMessageConstants.AGENT_NOT_FOUND, CommonUtil.getString(id)));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                        .put(MESSAGE, String.format(ErrorMessageConstants.AGENT_NOT_FOUND, CommonUtil.getString(id))));
            }
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void restart(RoutingContext routingContext)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(ID));

            var agent = AgentConfigStore.getStore().getItem(id);

            if (agent != null && !agent.isEmpty())
            {
                LOGGER.debug(String.format("restarting agent %s", id));

                publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC).put(EVENT_TYPE, EventBusConstants.EVENT_AGENT_RESTART).put(ID, id)
                        .put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(AGENT_DISABLE, YES)
                        .put(USER_NAME, routingContext.user().principal().getString(USER_NAME))
                        .put(AGENT_UUID, agent.getString(AGENT_UUID)));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(RESULT, String.format(InfoMessageConstants.RESTART_REQUESTED, Entity.AGENT.getName())));
            }

            else
            {
                LOGGER.debug(String.format(ErrorMessageConstants.AGENT_NOT_FOUND, CommonUtil.getString(id)));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                        .put(MESSAGE, String.format(ErrorMessageConstants.AGENT_NOT_FOUND, CommonUtil.getString(id))));
            }
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void startAll(RoutingContext routingContext)
    {
        try
        {
            var agentIds = routingContext.body().asJsonObject().getJsonArray(REQUEST_PARAM_IDS);

            var agents = AgentConfigStore.getStore().getItems(agentIds);

            if (agents != null && !agents.isEmpty())
            {
                for (var index = 0; index < agents.size(); index++)
                {
                    var agent = agents.getJsonObject(index);

                    if (agent != null)
                    {
                        if (RemoteEventProcessorConfigStore.getStore().getItemByValue(REMOTE_EVENT_PROCESSOR_UUID, agent.getString(Agent.AGENT_UUID)) == null)
                        {
                            LOGGER.debug(String.format("starting agent %s", agent.getLong(ID)));

                            publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC).put(EVENT_TYPE, EventBusConstants.EVENT_AGENT_START)
                                    .put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(ID, agent.getLong(ID)).put(AGENT_DISABLE, YES)
                                    .put(USER_NAME, routingContext.user().principal().getString(USER_NAME))
                                    .put(AGENT_UUID, agent.getString(AGENT_UUID)));
                        }
                        else
                        {
                            LOGGER.debug(String.format("%s is a health agent", ObjectConfigStore.getStore().getAgentObjectName(agent.getLong(ID))));
                        }
                    }
                }

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(RESULT, String.format(InfoMessageConstants.START_ALL_REQUESTED, Entity.AGENT.getName())));
            }

            else
            {
                LOGGER.debug(String.format(ErrorMessageConstants.AGENT_NOT_FOUND, agentIds.toString()));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                        .put(MESSAGE, String.format(ErrorMessageConstants.AGENT_NOT_FOUND, agentIds)));
            }
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void stopAll(RoutingContext routingContext)
    {
        try
        {
            var agentIds = routingContext.body().asJsonObject().getJsonArray(REQUEST_PARAM_IDS);

            var agents = AgentConfigStore.getStore().getItems(agentIds);

            if (agents != null && !agents.isEmpty())
            {
                for (var index = 0; index < agents.size(); index++)
                {
                    var agent = agents.getJsonObject(index);

                    if (agent != null)
                    {
                        if (RemoteEventProcessorConfigStore.getStore().getItemByValue(REMOTE_EVENT_PROCESSOR_UUID, agent.getString(Agent.AGENT_UUID)) == null)
                        {
                            LOGGER.debug(String.format("stopping agent %s", agent.getLong(ID)));

                            publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC).put(EVENT_TYPE, EventBusConstants.EVENT_AGENT_STOP)
                                    .put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(ID, agent.getLong(ID)).put(AGENT_DISABLE, YES)
                                    .put(USER_NAME, routingContext.user().principal().getString(USER_NAME))
                                    .put(AGENT_UUID, agent.getString(AGENT_UUID)));
                        }
                        else
                        {
                            LOGGER.debug(String.format("%s is a health agent", ObjectConfigStore.getStore().getAgentObjectName(agent.getLong(ID))));
                        }
                    }
                }

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(RESULT, String.format(InfoMessageConstants.STOP_ALL_REQUESTED, Entity.AGENT.getName())));
            }

            else
            {
                LOGGER.debug(String.format(ErrorMessageConstants.AGENT_NOT_FOUND, agentIds.toString()));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                        .put(MESSAGE, String.format(ErrorMessageConstants.AGENT_NOT_FOUND, agentIds)));
            }
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void restartAll(RoutingContext routingContext)
    {
        try
        {
            var agentIds = routingContext.body().asJsonObject().getJsonArray(REQUEST_PARAM_IDS);

            var agents = AgentConfigStore.getStore().getItems(agentIds);

            if (agents != null && !agents.isEmpty())
            {
                for (var index = 0; index < agents.size(); index++)
                {
                    var agent = agents.getJsonObject(index);

                    if (agent != null)
                    {
                        if (RemoteEventProcessorConfigStore.getStore().getItemByValue(REMOTE_EVENT_PROCESSOR_UUID, agent.getString(Agent.AGENT_UUID)) == null)
                        {
                            LOGGER.debug(String.format("restarting agent %s", agent.getLong(ID)));

                            publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC).put(EVENT_TYPE, EventBusConstants.EVENT_AGENT_RESTART)
                                    .put(ID, agent.getLong(ID))
                                    .put(AGENT_UUID, agent.getString(AGENT_UUID)).put(AGENT_DISABLE, YES)
                                    .put(USER_NAME, routingContext.user().principal().getString(USER_NAME))
                                    .put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)));
                        }
                        else
                        {
                            LOGGER.debug(String.format("%s is a health agent", ObjectConfigStore.getStore().getAgentObjectName(agent.getLong(ID))));
                        }
                    }
                }

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(RESULT, String.format(InfoMessageConstants.RESTART_ALL_REQUESTED, Entity.AGENT.getName())));
            }

            else
            {
                LOGGER.debug(String.format(ErrorMessageConstants.AGENT_NOT_FOUND, agentIds.toString()));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                        .put(MESSAGE, String.format(ErrorMessageConstants.AGENT_NOT_FOUND, agentIds)));
            }
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void importConfigs(RoutingContext routingContext)
    {
        var id = CommonUtil.getLong(routingContext.request().getParam(ID));

        var body = routingContext.body().asJsonObject();

        if (body != null && body.containsKey(AGENT_CONFIGURATION_FILE))
        {
            doPostConfigValidation(body.getString(AGENT_CONFIGURATION_FILE), routingContext).onComplete(result ->
            {
                if (result.succeeded() && !result.result())
                {
                    try (var inputStream = new FileInputStream(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + body.getString(AGENT_CONFIGURATION_FILE)))
                    {
                        var configs = new JsonObject(IOUtils.toString(Objects.requireNonNull(inputStream), StandardCharsets.UTF_8));

                        var agent = AgentConfigStore.getStore().getItem(id);

                        if (agent != null && !agent.isEmpty())
                        {
                            LOGGER.debug(String.format("import config for agent %s", agent.getLong(ID)));

                            configs.getJsonObject(AGENT).put(AGENT_UUID, agent.getString(AGENT_UUID));

                            updateConfigs(agent, configs, routingContext.user().principal().getString(SESSION_ID), routingContext.user().principal().getString(USER_NAME), body);

                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(RESULT, InfoMessageConstants.AGENT_CONFIGURATION_CHANGE_REQUESTED));
                        }

                        else
                        {
                            LOGGER.debug(String.format(ErrorMessageConstants.AGENT_NOT_FOUND, CommonUtil.getString(id)));

                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                                    .put(MESSAGE, String.format(ErrorMessageConstants.AGENT_NOT_FOUND, CommonUtil.getString(id))));
                        }
                    }

                    catch (Exception exception)
                    {
                        APIUtil.sendResponse(exception, routingContext);
                    }
                }
            });
        }

        else
        {
            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                    .put(MESSAGE, String.format(ErrorMessageConstants.API_FIELD_REQUIRED, AGENT_CONFIGURATION_FILE)));
        }
    }

    private void updateConfigs(JsonObject agent, JsonObject configs, String sessionId, String userName, JsonObject requestParameters)
    {
        var oldConfigs = new JsonObject(agent.getString(Agent.AGENT_CONFIGS));

        // Skip this check for health agent
        if (oldConfigs.getJsonObject(AGENT).getString(AGENT_UUID).equalsIgnoreCase(Bootstrap.getRegistrationId()) ||
                (oldConfigs.getJsonObject(AGENT).getString(METRIC_AGENT_STATUS).equalsIgnoreCase(configs.getJsonObject(AGENT).getString(METRIC_AGENT_STATUS))
                        || NO.equalsIgnoreCase(configs.getJsonObject(AGENT).getString(METRIC_AGENT_STATUS))
                        || (YES.equalsIgnoreCase(configs.getJsonObject(AGENT).getString(METRIC_AGENT_STATUS)) && LicenseUtil.licensedObjectConsumed())))
        {
            if (!requestParameters.containsKey(AgentConstants.AGENT_RESTART_REQUIRED) || requestParameters.getString(AgentConstants.AGENT_RESTART_REQUIRED).equalsIgnoreCase(NO))
            {
                publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, AGENT_TOPIC).put(EVENT_TYPE, EventBusConstants.EVENT_AGENT_CONFIGURATION_CHANGE)
                        .put(ID, agent.getLong(ID)).put(AGENT_UUID, agent.getString(AGENT_UUID)).put(AGENT_DISABLE, YES)
                        .put(SESSION_ID, sessionId)
                        .put(USER_NAME, userName)
                        .put(AGENT_CONFIGS, configs.encodePrettily()));
            }

            else
            {
                if (!configs.getJsonObject(AGENT).getString(METRIC_AGENT_STATUS).equalsIgnoreCase(oldConfigs.getJsonObject(AGENT).getString(METRIC_AGENT_STATUS)))
                {
                    LicenseCacheStore.getStore().update(NMSConstants.AGENTS, YES.equalsIgnoreCase(configs.getJsonObject(AGENT).getString(METRIC_AGENT_STATUS)));
                }

                var updatedAgents = new JsonArray();

                if (oldConfigs.getJsonObject(AGENT).hashCode() != configs.getJsonObject(AGENT).hashCode())
                {
                    updatedAgents.add(AGENT);
                }

                if (oldConfigs.getJsonObject(AGENT_METRIC).hashCode() != configs.getJsonObject(AGENT_METRIC).hashCode())
                {
                    updatedAgents.add(AgentConstants.Agent.METRIC.getName());
                }

                if (oldConfigs.getJsonObject(AGENT_LOG).hashCode() != configs.getJsonObject(AGENT_LOG).hashCode())
                {
                    updatedAgents.add(AgentConstants.Agent.LOG.getName());
                }

                if (oldConfigs.getJsonObject(AGENT_PACKET).hashCode() != configs.getJsonObject(AGENT_PACKET).hashCode())
                {
                    updatedAgents.add(AgentConstants.Agent.PACKET.getName());
                }

                publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, AGENT_TOPIC).put(EVENT_TYPE, EventBusConstants.EVENT_AGENT_CONFIGURATION_CHANGE)
                        .put(ID, agent.getLong(ID)).put(AGENT_UUID, agent.getString(AGENT_UUID)).put(AGENT_DISABLE, YES)
                        .put(AGENT_TYPE, updatedAgents)
                        .put(SESSION_ID, sessionId)
                        .put(USER_NAME, userName)
                        .put(AGENT_CONFIGS, configs.encodePrettily()));
            }
        }
        else
        {
            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION,
                    new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_AGENT)
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_LICENSE_LIMIT_EXCEEDED)
                            .put(MESSAGE, String.format(ErrorMessageConstants.AGENT_CONFIG_UPDATE_FAILED, userName, ErrorMessageConstants.LICENSE_LIMIT_EXCEEDED))
                            .put(STATUS, STATUS_FAIL).put(EventBusConstants.EVENT_COPY_REQUIRED, false));

            EventBusConstants.publish(sessionId, EventBusConstants.UI_NOTIFICATION_OBJECT_PROVISION_PROGRESS,
                    new JsonObject().put(EventBusConstants.EVENT_TYPE, EVENT_AGENT)
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_LICENSE_LIMIT_EXCEEDED)
                            .put(MESSAGE, String.format(ErrorMessageConstants.AGENT_CONFIG_UPDATE_FAILED, userName, ErrorMessageConstants.LICENSE_LIMIT_EXCEEDED))
                            .put(STATUS, STATUS_FAIL).put(EventBusConstants.EVENT_COPY_REQUIRED, false));

            LOGGER.warn(String.format(ErrorMessageConstants.AGENT_CONFIG_UPDATE_FAILED, userName, ErrorMessageConstants.LICENSE_LIMIT_EXCEEDED));
        }


    }

    private void importConfigAll(RoutingContext routingContext)
    {
        var body = routingContext.body().asJsonObject();

        if (!body.isEmpty() && body.containsKey(AGENT_CONFIGURATION_FILE))
        {
            doPostConfigValidation(body.getString(AGENT_CONFIGURATION_FILE), routingContext).onComplete(result ->
            {
                if (result.succeeded() && !result.result())
                {
                    var agentIds = body.getJsonArray(REQUEST_PARAM_IDS);

                    try (InputStream inputStream = new FileInputStream(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + body.getString(AGENT_CONFIGURATION_FILE)))
                    {
                        var configs = new JsonObject(IOUtils.toString(Objects.requireNonNull(inputStream), StandardCharsets.UTF_8));

                        var agents = AgentConfigStore.getStore().getItems(agentIds);

                        if (agents != null && !agents.isEmpty())
                        {
                            var userName = routingContext.user().principal().getString(USER_NAME);

                            var sessionId = routingContext.user().principal().getString(SESSION_ID);

                            for (var index = 0; index < agents.size(); index++)
                            {
                                var agent = agents.getJsonObject(index);

                                if (agent != null)
                                {
                                    if (RemoteEventProcessorConfigStore.getStore().getItemByValue(REMOTE_EVENT_PROCESSOR_UUID, agent.getString(Agent.AGENT_UUID)) == null)
                                    {
                                        LOGGER.debug(String.format("import config for agent %s", agent.getLong(ID)));

                                        configs.getJsonObject(AGENT).put(AGENT_UUID, agent.getString(AGENT_UUID));

                                        updateConfigs(agent, configs, sessionId, userName, body);
                                    }
                                    else
                                    {
                                        LOGGER.debug(String.format("%s is a health agent", ObjectConfigStore.getStore().getAgentObjectName(agent.getLong(ID))));
                                    }
                                }
                            }

                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(RESULT, InfoMessageConstants.AGENT_CONFIGURATION_CHANGE_ALL_REQUESTED));
                        }

                        else
                        {
                            LOGGER.debug(String.format(ErrorMessageConstants.AGENT_NOT_FOUND, agentIds.toString()));

                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                                    .put(MESSAGE, String.format(ErrorMessageConstants.AGENT_NOT_FOUND, agentIds)));
                        }
                    }

                    catch (Exception exception)
                    {
                        APIUtil.sendResponse(exception, routingContext);
                    }
                }
            });
        }

        else
        {
            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                    .put(MESSAGE, String.format(ErrorMessageConstants.API_FIELD_REQUIRED, AGENT_CONFIGURATION_FILE)));
        }
    }

    /**
     * Validate agent config file
     */
    private Future<Boolean> doPostConfigValidation(String fileName, RoutingContext routingContext)
    {
        var promise = Promise.<Boolean>promise();

        try
        {
            var configs = new JsonObject(IOUtils.toString(new FileInputStream(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + fileName), StandardCharsets.UTF_8));

            var valid = true;

            if (!configs.isEmpty() && configs.getJsonObject(AGENT) != null && configs.getJsonObject(AGENT_METRIC) != null
                    && configs.getJsonObject(AGENT_LOG) != null && configs.getJsonObject(AGENT_PACKET) != null)
            {
                var requestParameters = new JsonObject();

                requestParameters.mergeIn(configs.getJsonObject(AGENT)).mergeIn(configs.getJsonObject(AGENT_METRIC)).mergeIn(configs.getJsonObject(AGENT_LOG)).mergeIn(configs.getJsonObject(AGENT_PACKET));

                valid = RequestValidator.validateRequest("motadata-agent", REQUEST_CREATE, routingContext, requestParameters, true, configStore);
            }

            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(STATUS, STATUS_FAIL)
                        .put(MESSAGE, ErrorMessageConstants.AGENT_INVALID_CONFIG));
            }

            promise.complete(valid);
        }
        catch (Exception exception)
        {
            promise.fail(exception);

            APIUtil.sendResponse(exception, routingContext);
        }

        return promise.future();
    }

    private void publishEvent(JsonObject event)
    {
        try
        {
            if (!event.isEmpty() && isNotNullOrEmpty(event.getString(EVENT_TYPE)) && isNotNullOrEmpty(event.getString(AGENT_UUID)))
            {
                event.put(EVENT_ID, CommonUtil.newEventId());

                if (CommonUtil.isNotNullOrEmpty(event.getString(AGENT_DISABLE))) //improvements - 3636 -> prevent user to perform any action
                {
                    AgentCacheStore.getStore().updateItem(event.getLong(ID), AgentCacheStore.getStore().getItem(event.getLong(ID)).put(AGENT_DISABLE, event.getString(AGENT_DISABLE)));

                    AgentCacheStore.getStore().updateDuration(event.getLong(ID), event.getString(AGENT_DISABLE));

                    EventBusConstants.publish(UI_NOTIFICATION_AGENT, new JsonObject().put(EVENT_TYPE, EVENT_AGENT_ACTION).put(ID, event.getLong(ID))
                            .put(AGENT_UUID, event.getString(AGENT_UUID)).put(AGENT_DISABLE, event.getString(AGENT_DISABLE)));
                }

                var context = new JsonObject().put(EventBusConstants.EVENT_CONTEXT, event)
                        .put(EventBusConstants.EVENT_ID, event.getLong(EVENT_ID))
                        .put(USER_NAME, event.remove(USER_NAME))
                        .put(EventBusConstants.EVENT_TYPE, event.getString(EVENT_TYPE));

                if (event.getString(UI_EVENT_UUID) != null)
                {
                    context.put(UI_EVENT_UUID, event.getString(UI_EVENT_UUID));
                }

                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_ADD, context);

                if (AGENT_TOPIC.equalsIgnoreCase(event.getString(EVENT_TOPIC)))
                {
                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_PUBLICATION, event);
                }
                else if (MOTADATA_MANAGER_TOPIC.equalsIgnoreCase(event.getString(EVENT_TOPIC)))
                {
                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_PUBLICATION_MOTADATA_MANAGER, event);
                }

            }

            else
            {
                LOGGER.warn(String.format("failed to send event %s", event));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

    }

    //This method is used to update states of agent
    private void updateState(RoutingContext routingContext)
    {
        try
        {
            changeAgentState(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID)), routingContext).onComplete(result ->
            {
                if (result.succeeded())
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                            .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.AGENT.getName())).put(ID, CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID))));
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(GlobalConstants.STATUS, STATUS_FAIL)
                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_UPDATE_FAILED, CommonUtil.getEntitySchema(endpoint).getString(APIConstants.ENTITY_NAME), UNKNOWN)));

                }
            });

            //TODO free license code
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void updateStateAll(RoutingContext routingContext)
    {
        try
        {
            var futures = new ArrayList<Future<Void>>();

            var ids = routingContext.body().asJsonObject().getJsonArray(APIConstants.REQUEST_PARAM_IDS);

            for (var index = 0; index < ids.size(); index++)
            {
                futures.add(changeAgentState(ids.getLong(index), routingContext));
            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED).put(RESPONSE_CODE, HttpStatus.SC_OK)
                            .put(REQUEST_PARAM_IDS, ids)
                            .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.AGENT.getName())));
                }

                else
                {
                    this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                            .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace()))
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_UPDATE_FAILED, Entity.OBJECT.getName(), result.cause().getMessage())));
                }
            });

            //TODO free license code
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private Future<Void> changeAgentState(long id, RoutingContext routingContext)
    {
        var promise = Promise.<Void>promise();

        try
        {
            var oldItem = AgentConfigStore.getStore().getItem(id);

            var oldObjectItem = ObjectConfigStore.getStore().getItemByAgentId(id);

            if (oldObjectItem != null)
            {
                if (oldObjectItem.containsKey(AIOpsObject.OBJECT_CONTEXT))
                {
                    oldObjectItem.mergeIn(oldObjectItem.getJsonObject(AIOpsObject.OBJECT_CONTEXT));

                    oldObjectItem.remove(AIOpsObject.OBJECT_CONTEXT);
                }

                oldItem.mergeIn(oldObjectItem); //merge item from `object and agent collection

                var parameters = routingContext.body().asJsonObject();

                if (!parameters.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(oldItem.getString(AIOpsObject.OBJECT_STATE)))
                {
                    if (RemoteEventProcessorConfigStore.getStore().getItemByValue(REMOTE_EVENT_PROCESSOR_UUID, oldItem.getString(Agent.AGENT_UUID)) == null)
                    {
                        Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_AGENT,
                                new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, id),
                                new JsonObject().put(AGENT_STATE, parameters.getString(AIOpsObject.OBJECT_STATE)),
                                routingContext.user().principal().getString(USER_NAME), routingContext.request().remoteAddress().host(),
                                result ->
                                {
                                    if (result.succeeded())
                                    {
                                        AgentConfigStore.getStore().updateItem(id).onComplete(asyncResult ->
                                        {
                                            if (asyncResult.succeeded())
                                            {
                                                var agent = AgentConfigStore.getStore().getItem(id);

                                                Bootstrap.vertx().eventBus().request(parameters.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()) ? EVENT_OBJECT_ENABLE : EVENT_OBJECT_DISABLE,
                                                        new JsonObject().put(ID, oldItem.getLong(ID)).put(NMSConstants.STATE, parameters.getString(AIOpsObject.OBJECT_STATE))
                                                                .put(USER_NAME, routingContext.user().principal().getString(USER_NAME)),
                                                        future ->
                                                        {
                                                            publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC).put(EVENT_TYPE, parameters.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()) ? EVENT_AGENT_ENABLE : EVENT_AGENT_DISABLE).put(ID, id).put(AGENT_UUID, agent.getString(AGENT_UUID))
                                                                    .put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                                                                    .put(USER_NAME, routingContext.user().principal().getString(USER_NAME))
                                                                    .put(SYSTEM_REMOTE_ADDRESS, routingContext.request().remoteAddress().host()));

                                                            publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC).put(EVENT_TYPE, parameters.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()) ? EVENT_AGENT_START : EVENT_AGENT_STOP).put(ID, id)
                                                                    .put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                                                                    .put(USER_NAME, routingContext.user().principal().getString(USER_NAME))
                                                                    .put(AGENT_UUID, agent.getString(AGENT_UUID)));

                                                            promise.complete();
                                                        });
                                            }
                                            else
                                            {
                                                promise.fail(asyncResult.cause());
                                            }
                                        });


                                    }
                                    else
                                    {
                                        promise.fail(result.cause());
                                    }
                                });
                    }
                    else
                    {
                        LOGGER.debug(String.format("%s is a health agent", ObjectConfigStore.getStore().getAgentObjectName(id)));

                        promise.complete();
                    }
                }

                else
                {
                    promise.complete();
                }
            }

            else
            {
                promise.fail(String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, Entity.OBJECT.getName()));

                this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, Entity.OBJECT.getName())));
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }
}
