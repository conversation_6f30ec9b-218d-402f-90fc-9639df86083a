/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.mindarray.GlobalConstants.EMPTY_VALUE;
import static com.mindarray.GlobalConstants.HASH_SEPARATOR;
import static com.mindarray.nms.NMSConstants.INSTANCE_IP_METRICS;

public class ObjectCacheStore extends AbstractCacheStore
{
    private static final Logger LOGGER = new Logger(ObjectCacheStore.class, GlobalConstants.MOTADATA_STORE, "Object Cache Store");

    private static final ObjectCacheStore STORE = new ObjectCacheStore();

    private final Map<String, String> items = new ConcurrentHashMap<>(); // key: object.id + # + instance.name -> value: instance.ip


    private ObjectCacheStore()
    {
    }

    public static ObjectCacheStore getStore()
    {
        return STORE;
    }

    /*
     *  id: Object Id (Long)
     *  item: polling data
     *  instance: instance type(example: esxi.vm)
     *  instanceValue: instanceName (example: vm1)
     *
     * */
    public void update(long id, Map<String, Object> item, String instanceType, String instanceName)
    {
        try
        {
            var metricName = INSTANCE_IP_METRICS.getOrDefault(instanceType, EMPTY_VALUE);

            if (!metricName.isEmpty() && item.containsKey(metricName))//will be checking whether instance has particular counter or not to add in cache
            {
                items.put(id + HASH_SEPARATOR + instanceName, CommonUtil.getString(item.get(metricName)));
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public String getInstanceIP(long id, String instance)
    {
        return items.getOrDefault(id + HASH_SEPARATOR + instance, EMPTY_VALUE);
    }

    public void delete(long id)
    {
        try
        {
            items.entrySet().removeIf(entry -> entry.getKey().split(HASH_SEPARATOR)[0].equalsIgnoreCase(CommonUtil.getString(id)));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
