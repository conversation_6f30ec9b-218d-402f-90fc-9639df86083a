/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.db;

import com.mindarray.GlobalConstants;
import com.mindarray.api.BackupProfile;
import com.mindarray.compliance.ComplianceConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.ha.HAConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.*;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.pgclient.PgBuilder;
import io.vertx.pgclient.PgConnectOptions;
import io.vertx.sqlclient.Pool;
import io.vertx.sqlclient.PoolOptions;
import io.vertx.sqlclient.Row;
import io.vertx.sqlclient.Tuple;
import org.apache.commons.io.FileUtils;
import org.zeroturnaround.zip.ZipUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.compliance.ComplianceConstants.COMPLIANCE_DB;
import static com.mindarray.eventbus.EventBusConstants.EVENT_AUDIT;

/**
 * Implementation of ConfigDBService that provides database operations for configuration data.
 * This service handles CRUD operations, backup/restore functionality, and compliance-related
 * database operations using PostgreSQL as the underlying database.
 *
 * <p>Key features include:
 * <ul>
 *   <li>Configuration data persistence with JSONB support</li>
 *   <li>High availability synchronization</li>
 *   <li>Audit trail and notification support</li>
 *   <li>Database backup and restore capabilities</li>
 *   <li>Compliance-specific database operations</li>
 * </ul>
 *
 * <AUTHOR> Engineering Team
 * @version 1.0
 * @since 2025
 */
public class ConfigDBServiceImpl implements ConfigDBService
{
    private static final Logger LOGGER = new Logger(ConfigDBServiceImpl.class, GlobalConstants.MOTADATA_DB, "Config DB Service");

    // Database connection constants
    private static final int DEFAULT_CONNECTION_POOL_SIZE = 5;
    private static final int RECONNECT_ATTEMPTS = 3;
    private static final long RECONNECT_INTERVAL_MILLIS = 10 * 1000L;
    private static final String DATABASE_NAME = "motadata";
    private static final String DATABASE_USER = "motadata";
    private static final String DATABASE_PASSWORD = "TRACEorg@2025";

    private final Vertx vertx;
    private Pool databaseConnectionPool;

    /**
     * Constructs a new ConfigDBServiceImpl instance and initializes the database connection.
     * Sets up PostgreSQL connection pool and creates required tables for configuration data.
     *
     * @param vertx The Vert.x instance for asynchronous operations
     * @param handler Handler to be called with the result of service initialization
     */
    ConfigDBServiceImpl(Vertx vertx, Handler<AsyncResult<ConfigDBService>> handler)
    {
        this.vertx = vertx;

        try
        {
            databaseConnectionPool = PgBuilder
                    .pool()
                    .with(new PoolOptions()
                            .setMaxSize(DEFAULT_CONNECTION_POOL_SIZE))
                    .connectingTo(new PgConnectOptions()
                            .setPort(MotadataConfigUtil.getPostgresDatabasePort())
                            .setHost(MotadataConfigUtil.getPostgresDatabaseHost())
                            .setDatabase(DATABASE_NAME)
                            .setUser(DATABASE_USER)
                            .setPassword(DATABASE_PASSWORD)
                            .setReconnectAttempts(RECONNECT_ATTEMPTS)
                            .setReconnectInterval(RECONNECT_INTERVAL_MILLIS)) // Reconnect attempts with interval
                    .using(vertx)
                    .build();


            db.query("SELECT 1").execute(result ->
            {
                if (result.succeeded())
                {
                    var promise = Promise.<Void>promise();

                    // need to clear the config db if we're running test cases
                    if (MotadataConfigUtil.devMode())
                    {
                        db.query("DROP SCHEMA public CASCADE;").execute()
                                .compose(future-> db.query("CREATE SCHEMA public;").execute())
                                .onComplete(asyncResult->promise.complete());
                    }
                    else
                    {
                        promise.complete();
                    }

                    promise.future().onComplete(voidAsyncResult->
                    {
                        // create required tables if not exists
                        db.query(String.format("CREATE TABLE IF NOT EXISTS %s (id BIGINT PRIMARY KEY, document JSONB)", ConfigDBConstants.COLLECTION_SYSTEM)).execute()
                                .onComplete(future->
                                {
                                    if (future.succeeded())
                                    {
                                        LOGGER.info("system table created successfully!");

                                        db.query(ComplianceConstants.TABLE_QUERY_COMPLIANCE_STATS_POLICY).execute(asyncResult ->
                                        {
                                            if (asyncResult.succeeded())
                                            {
                                                LOGGER.info(ComplianceConstants.TABLE_COMPLIANCE_STATS_POLICY + " Table Existed/Created Successfully ");
                                            }
                                            else
                                            {
                                                LOGGER.error(asyncResult.cause().getCause());
                                            }
                                        });

                                        db.query(ComplianceConstants.TABLE_QUERY_COMPLIANCE_STATS_ENTITY).execute(asyncResult ->
                                        {
                                            if (asyncResult.succeeded())
                                            {
                                                db.query(String.format(ComplianceConstants.INDEX_QUERY_COMPLIANCE_POLICY_ID, ComplianceConstants.TABLE_COMPLIANCE_STATS_ENTITY)).execute(response ->
                                                        LOGGER.info(ComplianceConstants.TABLE_COMPLIANCE_STATS_ENTITY + " : Indexable Existed/Column Created Successfully for compliance_policy_id"));

                                                db.query(String.format(ComplianceConstants.INDEX_QUERY_OBJECT_ID, ComplianceConstants.TABLE_COMPLIANCE_STATS_ENTITY)).execute(response ->
                                                        LOGGER.info(ComplianceConstants.TABLE_COMPLIANCE_STATS_ENTITY + " : Indexable Existed/Column Created Successfully for object_id"));

                                                LOGGER.info(ComplianceConstants.TABLE_COMPLIANCE_STATS_ENTITY + " Table Existed/Created Successfully");
                                            }
                                            else
                                            {
                                                LOGGER.error(asyncResult.cause().getCause());
                                            }
                                        });

                                        db.query(ComplianceConstants.TABLE_QUERY_COMPLIANCE_TRAIL).execute(asyncResult ->
                                        {
                                            if (asyncResult.succeeded())
                                            {
                                                db.query(String.format(ComplianceConstants.INDEX_QUERY_COMPLIANCE_POLICY_ID, ComplianceConstants.TABLE_COMPLIANCE_TRAIL)).execute(response ->
                                                        LOGGER.info(ComplianceConstants.TABLE_COMPLIANCE_TRAIL + " : Indexable Column Created Successfully for compliance_policy_id"));

                                                db.query(String.format(ComplianceConstants.INDEX_QUERY_OBJECT_ID, ComplianceConstants.TABLE_COMPLIANCE_TRAIL)).execute(response ->
                                                        LOGGER.info(ComplianceConstants.TABLE_COMPLIANCE_TRAIL + " : Indexable Column Created Successfully for object_id"));

                                                db.query(ComplianceConstants.INDEX_QUERY_RULE_ID).execute(response ->
                                                        LOGGER.info(ComplianceConstants.TABLE_COMPLIANCE_TRAIL + " : Indexable Column Created Successfully for compliance_rule_id"));

                                                LOGGER.info(ComplianceConstants.TABLE_COMPLIANCE_TRAIL + " Table Existed/Created Successfully");
                                            }
                                            else
                                            {
                                                LOGGER.error(asyncResult.cause().getCause());
                                            }
                                        });

                                        handler.handle(Future.succeededFuture(this));
                                    }
                                    else
                                    {
                                        LOGGER.error(future.cause());

                                        handler.handle(Future.failedFuture(future.cause()));
                                    }
                                });
                    });
                }
                else
                {
                    LOGGER.info(result.cause().getMessage());

                    handler.handle(Future.failedFuture(result.cause()));
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            handler.handle(Future.failedFuture(exception));
        }
    }

    @Override
    public ConfigDBService save(String table, JsonObject document, String user, String remoteIP, Handler<AsyncResult<Long>> handler)
    {
        saveAll(table, new JsonArray().add(document), user, remoteIP, result ->
        {
            if (result.succeeded())
            {
                handler.handle(Future.succeededFuture(result.result().getLong(0)));
            }
            else
            {
                LOGGER.error(result.cause());

                handler.handle(Future.failedFuture(result.cause()));
            }
        });

        return this;
    }

    @Override
    public ConfigDBService saveAll(String table, JsonArray documents, String user, String remoteIP, Handler<AsyncResult<JsonArray>> handler)
    {
        var createdItems = new JsonArray();

        db.query("CREATE TABLE IF NOT EXISTS " + table + " (id BIGINT PRIMARY KEY, document JSONB)").execute(result ->
        {
            try
            {
                if (result.succeeded())
                {
                    var documentIds = new JsonArray();

                    var batch = new ArrayList<Tuple>();

                    for (var index = 0; index < documents.size(); index++)
                    {
                        var document = documents.getJsonObject(index);

                        if (!document.containsKey(ConfigDBConstants.FIELD_TYPE))
                        {
                            document.put(ConfigDBConstants.FIELD_TYPE, ConfigDBConstants.ENTITY_TYPE_USER);
                        }

                        var id = document.containsKey(GlobalConstants.ID) ? document.getLong(GlobalConstants.ID) : CommonUtil.newId();

                        batch.add(Tuple.of(id, document.put(GlobalConstants.ID, id)));

                        documentIds.add(id);

                        createdItems.add(document);
                    }

                    if (!batch.isEmpty())
                    {
                        db.preparedQuery("INSERT INTO " + table + " (id, document) VALUES ($1, $2)")
                                .executeBatch(batch, asyncResult ->
                                {
                                    try
                                    {
                                        if (asyncResult.succeeded())
                                        {
                                            handler.handle(Future.succeededFuture(documentIds));

                                            if (!remoteIP.equalsIgnoreCase(MOTADATA_SYSTEM) && !remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                            {
                                                HAConstants.sync(table, createdItems, user, HAConstants.HASyncOperation.SAVE_ALL.getName(), BackupProfile.BackupProfileType.CONFIG_DB.getName());
                                            }

                                            if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                            {
                                                for (var index = 0; index < createdItems.size(); index++)
                                                {
                                                    notify(remoteIP, user, REQUEST_CREATE, table, Boolean.TRUE, null, null, createdItems.getJsonObject(index));
                                                }
                                            }
                                        }
                                        else
                                        {
                                            LOGGER.error(asyncResult.cause());

                                            handler.handle(Future.failedFuture(asyncResult.cause()));

                                            notify(remoteIP, user, REQUEST_CREATE, table, Boolean.FALSE, asyncResult.cause().getMessage(), null, null);
                                        }
                                    }
                                    catch (Exception exception)
                                    {
                                        LOGGER.error(exception);

                                        handler.handle(Future.failedFuture(exception));

                                        notify(remoteIP, user, REQUEST_CREATE, table, Boolean.FALSE, exception.getMessage(), null, null);
                                    }
                                });
                    }
                    else
                    {
                        LOGGER.debug("request received to insert empty data");

                        handler.handle(Future.succeededFuture(documentIds));
                    }
                }
                else
                {
                    LOGGER.error(result.cause());

                    handler.handle(Future.failedFuture(result.cause()));

                    notify(remoteIP, user, REQUEST_CREATE, table, Boolean.FALSE, result.cause().getMessage(), null, null);
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                handler.handle(Future.failedFuture(exception));

                notify(remoteIP, user, REQUEST_CREATE, table, Boolean.FALSE, exception.getMessage(), null, null);
            }

        });

        return this;
    }

    @Override
    public ConfigDBService get(String table, JsonObject query, Handler<AsyncResult<JsonArray>> handler)
    {
        var items = new JsonArray();

        try
        {
            if (query != null && !query.isEmpty())
            {
                var preparedQuery = new StringBuilder();

                preparedQuery.append("SELECT * FROM ").append(table).append(" WHERE ").append(query.getString(ConfigDBConstants.FIELD_NAME).equalsIgnoreCase(GlobalConstants.ID) ? "id" : String.format("document->>'%s'", query.getString(ConfigDBConstants.FIELD_NAME)));

                var vars = buildTuple(preparedQuery, query);

                db.preparedQuery(preparedQuery.toString())
                        .execute(vars, result ->
                        {
                            try
                            {
                                if (result.succeeded())
                                {
                                    for (var row : result.result())
                                    {
                                        items.add(row.getJsonObject("document"));
                                    }

                                    handler.handle(Future.succeededFuture(items));
                                }
                                else if (result.cause().getMessage().contains("does not exist"))
                                {
                                    handler.handle(Future.succeededFuture(items));
                                }
                                else
                                {
                                    LOGGER.error(result.cause());

                                    handler.handle(Future.failedFuture(result.cause()));
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);

                                handler.handle(Future.failedFuture(exception));
                            }
                        });
            }
            else
            {
                db.preparedQuery("SELECT * FROM " + table).execute(result->
                {
                    try
                    {
                        if (result.succeeded())
                        {
                            for (var row : result.result())
                            {
                                items.add(row.getJsonObject("document"));
                            }

                            handler.handle(Future.succeededFuture(items));
                        }
                        else if (result.cause().getMessage().contains("does not exist"))
                        {
                            handler.handle(Future.succeededFuture(items));
                        }
                        else
                        {
                            LOGGER.error(result.cause());

                            handler.handle(Future.failedFuture(result.cause()));
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        handler.handle(Future.failedFuture(exception));
                    }
                });
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            handler.handle(Future.failedFuture(exception));
        }

        return this;
    }

    @Override
    public ConfigDBService getById(String table, long id, Handler<AsyncResult<JsonObject>> handler)
    {
        getOneByQuery(table, new JsonObject().put(ConfigDBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, id), result ->
        {
            if (result.succeeded())
            {
                handler.handle(Future.succeededFuture(result.result()));
            }
            else
            {
                LOGGER.error(result.cause());

                handler.handle(Future.failedFuture(result.cause()));
            }
        });

        return this;
    }

    @Override
    public ConfigDBService getOneByQuery(String table, JsonObject query, Handler<AsyncResult<JsonObject>> handler)
    {
        get(table, query, result ->
        {
            if (result.succeeded())
            {
                if (!result.result().isEmpty())
                {
                    handler.handle(Future.succeededFuture(result.result().getJsonObject(0)));
                }
                else
                {
                    handler.handle(Future.succeededFuture(new JsonObject()));
                }
            }
            else
            {
                LOGGER.error(result.cause());

                handler.handle(Future.failedFuture(result.cause()));
            }
        });

        return this;
    }

    @Override
    public ConfigDBService getOne(String table, Handler<AsyncResult<JsonObject>> handler)
    {
        db.query("SELECT * FROM " + table + " LIMIT 1")
                .execute(result->
                {
                    try
                    {
                        if (result.succeeded())
                        {
                            var iterator = result.result().iterator();

                            if (iterator.hasNext())
                            {
                                handler.handle(Future.succeededFuture(iterator.next().getJsonObject("document")));
                            }
                            else
                            {
                                handler.handle(Future.succeededFuture(new JsonObject()));
                            }
                        }
                        else if (result.cause().getMessage().contains("does not exist"))
                        {
                            handler.handle(Future.succeededFuture(new JsonObject()));
                        }
                        else
                        {
                            LOGGER.error(result.cause());

                            handler.handle(Future.failedFuture(result.cause()));
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        handler.handle(Future.failedFuture(exception));
                    }
                });

        return this;
    }

    @Override
    public ConfigDBService getAll(String table, Handler<AsyncResult<JsonArray>> handler)
    {
        get(table, null, result ->
        {
            if (result.succeeded())
            {
                handler.handle(Future.succeededFuture(result.result()));
            }
            else
            {
                LOGGER.error(result.cause());

                handler.handle(Future.failedFuture(result.cause()));
            }
        });

        return this;

    }

    @Override
    public ConfigDBService delete(String table, JsonObject query, String user, String remoteIP, Handler<AsyncResult<JsonArray>> handler)
    {
        deleteAll(table, query, user, remoteIP, result ->
        {
            if (result.succeeded())
            {
                handler.handle(Future.succeededFuture(result.result()));
            }
            else
            {
                LOGGER.error(result.cause());

                handler.handle(Future.failedFuture(result.cause()));
            }
        });

        return this;
    }

    @Override
    public ConfigDBService deleteAll(String table, JsonObject query, String user, String remoteIP, Handler<AsyncResult<JsonArray>> handler)
    {
        try
        {
            var items = new JsonArray();

            var documents = new JsonArray();

            var preparedQuery = new StringBuilder();

            preparedQuery.append("DELETE FROM ").append(table).append(" WHERE ").append(query.getString(ConfigDBConstants.FIELD_NAME).equalsIgnoreCase(GlobalConstants.ID) ? "id" : String.format("document->>'%s' != '%s' AND document->>'%s'", ConfigDBConstants.FIELD_TYPE, ConfigDBConstants.ENTITY_TYPE_SYSTEM, query.getString(ConfigDBConstants.FIELD_NAME)));

            var vars = buildTuple(preparedQuery, query);

            preparedQuery.append(" RETURNING *");

            db.preparedQuery(preparedQuery.toString())
                    .execute(vars, result ->
                    {
                        try
                        {
                            if (result.succeeded())
                            {
                                for (var row : result.result())
                                {
                                    items.add(row.getJsonObject("document"));

                                    documents.add(row.getLong(ID));
                                }

                                if (!remoteIP.equalsIgnoreCase(MOTADATA_SYSTEM) && !remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                {
                                    HAConstants.sync(table, query, null, user, remoteIP, HAConstants.HASyncOperation.DELETE.getName(), BackupProfile.BackupProfileType.CONFIG_DB.getName());
                                }

                                if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                {
                                    for (var index = 0; index < items.size(); index++)
                                    {
                                        notify(remoteIP, user, REQUEST_DELETE, table, Boolean.TRUE, null, null, items.getJsonObject(index));
                                    }
                                }

                                handler.handle(Future.succeededFuture(documents));
                            }
                            else
                            {
                                LOGGER.error(result.cause());

                                if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                {
                                    notify(remoteIP, user, REQUEST_DELETE, table, Boolean.TRUE, result.cause().getMessage(), null, null);
                                }

                                handler.handle(Future.failedFuture(result.cause()));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            handler.handle(Future.failedFuture(exception));
                        }
                    });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            handler.handle(Future.failedFuture(exception));
        }

        return this;
    }

    @Override
    public ConfigDBService drop(String table, Handler<AsyncResult<Void>> handler)
    {
        db.preparedQuery("SELECT EXISTS (SELECT 1 FROM pg_tables  WHERE tablename = '" + table + "' )")
                .execute(result->
                {
                    try
                    {
                        if (result.succeeded())
                        {
                            var iterator = result.result().iterator();

                            if (iterator.hasNext() && iterator.next().getBoolean(0))
                            {
                                db.preparedQuery("DROP TABLE " + table).execute(asyncResult->
                                {
                                    if (asyncResult.succeeded())
                                    {
                                        HAConstants.sync(table, null, null, SYSTEM_USER, SYSTEM_REMOTE_ADDRESS, HAConstants.HASyncOperation.DROP.getName(), BackupProfile.BackupProfileType.CONFIG_DB.getName());

                                        notify(SYSTEM_REMOTE_ADDRESS, SYSTEM_USER, REQUEST_DROP, table, Boolean.TRUE, null, null, null);

                                        handler.handle(Future.succeededFuture());
                                    }
                                    else
                                    {
                                        LOGGER.error(asyncResult.cause());

                                        handler.handle(Future.failedFuture(asyncResult.cause()));
                                    }
                                });
                            }
                            else
                            {
                                handler.handle(Future.succeededFuture());
                            }
                        }
                        else
                        {
                            LOGGER.error(result.cause());

                            handler.handle(Future.failedFuture(result.cause()));
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        handler.handle(Future.failedFuture(exception));
                    }
                });

        return this;
    }

    @Override
    public ConfigDBService update(String table, JsonObject query, JsonObject document, String user, String remoteIP, Handler<AsyncResult<JsonArray>> handler)
    {
        updateAll(table, query, document, user, remoteIP, result ->
        {
            if (result.succeeded())
            {
                handler.handle(Future.succeededFuture(result.result()));
            }
            else
            {
                LOGGER.error(result.cause());

                handler.handle(Future.failedFuture(result.cause()));
            }
        });

        return this;
    }

    //this method is strictly used during configdbinitializer
    @Override
    public ConfigDBService upsert(String table, JsonObject document, String user, String remoteIP, Handler<AsyncResult<Long>> handler)
    {
        this.upsertAll(table, new JsonArray().add(document), user, remoteIP, result ->
        {
            if (result.succeeded())
            {
                handler.handle(Future.succeededFuture(result.result().getLong(0)));
            }
            else
            {
                LOGGER.error(result.cause());

                handler.handle(Future.failedFuture(result.cause()));
            }
        });

        return this;
    }

    @Override
    public ConfigDBService upsertAll(String table, JsonArray documents, String user, String remoteIP, Handler<AsyncResult<JsonArray>> handler)
    {
        var items = new JsonArray();

        db.query("CREATE TABLE IF NOT EXISTS " + table + " (id BIGINT PRIMARY KEY, document JSONB)").execute(result ->
        {
            try
            {
                if (result.succeeded())
                {
                    var documentIds = new JsonArray();

                    if (documents != null)
                    {
                        var batch = new ArrayList<Tuple>();

                        for (var index = 0; index < documents.size(); index++)
                        {
                            var document = documents.getJsonObject(index);

                            if (!document.containsKey(ConfigDBConstants.FIELD_TYPE))
                            {
                                document.put(ConfigDBConstants.FIELD_TYPE, ConfigDBConstants.ENTITY_TYPE_USER);
                            }

                            var id = document.containsKey(GlobalConstants.ID) ? document.getLong(GlobalConstants.ID) : CommonUtil.newId();

                            batch.add(Tuple.of(id, document.put(GlobalConstants.ID, id)));

                            documentIds.add(id);

                            items.add(document);
                        }

                        if (!batch.isEmpty())
                        {
                            db.preparedQuery("INSERT INTO " + table + " (id, document) VALUES ($1, $2) ON CONFLICT (id) DO UPDATE SET document = " + table + ".document || EXCLUDED.document")
                                    .executeBatch(batch, asyncResult ->
                                    {
                                        try
                                        {
                                            if (asyncResult.succeeded())
                                            {
                                                handler.handle(Future.succeededFuture(documentIds));

                                                if (!remoteIP.equalsIgnoreCase(MOTADATA_SYSTEM) && !remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                                {
                                                    HAConstants.sync(table, items, user, HAConstants.HASyncOperation.UPSERT.getName(), BackupProfile.BackupProfileType.CONFIG_DB.getName());
                                                }

                                                if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                                {
                                                    for (var index = 0; index < items.size(); index++)
                                                    {
                                                        notify(remoteIP, user, REQUEST_CREATE, table, Boolean.TRUE, null, null, items.getJsonObject(index));
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                LOGGER.error(asyncResult.cause());

                                                handler.handle(Future.failedFuture(asyncResult.cause()));

                                                notify(remoteIP, user, REQUEST_CREATE, table, Boolean.FALSE, asyncResult.cause().getMessage(), null, null);
                                            }
                                        }
                                        catch (Exception exception)
                                        {
                                            LOGGER.error(exception);

                                            handler.handle(Future.failedFuture(exception));
                                        }
                                    });
                        }
                        else
                        {
                            LOGGER.debug("request received to insert empty data");

                            handler.handle(Future.succeededFuture(documentIds));
                        }

                    }
                    else // this is the case when no entries exist, but we need to create the table (ex: agent.json)
                    {
                        handler.handle(Future.succeededFuture(documentIds));
                    }
                }
                else
                {
                    LOGGER.error(result.cause());

                    handler.handle(Future.failedFuture(result.cause()));

                    notify(remoteIP, user, REQUEST_CREATE, table, Boolean.FALSE, result.cause().getMessage(), null, null);
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                handler.handle(Future.failedFuture(exception));
            }
        });

        return this;
    }

    @Override
    public ConfigDBService updateAll(String table, JsonObject query, JsonObject document, String user, String remoteIP, Handler<AsyncResult<JsonArray>> handler)
    {
        var oldItems = new ArrayList<JsonObject>();

        var documents =  new ArrayList<Long>();

        this.get(table, query, result->
        {
            if (result.succeeded() && !result.result().isEmpty())
            {
                try
                {
                    var preparedQuery = new StringBuilder();

                    var batch = new ArrayList<Tuple>();

                    preparedQuery.append("UPDATE ").append(table).append(" AS d SET document = v.document FROM ( VALUES ($1::BIGINT, $2::JSONB) ) AS v(id, document) WHERE d.id = v.id");

                    var items = result.result();

                    for (var index = 0; index < items.size(); index++)
                    {
                        var item = items.getJsonObject(index);

                        oldItems.add(item);

                        var garbageFields = (JsonArray) document.remove(ConfigDBConstants.GARBAGE_FIELDS);

                        removeGarbageFields(item.getLong(ID), document, item.mergeIn(document), documents, garbageFields);

                        batch.add(Tuple.of(item.getLong(ID), item));
                    }

                    if (!batch.isEmpty())
                    {
                        db.preparedQuery(preparedQuery.toString())
                                .executeBatch(batch, asyncResult ->
                                {
                                    try
                                    {
                                        if (asyncResult.succeeded())
                                        {
                                            handler.handle(Future.succeededFuture(new JsonArray(documents)));

                                            if (!remoteIP.equalsIgnoreCase(MOTADATA_SYSTEM) && !remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                            {
                                                HAConstants.sync(table, query, document, user, remoteIP, HAConstants.HASyncOperation.UPDATE_ALL.getName(), BackupProfile.BackupProfileType.CONFIG_DB.getName());
                                            }

                                            if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                            {
                                                for (var oldItem : oldItems)
                                                {
                                                    notify(remoteIP, user, REQUEST_UPDATE, table, Boolean.TRUE, null, oldItem, document);
                                                }
                                            }
                                        }
                                        else
                                        {
                                            LOGGER.error(asyncResult.cause());

                                            handler.handle(Future.failedFuture(asyncResult.cause()));

                                            if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                            {
                                                notify(remoteIP, user, REQUEST_UPDATE, table, Boolean.FALSE, asyncResult.cause().getMessage(), null, null);
                                            }
                                        }
                                    }
                                    catch (Exception exception)
                                    {
                                        LOGGER.error(exception);

                                        handler.handle(Future.failedFuture(exception));
                                    }
                                });
                    }
                    else
                    {
                        LOGGER.debug("request received to update empty data");

                        handler.handle(Future.succeededFuture(new JsonArray(documents)));
                    }

                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    handler.handle(Future.failedFuture(exception));
                }
            }
            else
            {
                LOGGER.error(result.cause());

                handler.handle(Future.failedFuture(result.cause()));

                if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                {
                    notify(remoteIP, user, REQUEST_UPDATE, table, Boolean.FALSE, result.cause().getMessage(), null, null);
                }
            }
        });

        return this;
    }

    @Override
    public ConfigDBService compact()
    {
        try
        {
            db.query("VACUUM FULL ANALYZE;").execute(result->
            {
                if (result.succeeded())
                {
                    LOGGER.info("config db compacted successfully!");
                }
                else
                {
                    LOGGER.error(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return this;
    }

    @Override
    public void close(Handler<AsyncResult<Void>> handler)
    {
        db.close(result->
        {
            if (result.succeeded())
            {
                handler.handle(Future.succeededFuture());
            }

            else
            {
                LOGGER.error(result.cause());

                handler.handle(Future.failedFuture(result.cause()));
            }
        });
    }

    @Override
    public void backup(JsonObject context, Handler<AsyncResult<JsonObject>> handler)
    {
        vertx.<JsonObject>executeBlocking(future ->
        {
            try
            {
                var configDir = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR);

                // backup zip : ConfigDB-backup-8.0.0-timestamp.zip
                var backupDir = new File(ConfigDBConstants.CONFIG_DB_BACKUP_PATH + PATH_SEPARATOR + "ConfigDB-backup" + DASH_SEPARATOR + MotadataConfigUtil.getVersion() + DASH_SEPARATOR + context.getLong(ConfigDBConstants.BACKUP_START_TIME));

                backupDir.mkdirs();

                // copy required files from config folder to back up directory
                try
                {
                    for (var currentFile : Objects.requireNonNull(configDir.listFiles()))
                    {
                        if (!ConfigDBConstants.BACKUP_EXCLUSION_FILES.contains(currentFile.getName()) && !currentFile.getName().endsWith("position"))
                        {
                            var backupFile = new File(backupDir.getAbsolutePath() + PATH_SEPARATOR + currentFile.getName());

                            if (backupFile.createNewFile())
                            {
                                FileUtils.copyFile(currentFile, backupFile);

                                if (CommonUtil.traceEnabled())
                                {
                                    LOGGER.trace(String.format("%s file copied for backup", currentFile.getName()));
                                }

                            }
                        }
                    }

                    FileUtils.copyFileToDirectory(new File(CURRENT_DIR + PATH_SEPARATOR + VERSION_FILE), backupDir, false);
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

                // Create PostgreSQL backup using ProcessBuilder
                takePostgresBackup(backupDir.getAbsolutePath());

                if (backupDir.exists() && backupDir.length() > 0)
                {
                    var zipFile = new File(backupDir.getAbsolutePath() + ".zip");

                    ZipUtil.pack(backupDir, zipFile, false);

                    FileUtils.deleteQuietly(backupDir);

                    context.put(GlobalConstants.SRC_FILE_PATH, zipFile.getAbsolutePath());

                    context.put(DatastoreConstants.BACKUP_SIZE_BYTES, zipFile.length() / (1024 * 1024));

                    future.complete(context);
                }
                else
                {
                    future.fail("backup directory does not exist");
                }
            }
            catch (Exception exception)
            {
                future.fail(exception);

                LOGGER.error(exception);
            }

        }, true, result ->
        {
            if (result.succeeded())
            {
                handler.handle(Future.succeededFuture(result.result()));
            }
            else
            {
                handler.handle(Future.failedFuture(result.cause()));
            }
        });
    }

    private Tuple buildTuple(StringBuilder preparedQuery, JsonObject query)
    {
        Tuple vars;

        var value = query.getValue(VALUE);

        if (value instanceof JsonArray)
        {
            vars = query.getString(ConfigDBConstants.FIELD_NAME).equalsIgnoreCase(GlobalConstants.ID) ? Tuple.from(((JsonArray) value).getList()) : Tuple.from(((JsonArray) value).getList().stream().map(CommonUtil::getString).toList());

            preparedQuery.append(String.format(" IN (%s)", IntStream.range(0, ((JsonArray) value).size())
                    .mapToObj(i -> "$" + (i + 1))
                    .collect(Collectors.joining(","))));
        }
        else
        {
            vars = query.getString(ConfigDBConstants.FIELD_NAME).equalsIgnoreCase(GlobalConstants.ID) ? Tuple.of(value) : Tuple.of(value.toString());

            preparedQuery.append("= $1");
        }

        return vars;
    }

    /*
    ----------------------------- Compliance Related methods -----------------------------
     */
    @Override
    public ConfigDBService insert(String table, JsonObject document, String user, String remoteIP, Handler<AsyncResult<Long>> handler)
    {
        vertx.<Long>executeBlocking(future ->
                {
                    var id = document.containsKey(GlobalConstants.ID) ? document.getLong(GlobalConstants.ID) : CommonUtil.newId();

                    db.preparedQuery(populate(table, document)).execute(result ->
                    {
                        if (result.succeeded())
                        {
                            future.complete(id);
                        }
                        else
                        {
                            future.fail(result.cause().getMessage());
                        }
                    });
                },

                false,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));

                        if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            HAConstants.sync(table, null, document, user, remoteIP, HAConstants.HASyncOperation.SAVE.getName(), COMPLIANCE_DB);
                        }
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });

        return this;
    }

    @Override
    public ConfigDBService execute(String table, String document, String user, String remoteIP, Handler<AsyncResult<Long>> handler)
    {
        vertx.<Long>executeBlocking(future ->
                        db.preparedQuery(document).execute(result ->
                        {
                            if (result.succeeded())
                            {
                                future.complete();
                            }
                            else
                            {
                                future.fail(result.cause().getMessage());
                            }
                        }),

                false,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });

        return this;
    }

    @Override
    public ConfigDBService insertAll(String table, JsonArray documents, String user, String remoteIP, Handler<AsyncResult<Long>> handler)
    {
        vertx.<Long>executeBlocking(future ->
                        db.preparedQuery(populateBatch(table, documents)).execute(result ->
                        {
                            if (result.succeeded())
                            {
                                future.complete();
                            }
                            else
                            {
                                future.fail(result.cause().getMessage());
                            }
                        }),

                false,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));

                        if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            HAConstants.sync(table, documents, user, HAConstants.HASyncOperation.SAVE_ALL.getName(), COMPLIANCE_DB);
                        }
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });

        return this;
    }

    @Override
    public ConfigDBService select(String table, JsonObject query, Handler<AsyncResult<JsonObject>> handler)
    {
        vertx.<JsonObject>executeBlocking(future ->
                {
                    var item = new JsonObject();

                    if (query != null && !query.isEmpty())
                    {
                        db.preparedQuery(query.getString(ComplianceConstants.QUERY)).execute()
                                .onComplete(result ->
                                {
                                    if (result.succeeded())
                                    {
                                        if (!query.containsKey(ComplianceConstants.TRANSFORM) || query.getBoolean(ComplianceConstants.TRANSFORM))//fetch data in visualization format or list of result format
                                        {
                                            result.result().forEach(value -> transform(value, item));
                                        }
                                        else
                                        {
                                            var values = new JsonArray();

                                            result.result().forEach(value -> transform(value, values));

                                            if (!values.isEmpty())
                                            {
                                                item.put(RESULT, values);
                                            }
                                        }

                                        future.complete(item);
                                    }
                                    else
                                    {
                                        future.fail(result.cause().getMessage());
                                    }
                                });
                    }
                },
                false,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });
        return this;
    }

    @Override
    public ConfigDBService deleteRow(String table, JsonObject query, String user, String remoteIP, Handler<AsyncResult<JsonArray>> handler)
    {
        var items = new JsonArray();

        vertx.<JsonArray>executeBlocking(future ->
                {

                    if (!query.isEmpty() && !table.isEmpty())
                    {
                        db.preparedQuery(delete(table, query)).execute().onComplete(result ->
                        {
                            if (result.succeeded())
                            {
                                future.complete(items);
                            }
                            else
                            {
                                future.fail(result.cause().getMessage());
                            }

                        });
                    }
                },

                true,

                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));

                        if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            HAConstants.sync(table, query, null, user, remoteIP, HAConstants.HASyncOperation.DELETE.getName(), COMPLIANCE_DB);
                        }
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });

        return this;
    }

    /*
    ----------------------------- Utility methods -----------------------------
     */
    private String populate(String tableName, JsonObject document)
    {
        var query = new StringBuilder();

        var columns = new StringBuilder();

        var values = new StringBuilder();

        for (var entry : document.getMap().entrySet())
        {
            columns.append("\"").append(entry.getKey()).append("\"").append(COMMA_SEPARATOR);

            if (entry.getValue() instanceof String)
            {
                values.append("'").append(entry.getValue()).append("'").append(COMMA_SEPARATOR);
            }
            else
            {
                values.append(entry.getValue()).append(COMMA_SEPARATOR);
            }
        }

        columns.deleteCharAt(columns.length() - 1);

        values.deleteCharAt(values.length() - 1);

        return query.append("INSERT INTO ").append(tableName).append(" (").append(columns).append(") ").append(" VALUES ").append("(").append(values).append(") ").toString();
    }

    private String populateBatch(String tableName, JsonArray documents)
    {
        var query = new StringBuilder();

        try
        {
            var columns = new StringBuilder();

            var values = new StringBuilder();

            // Build column names based on the first JSON object
            var headers = documents.getJsonObject(0);

            for (var column : headers.getMap().keySet())
            {
                column = column.replace(".", "_");

                columns.append("\"").append(column).append("\", ");
            }
            // Remove trailing comma and space
            columns.delete(columns.length() - 2, columns.length());

            // Build values for each JSON object in the array
            for (var i = 0; i < documents.size(); i++)
            {
                var document = documents.getJsonObject(i);

                values.append("("); // Start row values

                for (var entry : document.getMap().entrySet())
                {
                    var value = entry.getValue();

                    // Add quotes for string values, else add raw value
                    if (value instanceof String)
                    {
                        values.append("'").append(value).append("', ");
                    }
                    else
                    {
                        values.append(value).append(", ");
                    }
                }

                // Remove trailing comma and space for this row
                values.delete(values.length() - 2, values.length());

                values.append("), "); // End row values
            }

            // Remove trailing comma and space after the last row
            values.delete(values.length() - 2, values.length());

            // Build the final INSERT query
            query.append("INSERT INTO ").append(tableName)
                    .append(" (").append(columns).append(") ")
                    .append("VALUES ").append(values);


        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return query.toString();
    }

    private String delete(String table, JsonObject condition)
    {
        var result = new StringBuilder();

        ComplianceConstants.delete(table, condition, result);

        return result.toString();
    }

    public void transform(Row row, JsonObject item)
    {
        try
        {
            for (var i = 0; i < row.size(); i++)
            {
                var column = row.getColumnName(i).replace("_", ".");

                if (item.containsKey(column))
                {
                    //As rule_status would be containing integer thus need to change it to severity data.
                    if (column.equalsIgnoreCase(ComplianceConstants.CURRENT_SCAN_STATUS) || column.equalsIgnoreCase(ComplianceConstants.LAST_SCAN_STATUS))
                    {
                        item.getJsonArray(column).add(CommonUtil.getString(ComplianceConstants.RuleStatus.valueOfName(CommonUtil.getInteger(row.getValue(i)))).toLowerCase());
                    }
                    else
                    {
                        item.getJsonArray(column).add(row.getValue(i));
                    }
                }
                else
                {
                    //As rule_status would be containing integer thus need to change it to severity data.
                    if (column.equalsIgnoreCase(ComplianceConstants.CURRENT_SCAN_STATUS) || column.equalsIgnoreCase(ComplianceConstants.LAST_SCAN_STATUS))
                    {
                        item.put(column, new JsonArray().add(CommonUtil.getString(ComplianceConstants.RuleStatus.valueOfName(CommonUtil.getInteger(row.getValue(i)))).toLowerCase()));
                    }
                    else
                    {
                        item.put(column, new JsonArray().add(row.getValue(i)));
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

    }

    public void transform(Row row, JsonArray items)
    {
        try
        {
            var item = new JsonObject();

            for (var i = 0; i < row.size(); i++)
            {
                item.put(row.getColumnName(i).replace("_", "."), row.getValue(i));
            }

            items.add(item);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

    }

    private void notify(String remoteIP, String user, String request, String collection, boolean status, String cause, JsonObject oldProps, JsonObject newProps)
    {
        var event = new JsonObject().put(REMOTE_ADDRESS, remoteIP).put(USER_NAME, user).put(ENTITY_COLLECTION, collection)
                .put(REQUEST, request).put(STATUS, status);

        if (cause != null)
        {
            event.put(ERROR, cause);
        }

        if (oldProps != null && !oldProps.isEmpty())
        {
            event.put(CONFIG_OLD_PROPS, oldProps);
        }

        if (newProps != null && !newProps.isEmpty())
        {
            event.put(CONFIG_UPDATED_PROPS, newProps);
        }

        if (!request.equalsIgnoreCase(REQUEST_DROP))
        {
            vertx.eventBus().send(EVENT_AUDIT, event);
        }
    }

    //It will remove key from item if filter value is not null.
    private void removeGarbageFields(long id, JsonObject updatedItem, JsonObject item, List<Long> documents, JsonArray garbageFields)
    {
        if (garbageFields != null)
        {
            for (var index = 0; index < garbageFields.size(); index++)
            {
                if (item.containsKey(garbageFields.getString(index)))
                {
                    item.remove(garbageFields.getString(index));

                    updatedItem.put(garbageFields.getString(index), EMPTY_VALUE);
                }
            }
        }

        documents.add(id);
    }

    /**
     * Takes a backup of PostgreSQL database using pg_dump command
     *
     * @param backupDir Directory where the backup file should be created
     * @throws Exception If the backup process fails
     */
    private void takePostgresBackup(String backupDir) throws Exception
    {
        try
        {
            var backupFile = backupDir + PATH_SEPARATOR + "backup.dump";

            var processBuilder = new ProcessBuilder();
            processBuilder.environment().put("PGPASSWORD", "TRACEorg@2025");

            var command = List.of(
                    "pg_dump",
                    "-U", "motadata",
                    "-h", "127.0.0.1",
                    "-F", "c",
                    "-f", backupFile,
                    "-d", "motadata"
            );

            processBuilder.command(command);

            var process = processBuilder.start();
            var exitCode = process.waitFor();

            if (exitCode == 0)
            {
                LOGGER.info("PostgreSQL database backup completed successfully at: " + backupFile);
            }
            else
            {
                var errorStream = new String(process.getErrorStream().readAllBytes());
                LOGGER.warn("PostgreSQL backup failed with exit code: " + exitCode + ", Error: " + errorStream);
                throw new Exception("PostgreSQL backup failed with exit code: " + exitCode);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
            throw exception;
        }
    }
}
