/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.job;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.User;
import com.mindarray.notification.Notification;
import com.mindarray.store.UserConfigStore;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.LicenseUtil;
import com.mindarray.util.Logger;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.text.StringSubstitutor;
import org.joda.time.Duration;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;

import static com.mindarray.GlobalConstants.DEFAULT_ID;


/**
 * The LicenseJob class is responsible for managing license-related operations in the Motadata platform.
 * <p>
 * This class implements the Quartz Job interface and performs daily license management tasks:
 * <ol>
 *   <li>Resetting license quotas (log and flow quotas)</li>
 *   <li>Checking license expiry and sending notifications</li>
 *   <li>Taking action when the license expires (undeploying licensed verticles)</li>
 * </ol>
 * <p>
 * The job sends notifications at specific intervals before license expiry (30, 15, 7, 3, 1, and 0 days)
 * to remind administrators to renew their license. When the license expires, it also takes action
 * to suspend monitoring functionality.
 * <p>
 * The job is scheduled to run daily at midnight (12:00 AM) to ensure timely notifications
 * and proper license management.
 */
public class LicenseJob implements Job
{
    /**
     * CRON expression for scheduling this job to run daily at midnight (12:00 AM)
     */
    public static final String LICENSE_JOB_CRON_EXPRESSION = "0 0 0 ? * * *";   // At 00:00:00am every day

    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(LicenseJob.class, GlobalConstants.MOTADATA_JOB, "License Job");

    /**
     * Email subject template for expired license notifications
     */
    private static final String EXPIRED_EMAIL_SUBJECT = "Attention – Your Motadata AIOps %s's license has expired";

    /**
     * Email subject template for expiring license notifications
     */
    private static final String EXPIRING_EMAIL_SUBJECT = "Attention – Your Motadata AIOps %s's license is expiring in %d days";

    private static final String EXPIRED_EMAIL_HTML_TEMPLATE = "<!doctype html><html><head><meta charset=\"utf-8\"><title>License Expire</title></head><body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"><table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\"border-collapse:collapse;font-family:Arial,Helvetica,sans-serif;background:#f3f6f8\"><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"40\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" width=\"650\" valign=\"top\" align=\"center\"><a href=\"https://www.motadata.com/\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px;height:25px\" src=\"cid:logo.png\" alt=\"motadata\"></a></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" valign=\"top\" align=\"center\"><table width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"padding:0 24px;border-top:4px solid #f04e3e;background:#fff\"><tbody><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0;text-align:center\"><img width=\"36\" height=\"36\" style=\"width:36px;height:36px\" src=\"cid:expired.png\" alt=\"expired\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"10\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0;text-align:center\"><h1 style=\"font-size:16px;color:#364658;font-family:Arial,Helvetica,sans-serif;margin:0;padding:0 0 5px\">License Expired</h1><p style=\"margin:0;padding:3px 0 0 0;font-size:12px;color:#9ba3ab\">License Type:<strong style=\"color:#364658\">Subscription</strong></p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"32\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><p style=\"font-size:#364658;font-size:11px;line-height:16px;padding:0;margin:0\">Hi ${user},</p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"12\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><p style=\"font-size:#364658;font-size:11px;line-height:16px;padding:0;margin:0\">We regret to inform you that your subscription for <strong>${license.edition}</strong>  has expired. This means that you are no longer able to access the industry-leading AIOps capabilities.</p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"12\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><p style=\"font-size:#364658;font-size:11px;line-height:16px;padding:0;margin:0\">We understand that the consequences of not having a valid license could be critical for your organization. That's why we strongly encourage you to renew your license as soon as possible to ensure uninterrupted monitoring of your infrastructure and continued access to our advanced AIOps capabilities.</p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"12\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><p style=\"font-size:#364658;font-size:11px;line-height:16px;padding:0;margin:0\">Renewing your license is quick and easy. Simply click on the following link to renew your license:<br><a style=\"color:#009ddc;background:#89c540;color:#fff;padding:2px 8px;display:inline-block;text-decoration:none;margin:5px 0 0 0;border-radius:2px;text-transform:uppercase\" href=\"#\">Active Now</a></p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"12\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><p style=\"font-size:#364658;font-size:11px;line-height:16px;padding:0;margin:0\">If you have any questions or need assistance renewing your license, please do not hesitate to contact us at<a style=\"color:#009ddc\" href=\"mailto:<EMAIL>\"><EMAIL></a>.</p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"12\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><p style=\"font-size:#364658;font-size:11px;line-height:16px;padding:0;margin:0\">Thank you for being a valued customer of Motadata!</p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"12\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><p style=\"font-size:#364658;font-size:11px;line-height:16px;padding:0;margin:0\">Best regards,<br>The Motadata team</p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\"><p style=\"color:#364658;font-size:10px;line-height:14px;text-align:center;margin:0;padding:0 0 5px\"><strong>Disclaimer:</strong>This is an automated message generated by Motadata.Please do not reply to this email.<br>For any queries, reach out to your System Admin</p></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td></tr></tbody></table></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr align=\"center\" ; style=\"margin:0;padding:0;text-align:center\"><td style=\"margin:0;padding:0\"><p style=\"font-size:10px;margin:0;padding:0;color:#7b8fa5\">For more details, visit<a style=\"color:#009ddc;text-decoration:none\" href=\"https://www.motadata.com/\" target=\"_blank\">www.motadata.com</a></p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr align=\"center\" style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr><td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:fb.png\" alt=\"Facebook\"></a></td><td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:twitter.png\" alt=\"Twitter\"></a></td><td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"></a></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"32\"></td></tr></table></body></html>";

    private static final String EXPIRING_EMAIL_HTML_TEMPLATE = "<!doctype html><html><head><meta charset=\"utf-8\"><title>License Expire Soon</title></head><body bgcolor=\"#F3F6F8\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\"><table width=\"100%\" bgcolor=\"#F3F6F8\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" style=\"border-collapse:collapse;font-family:Arial,Helvetica,sans-serif;background:#f3f6f8\"><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"40\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" width=\"650\" valign=\"top\" align=\"center\"><a href=\"https://www.motadata.com/\" target=\"_blank\"><img width=\"85\" height=\"25\" style=\"width:85px;height:25px\" src=\"cid:logo.png\" alt=\"motadata\"></a></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" valign=\"top\" align=\"center\"><table width=\"600\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"padding:0 24px;border-top:4px solid #f5bc18;background:#fff\"><tbody><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0;text-align:center\"><img width=\"36\" height=\"36\" style=\"width:36px;height:36px\" src=\"cid:expiring.png\" alt=\"expiring\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"10\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0;text-align:center\"><h1 style=\"font-size:16px;color:#364658;font-family:Arial,Helvetica,sans-serif;margin:0;padding:0 0 5px\">Expires in ${remaining.days} days</h1><p style=\"margin:0;padding:3px 0 0 0;font-size:12px;color:#9ba3ab\">License Type:<strong style=\"color:#364658\">Subscription</strong></p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"32\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><p style=\"font-size:#364658;font-size:11px;line-height:16px;padding:0;margin:0\">Hi ${user},</p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"12\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><p style=\"font-size:#364658;font-size:11px;line-height:16px;padding:0;margin:0\">We want to remind you that your license for <strong>${license.edition}</strong> is expiring in <strong>${remaining.days}</strong> day(s) on <strong>${expiry.date}</strong>.</p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"12\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><p style=\"font-size:#364658;font-size:11px;line-height:16px;padding:0;margin:0\">Renewing your license is quick and easy. You can renew your license by clicking on the following link:<br><a style=\"color:#009ddc;background:#89c540;color:#fff;padding:2px 8px;display:inline-block;text-decoration:none;margin:5px 0 0 0;border-radius:2px;text-transform:uppercase\" href=\"#\">Active Now</a></p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"12\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><p style=\"font-size:#364658;font-size:11px;line-height:16px;padding:0;margin:0\">Renew your license to ensure uninterrupted monitoring of your infrastructure and continue benefiting from our industry-leading AIOps capabilities.</p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"12\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><p style=\"font-size:#364658;font-size:11px;line-height:16px;padding:0;margin:0\">If you have any questions or need assistance renewing your license, please contact us at<a style=\"color:#009ddc\" href=\"mailto:<EMAIL>\"><EMAIL></a>.</p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"12\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><p style=\"font-size:#364658;font-size:11px;line-height:16px;padding:0;margin:0\">Thank you for being a valued customer of Motadata!</p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"12\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><p style=\"font-size:#364658;font-size:11px;line-height:16px;padding:0;margin:0\">Best regards,<br>The Motadata team</p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><hr style=\"border:1px solid #eef2f6;margin:0;padding:0\"></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\"><p style=\"color:#364658;font-size:10px;line-height:14px;text-align:center;margin:0;padding:0 0 5px\"><strong>Disclaimer:</strong>This is an automated message generated by Motadata.Please do not reply to this email.<br>For any queries, reach out to your System Admin</p></td></tr><tr style=\"margin:0;padding:0\"><td colspan=\"3\" style=\"margin:0;padding:0\" height=\"24\"></td></tr></tbody></table></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"24\"></td></tr><tr align=\"center\" ; style=\"margin:0;padding:0;text-align:center\"><td style=\"margin:0;padding:0\"><p style=\"font-size:10px;margin:0;padding:0;color:#7b8fa5\">For more details, visit<a style=\"color:#009ddc;text-decoration:none\" href=\"https://www.motadata.com/\" target=\"_blank\">www.motadata.com</a></p></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"16\"></td></tr><tr align=\"center\" style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\"><table width=\"102\" align=\"center\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"border-collapse:collapse;margin:0;padding:0\"><tbody><tr><td><a href=\"https://www.facebook.com/motadata\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:fb.png\" alt=\"Facebook\"></a></td><td><a href=\"https://twitter.com/MotadataSystems\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:twitter.png\" alt=\"Twitter\"></a></td><td><a href=\"https://www.linkedin.com/company/motadata/\" target=\"_blank\"><img style=\"vertical-align:middle\" src=\"cid:linkedin.png\" alt=\"Linkedin\"></a></td></tr></tbody></table></td></tr><tr style=\"margin:0;padding:0\"><td style=\"margin:0;padding:0\" height=\"32\"></td></tr></table></body></html>";

    /**
     * SMS message template for expiring license notifications
     */
    private static final String EXPIRING_SMS_SUBJECT = "Hi %s, \n\nYour Motadata license expires in %d days. For queries, please contact <NAME_EMAIL>. \n\nThank you,";

    /**
     * SMS message template for expired license notifications
     */
    private static final String EXPIRED_SMS_SUBJECT = "Hi %s, \n\nYour Motadata license has expired. To renew, contact <NAME_EMAIL>. We're happy to assist you with any questions. \n\nThank you,";

    /**
     * Executes the license management job.
     * <p>
     * This method performs the following operations:
     * <ol>
     *   <li>Resets the used log and flow quotas</li>
     *   <li>Calculates the number of days remaining until license expiry</li>
     *   <li>Sends notifications at specific intervals before expiry (30, 15, 7, 3, 1, and 0 days)</li>
     *   <li>Takes action when the license expires (undeploying licensed verticles)</li>
     * </ol>
     * <p>
     * The method ensures that administrators are notified about upcoming license expiry
     * and that appropriate actions are taken when the license expires.
     *
     * @param jobExecutionContext the context in which the job is executed
     * @throws JobExecutionException if an error occurs during job execution
     */
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException
    {
        try
        {
            LOGGER.info("executing license job...");

            // Reset the used log and flow quotas
            LOGGER.info("resetting used quota...");
            LicenseUtil.resetUsedLogQuota();
            LicenseUtil.resetUsedFlowQuota();
            LOGGER.info("Used quota reset successfully...");

            // Calculate the number of days remaining until license expiry
            var remainingDays = Duration.millis((LicenseUtil.LICENSE_EXPIRY_DATE.get() - DateTimeUtil.currentMilliSeconds())).toStandardDays().getDays();

            // If the license has already expired, set remainingDays to -1
            if (LicenseUtil.LICENSE_EXPIRY_DATE.get() - DateTimeUtil.currentMilliSeconds() < 0)
            {
                remainingDays = -1;
            }

            LOGGER.info("remaining days: " + remainingDays + "license_expire: " + LicenseUtil.LICENSE_EXPIRY_DATE.get() + "current: " + DateTimeUtil.currentMilliSeconds());

            // Send notifications at specific intervals before expiry (30, 15, 7, 3, 1, and 0 days)
            if (remainingDays == 30 || remainingDays == 15 || remainingDays == 7 || remainingDays == 3 || remainingDays == 1 || remainingDays == 0)
            {
                // Send expiring license notification
                send(false, remainingDays);
                LOGGER.warn("License Expires in " + remainingDays + " Days...!!!");
            }
            // Handle expired license
            else if (remainingDays < 0)
            {
                // Send expired license notification
                send(true, 0);
                LOGGER.fatal("License Expired...!!!");

                // Undeploy licensed verticles to suspend monitoring functionality
                Bootstrap.undeployLicensedVerticle().onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        LOGGER.warn("Monitoring has been suspended");
                    }
                    else
                    {
                        LOGGER.error(result.cause());
                    }
                });
            }

            LOGGER.info("license job executed successfully...");
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Sends license expiry notifications via email and SMS.
     * <p>
     * This method sends notifications to the default user about license expiry or upcoming expiry.
     * It formats the notification messages based on whether the license has expired or is about to expire,
     * and includes relevant information such as the license edition, remaining days, and expiry date.
     * <p>
     * The method sends both email and SMS notifications to ensure that administrators are aware
     * of the license status and can take appropriate action.
     *
     * @param expired       true if the license has expired, false if it's about to expire
     * @param remainingDays the number of days remaining until license expiry (0 if expired)
     */
    private void send(boolean expired, int remainingDays)
    {
        // Get the default user (administrator) from the store
        var user = UserConfigStore.getStore().getItem(DEFAULT_ID);

        // Format the expiry date according to the user's preferred date format
        var expiryDate = new SimpleDateFormat(
                user.getJsonObject(User.USER_PREFERENCES)
                        .getString(User.USER_PREFERENCE_DATE_TIME_FORMAT)
                        .replaceAll("ddd", "EEE")
                        .replaceAll("DD", "dd")
                        .replaceAll("A", "a"))
                .format(new Date(LicenseUtil.LICENSE_EXPIRY_DATE.get()));

        // Send email notification
        Notification.sendEmail(new JsonObject()
                .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline")
                .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray()
                        .add("expiring.png")
                        .add("expired.png")
                        .addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                .put(Notification.EMAIL_NOTIFICATION_SUBJECT,
                        expired ? String.format(EXPIRED_EMAIL_SUBJECT, LicenseUtil.LICENSE_EDITION.get().getName())
                                : String.format(EXPIRING_EMAIL_SUBJECT, LicenseUtil.LICENSE_EDITION.get().getName(), remainingDays))
                .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, new JsonArray().add(user.getString(User.USER_EMAIL)))
                .put(Notification.EMAIL_NOTIFICATION_CONTENT,
                        new StringSubstitutor(new JsonObject()
                                .put("user", user.getString(User.USER_FIRST_NAME))
                                .put("license.edition", LicenseUtil.LICENSE_EDITION.get().getName())
                                .put("remaining.days", remainingDays)
                                .put("expiry.date", expiryDate)
                                .getMap())
                                .replace(expired ? EXPIRED_EMAIL_HTML_TEMPLATE : EXPIRING_EMAIL_HTML_TEMPLATE)
                                .getBytes(StandardCharsets.UTF_8)));

        // Send SMS notification
        Notification.sendSMS(
                expired ? String.format(EXPIRED_SMS_SUBJECT, user.getString(User.USER_FIRST_NAME))
                        : String.format(EXPIRING_SMS_SUBJECT, user.getString(User.USER_NAME), remainingDays),
                new JsonArray().add(user.getLong(User.USER_MOBILE)));
    }
}
