/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  21-Mar-2025     Vismit              MOTADATA-5094: Added CONFIG_UPGRADE_OPERATION enum to JobScheduler
 *  25-Mar-2025     Smit Prajapati      MOTADATA-5435: Flow back-pressure mechanism.
 *  19-Mar-2025		Umang Sharma		Added status flap dump constant
 *  22-May-2025		Chopra Deven		MOTADATA-6234: Schedule a flow cache cleanup job only if a flow license is enabled.
 */

package com.mindarray.job;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.api.*;
import com.mindarray.compliance.ComplianceConstants;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.notification.Notification;
import com.mindarray.store.*;
import com.mindarray.util.*;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.quartz.*;
import org.quartz.Scheduler;
import org.quartz.impl.StdSchedulerFactory;
import org.quartz.impl.matchers.GroupMatcher;

import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.InfoMessageConstants.*;
import static com.mindarray.api.Discovery.*;
import static com.mindarray.api.Scheduler.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.EVENT_CONTEXT;
import static com.mindarray.eventbus.EventBusConstants.EVENT_SCHEDULER;
import static com.mindarray.nms.NMSConstants.DISCOVERED_OBJECTS;

/**
 * The JobScheduler class is responsible for managing and coordinating all scheduled jobs in the Motadata platform.
 * <p>
 * This class provides functionality for:
 * <ul>
 *   <li>Initializing the Quartz scheduler</li>
 *   <li>Scheduling system jobs that run automatically</li>
 *   <li>Scheduling custom jobs defined by users</li>
 *   <li>Managing job lifecycle (creation, execution, deletion)</li>
 *   <li>Handling job notifications</li>
 * </ul>
 * <p>
 * The JobScheduler uses the Quartz scheduler library to manage job scheduling and execution. It maintains
 * separate job groups for system jobs and custom jobs, and provides methods for scheduling, removing,
 * and triggering jobs.
 * <p>
 * This class is designed as a singleton with static methods and cannot be instantiated directly.
 */
public class JobScheduler
{
    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(JobScheduler.class, GlobalConstants.MOTADATA_JOB, "Job Scheduler");

    /**
     * Job group identifier for LDAP server synchronization jobs
     */
    private static final String LDAP_SERVER_SYNC_JOB = "ldap-server-sync-job";

    /**
     * Job group identifier for system jobs
     */
    private static final String SYSTEM_JOB = "system-job";

    /**
     * Job group identifier for custom jobs
     */
    private static final String CUSTOM_JOB = "custom-job";

    /**
     * Job group identifier for triggered custom jobs
     */
    private static final String TRIGGER_CUSTOM_JOB = "trigger-custom-job";

    /**
     * The Quartz scheduler instance
     */
    private static Scheduler scheduler = null;

    /**
     * Private constructor to prevent instantiation.
     * <p>
     * This class is designed as a singleton with static methods, so direct instantiation is not allowed.
     *
     * @throws IllegalStateException if an attempt is made to instantiate this class
     */
    private JobScheduler()
    {
        throw new IllegalStateException("Access not available");
    }

    /**
     * Schedules a custom job based on the provided context and job key.
     * <p>
     * This method parses the CRON expressions from the context and schedules a job for each valid expression.
     * Different job types may have different scheduling requirements, such as specific start dates.
     *
     * @param context the JSON object containing job configuration details
     * @param jobKey  the unique identifier for the job
     */
    private static void scheduleCustomJob(JsonObject context, String jobKey)
    {
        try
        {
            var index = 0;

            // Process each CRON expression (a job can have multiple schedules)
            for (var cronExpression : CronExpressionUtil.getCronExpression(context).split(CronExpressionUtil.CRON_SEPARATOR))
            {
                // Validate the CRON expression and ensure it has a future execution time
                if (CronExpression.isValidExpression(cronExpression) && new CronExpression(cronExpression).getNextValidTimeAfter(new Date()) != null)
                {
                    index++;

                    // Special handling for job types that require a specific start date
                    if (context.getString(SCHEDULER_JOB_TYPE).equalsIgnoreCase(JobType.MAINTENANCE.name) ||
                            context.getString(SCHEDULER_JOB_TYPE).equalsIgnoreCase(JobType.REDISCOVER.name) ||
                            context.getString(SCHEDULER_JOB_TYPE).equalsIgnoreCase(JobType.DISCOVERY.name) ||
                            context.getString(SCHEDULER_JOB_TYPE).equalsIgnoreCase(JobType.TOPOLOGY.name) ||
                            context.getString(SCHEDULER_JOB_TYPE).equalsIgnoreCase(JobType.REPORT.name) ||
                            context.getString(SCHEDULER_JOB_TYPE).equalsIgnoreCase(JobType.POLICY_SUPPRESSION.name) ||
                            context.getString(SCHEDULER_JOB_TYPE).equalsIgnoreCase(JobType.POLICY_ACTION_TRIGGER_DISABLE.name) ||
                            context.getString(SCHEDULER_JOB_TYPE).equalsIgnoreCase(JobType.EVENT_POLICY.name) ||
                            context.getString(SCHEDULER_JOB_TYPE).equalsIgnoreCase(JobType.RUNBOOK.name) ||
                            context.getString(SCHEDULER_JOB_TYPE).equalsIgnoreCase(JobType.DATABASE_BACKUP.name))
                    {
                        // Schedule job with a specific start date
                        scheduler.scheduleJob(JobBuilder.newJob(CustomJob.class).withIdentity(jobKey + "." + index, CUSTOM_JOB).usingJobData(ID, context.getLong(GlobalConstants.ID))
                                        .usingJobData(AIOpsObject.OBJECT_STATE, context.getString(AIOpsObject.OBJECT_STATE, EMPTY_VALUE))
                                        .usingJobData(USERNAME, context.containsKey(USER_NAME) ? context.getString(USER_NAME) : DEFAULT_USER)
                                        .usingJobData(REMOTE_ADDRESS, context.containsKey(REMOTE_ADDRESS) ? context.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS).build(),
                                TriggerBuilder.newTrigger().withIdentity(jobKey + "." + index + ".trigger", CUSTOM_JOB).startAt(new SimpleDateFormat("dd-MM-yyyy").parse(context.getString(SCHEDULER_START_DATE)))
                                        .withSchedule(CronScheduleBuilder.cronSchedule(cronExpression).withMisfireHandlingInstructionDoNothing()).build());
                    }
                    else
                    {
                        // Schedule job without a specific start date (starts immediately)
                        scheduler.scheduleJob(JobBuilder.newJob(CustomJob.class).withIdentity(jobKey + "." + index, CUSTOM_JOB).usingJobData(ID, context.getLong(GlobalConstants.ID))
                                        .usingJobData(AIOpsObject.OBJECT_STATE, context.getString(AIOpsObject.OBJECT_STATE, EMPTY_VALUE))
                                        .usingJobData(USERNAME, context.containsKey(USER_NAME) ? context.getString(USER_NAME) : DEFAULT_USER)
                                        .usingJobData(REMOTE_ADDRESS, context.containsKey(REMOTE_ADDRESS) ? context.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS).build(),
                                TriggerBuilder.newTrigger().withIdentity(jobKey + "." + index + ".trigger", CUSTOM_JOB)
                                        .withSchedule(CronScheduleBuilder.cronSchedule(cronExpression).withMisfireHandlingInstructionDoNothing()).build());
                    }

                    // Special handling for policy suppression jobs
                    if (context.getString(SCHEDULER_JOB_TYPE).equalsIgnoreCase(JobScheduler.JobType.POLICY_SUPPRESSION.getName()))
                    {
                        // Publish a notification that a policy has been suppressed
                        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, context.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.SUPPRESS_POLICY.name()));
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Initializes the job scheduler and sets up system and custom jobs.
     * <p>
     * This method performs the following tasks:
     * <ul>
     *   <li>Configures and starts the Quartz scheduler</li>
     *   <li>Schedules system jobs that run automatically</li>
     *   <li>Schedules custom jobs defined by users (if not running in AGENT or OBSERVER mode)</li>
     *   <li>Sets up event bus consumers for handling job completion events</li>
     * </ul>
     * <p>
     * The method also sets up notification handling for various job types, sending emails
     * or SMS messages when jobs complete.
     */
    public static void init()
    {
        try
        {
            LOGGER.info("initializing job scheduler.....");

            // Configure the Quartz scheduler thread pool size
            System.setProperty("org.quartz.threadPool.threadCount", CommonUtil.getString(MotadataConfigUtil.getJobWorkers()));

            // Enable JMX export for monitoring
            System.setProperty("org.quartz.scheduler.jmx.export", "true");

            // Create and start the scheduler
            scheduler = new StdSchedulerFactory().getScheduler();
            scheduler.start();

            LOGGER.info("job scheduler initialized successfully.... ");
            LOGGER.info("scheduling fixed jobs...");

            // Schedule system jobs that run automatically
            queueSystemJobs();

            // Only schedule custom jobs and set up event handlers if not running in AGENT or OBSERVER mode
            if (Bootstrap.bootstrapType() != BootstrapType.AGENT && Bootstrap.bootstrapType() != BootstrapType.OBSERVER)
            {
                // Schedule custom jobs defined by users
                queueCustomJobs();

                // Set up event bus consumer for handling job completion events
                Bootstrap.vertx().eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_SCHEDULER_COMPLETE, message ->
                {
                    try
                    {
                        // Validate the message contains scheduler information
                        if (message.body() != null && !message.body().isEmpty() && message.body().containsKey(EVENT_SCHEDULER))
                        {
                            var id = message.body().getLong(EVENT_SCHEDULER);
                            var scheduler = SchedulerConfigStore.getStore().getItem(id);

                            // Merge scheduler context if available
                            if (scheduler.containsKey(SCHEDULER_CONTEXT))
                            {
                                scheduler.mergeIn(scheduler.getJsonObject(SCHEDULER_CONTEXT));
                                scheduler.remove(SCHEDULER_CONTEXT);
                            }

                            var events = SchedulerCacheStore.getStore().getSchedulerEvents(id);

                            // Handle different job types differently
                            switch (JobType.valueOfName(scheduler.getString(SCHEDULER_JOB_TYPE)))
                            {
                                case DISCOVERY ->
                                {
                                    var item = DiscoveryConfigStore.getStore().getItem(scheduler.getJsonArray(NMSConstants.OBJECTS).getLong(0));

                                    // Send email notification if recipients are specified
                                    if (scheduler.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS) != null && !scheduler.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS).isEmpty())
                                    {
                                        // Build group hierarchy for the notification
                                        var groups = new JsonArray();
                                        var builder = new StringBuilder();

                                        item.getJsonArray(DISCOVERY_GROUPS).forEach(group ->
                                        {
                                            builder.setLength(0);
                                            CommonUtil.normalizeGroupHierarchy(GroupConfigStore.getStore().getItem(CommonUtil.getLong(group)), builder, null);
                                            groups.add(builder.toString());
                                        });

                                        // Send email with discovery results
                                        Notification.sendEmail(new JsonObject()
                                                .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                                                .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add("information.png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                                                .put(Notification.EMAIL_NOTIFICATION_SUBJECT, String.format(DISCOVERY_NOTIFICATION_SUBJECT, item.getString(DISCOVERY_NAME)))
                                                .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, scheduler.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS)).put(Notification.EMAIL_NOTIFICATION_CONTENT, new StringSubstitutor(new JsonObject().mergeIn(scheduler).mergeIn(item).put(TIME_STAMP, DateTimeUtil.timestamp()).put(DISCOVERY_GROUPS, StringUtils.join(groups, COMMA_SEPARATOR)).getMap()).replace(Notification.EMAIL_NOTIFICATION_DISCOVERY_HTML_TEMPLATE).getBytes(StandardCharsets.UTF_8)));
                                    }

                                    // Send SMS notification if recipients are specified
                                    if (scheduler.getJsonArray(SCHEDULER_SMS_RECIPIENTS) != null && !scheduler.getJsonArray(SCHEDULER_SMS_RECIPIENTS).isEmpty())
                                    {
                                        Notification.sendSMS(String.format(DISCOVERY_NOTIFICATION_MESSAGE, item.getString(DISCOVERY_NAME), item.getValue(DISCOVERY_TOTAL_OBJECTS), item.getValue(DISCOVERY_DISCOVERED_OBJECTS), item.getValue(DISCOVERY_FAILED_OBJECTS), item.getValue(DISCOVERY_PROGRESS)),
                                                scheduler.getJsonArray(SCHEDULER_SMS_RECIPIENTS));
                                    }

                                    // Auto-provision discovered objects if enabled
                                    if (events != null && scheduler.containsKey(NMSConstants.AUTO_PROVISION_STATUS) && scheduler.getString(NMSConstants.AUTO_PROVISION_STATUS).equalsIgnoreCase(YES))
                                    {
                                        for (var i = 0; i < events.size(); i++)
                                        {
                                            if (events.getJsonObject(i).containsKey(AIOpsObject.OBJECT_STATE) && !events.getJsonObject(i).getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase("FAILED"))
                                            {
                                                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_OBJECT_PROVISION, events.getJsonObject(i));
                                            }
                                        }
                                    }
                                }

                                case REDISCOVER ->
                                {
                                    var rediscoverObjects = scheduler.getString(NMSConstants.AUTO_PROVISION_STATUS) == null || scheduler.getString(NMSConstants.AUTO_PROVISION_STATUS).equalsIgnoreCase(NO) ? events != null ? events.size() : 0 : SchedulerCacheStore.getStore().getDiscoveredObjects(id);

                                    if (scheduler.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS) != null && !scheduler.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS).isEmpty())
                                    {
                                        Notification.sendEmail(new JsonObject()
                                                .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                                                .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add("information.png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                                                .put(Notification.EMAIL_NOTIFICATION_SUBJECT, String.format(REDISCOVERY_NOTIFICATION_SUBJECT, scheduler.getString(NMSConstants.REDISCOVER_JOB)))
                                                .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, scheduler.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS)).put(Notification.EMAIL_NOTIFICATION_CONTENT, new StringSubstitutor(new JsonObject().mergeIn(scheduler).put(TIME_STAMP, DateTimeUtil.timestamp()).put(DISCOVERED_OBJECTS, rediscoverObjects).put(STATUS, "Success").getMap()).replace(Notification.EMAIL_NOTIFICATION_REDISCOVERY_HTML_TEMPLATE).getBytes(StandardCharsets.UTF_8)));
                                    }

                                    if (scheduler.getJsonArray(SCHEDULER_SMS_RECIPIENTS) != null && !scheduler.getJsonArray(SCHEDULER_SMS_RECIPIENTS).isEmpty())
                                    {
                                        Notification.sendSMS(String.format(InfoMessageConstants.REDISCOVERY_NOTIFICATION_MESSAGE, scheduler.getString(NMSConstants.REDISCOVER_JOB), rediscoverObjects),
                                                scheduler.getJsonArray(SCHEDULER_SMS_RECIPIENTS));
                                    }
                                }

                                case MAINTENANCE ->
                                {
                                    var item = ObjectConfigStore.getStore().getItem(scheduler.getJsonArray(NMSConstants.OBJECTS).getLong(0));

                                    if (scheduler.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS) != null && !scheduler.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS).isEmpty())
                                    {
                                        Notification.sendEmail(String.format("Motadata Notification: Monitor %s Maintenance Status!!!", item.getString(AIOpsObject.OBJECT_NAME)), String.format("Monitor entered into %s state", item.getString(AIOpsObject.OBJECT_STATE)), scheduler.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS));
                                    }

                                    if (scheduler.getJsonArray(SCHEDULER_SMS_RECIPIENTS) != null && !scheduler.getJsonArray(SCHEDULER_SMS_RECIPIENTS).isEmpty())
                                    {
                                        Notification.sendSMS(String.format("Monitor entered into %s state", item.getString(AIOpsObject.OBJECT_STATE)), scheduler.getJsonArray(SCHEDULER_SMS_RECIPIENTS));
                                    }
                                }

                                case RUNBOOK ->
                                {
                                    var item = RunbookPluginConfigStore.getStore().getItem(scheduler.getJsonArray(NMSConstants.OBJECTS).getLong(0));

                                    var name = item.getString(RunbookPlugin.RUNBOOK_PLUGIN_NAME, EMPTY_VALUE);

                                    if (scheduler.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS) != null && !scheduler.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS).isEmpty())
                                    {
                                        Notification.sendEmail(new JsonObject()
                                                .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                                                .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add("information.png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                                                .put(Notification.EMAIL_NOTIFICATION_SUBJECT, String.format(RUNBOOK_EXECUTION_NOTIFICATION_SUBJECT, name))
                                                .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, scheduler.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS)).put(Notification.EMAIL_NOTIFICATION_CONTENT, new StringSubstitutor(new JsonObject().mergeIn(scheduler).mergeIn(item).put(RunbookPlugin.RUNBOOK_PLUGIN_OBJECTS, message.body().getLong(RunbookPlugin.RUNBOOK_PLUGIN_OBJECTS, 0L)).put(TIME_STAMP, DateTimeUtil.timestamp()).put(STATUS, "Success").getMap()).replace(Notification.EMAIL_NOTIFICATION_RUNBOOK_PLUGIN_TEMPLATE).getBytes(StandardCharsets.UTF_8)));
                                    }

                                    if (scheduler.getJsonArray(SCHEDULER_SMS_RECIPIENTS) != null && !scheduler.getJsonArray(SCHEDULER_SMS_RECIPIENTS).isEmpty())
                                    {
                                        Notification.sendSMS(String.format(RUNBOOK_EXECUTION_NOTIFICATION_MESSAGE, scheduler.getString(SCHEDULER_TIMELINE), name), scheduler.getJsonArray(SCHEDULER_SMS_RECIPIENTS));
                                    }
                                }

                                case COMPLIANCE_POLICY ->
                                {
                                    var event = message.body();

                                    var item = CompliancePolicyConfigStore.getStore().getItem(scheduler.getJsonArray(CompliancePolicy.COMPLIANCE_POLICY_ENTITIES).getLong(0));

                                    if (scheduler.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS) != null && !scheduler.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS).isEmpty())
                                    {
                                        Notification.sendEmail(new JsonObject()
                                                .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                                                .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add("information.png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                                                .put(Notification.EMAIL_NOTIFICATION_SUBJECT, String.format(COMPLIANCE_POLICY_NOTIFICATION_SUBJECT_SUCCESSFUL, item.getString(CompliancePolicy.COMPLIANCE_POLICY_NAME)))
                                                .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, scheduler.getJsonArray(SCHEDULER_EMAIL_RECIPIENTS)).put(Notification.EMAIL_NOTIFICATION_CONTENT, new StringSubstitutor(new JsonObject().put(CompliancePolicy.COMPLIANCE_POLICY_NAME, item.getString(CompliancePolicy.COMPLIANCE_POLICY_NAME)).put(ComplianceBenchmark.COMPLIANCE_BENCHMARK_NAME, ComplianceBenchmarkConfigStore.getStore().getItem(item.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).getLong(CompliancePolicy.COMPLIANCE_POLICY_BENCHMARK)).getString(ComplianceBenchmark.COMPLIANCE_BENCHMARK_NAME)).put(CompliancePolicy.COMPLIANCE_POLICY_CONFIG_FILE_TYPE, item.getJsonObject(CompliancePolicy.COMPLIANCE_POLICY_CONTEXT).getString(CompliancePolicy.COMPLIANCE_POLICY_CONFIG_FILE_TYPE)).put(ComplianceConstants.ComplianceState.VULNERABLE.name().toLowerCase(), event.getInteger(ComplianceConstants.ComplianceState.VULNERABLE.name().toLowerCase())).put(ComplianceConstants.ComplianceState.POOR.name().toLowerCase(), event.getInteger(ComplianceConstants.ComplianceState.POOR.name().toLowerCase())).put(ComplianceConstants.ComplianceState.MODERATE.name().toLowerCase(), event.getInteger(ComplianceConstants.ComplianceState.MODERATE.name().toLowerCase())).put(ComplianceConstants.ComplianceState.SECURE.name().toLowerCase(), event.getInteger(ComplianceConstants.ComplianceState.SECURE.name().toLowerCase())).put(ComplianceConstants.COMPLIANCE_PERCENTAGE, event.getInteger(ComplianceConstants.COMPLIANCE_PERCENTAGE)).put(USER_NAME, item.getString(USER_NAME, DEFAULT_USER)).put(TIME_STAMP, DateTimeUtil.timestamp()).put(STATUS, "Success").getMap()).replace(Notification.EMAIL_NOTIFICATION_COMPLIANCE_POLICY_SUCCESSFUL_HTML_TEMPLATE).getBytes(StandardCharsets.UTF_8)));
                                    }

                                    if (scheduler.getJsonArray(SCHEDULER_SMS_RECIPIENTS) != null && !scheduler.getJsonArray(SCHEDULER_SMS_RECIPIENTS).isEmpty())
                                    {
                                        Notification.sendSMS(String.format(COMPLIANCE_POLICY_NOTIFICATION_SUCCESSFUL_MESSAGE, event.getString(CompliancePolicy.COMPLIANCE_POLICY_NAME), DateTimeUtil.timestamp(), event.getString(USER_NAME, DEFAULT_USER), event.getInteger(ComplianceConstants.COMPLIANCE_PERCENTAGE), event.getInteger(ComplianceConstants.ComplianceState.VULNERABLE.name().toLowerCase()), event.getInteger(ComplianceConstants.ComplianceState.POOR.name().toLowerCase()), event.getInteger(ComplianceConstants.ComplianceState.MODERATE.name().toLowerCase()), event.getInteger(ComplianceConstants.ComplianceState.SECURE.name().toLowerCase())), scheduler.getJsonArray(SCHEDULER_SMS_RECIPIENTS));
                                    }
                                }

                                default ->
                                {
                                }
                            }

                            if (events != null)
                            {
                                Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_SCHEDULER,
                                        new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, id),
                                        new JsonObject().put(GlobalConstants.RESULT, events),
                                        SchedulerCacheStore.getStore().getSchedulerUser(id),
                                        SYSTEM_REMOTE_ADDRESS,
                                        result ->
                                        {
                                            if (result.succeeded())
                                            {
                                                SchedulerConfigStore.getStore().updateItem(id);

                                                SchedulerCacheStore.getStore().clearSchedulerContext(id);
                                            }
                                        });
                            }
                        }
                        else
                        {
                            LOGGER.warn("invalid event received " + message.body());
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                });
            }

            LOGGER.info("fixed job scheduled successfully...");
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            LOGGER.fatal("failed to initialize job scheduler...");
        }

    }

    /**
     * Deletes schedulers associated with a specific object when that object is deleted.
     * <p>
     * When an object (discovery, runbook, report, etc.) is deleted, this method is called to
     * delete any schedulers that were created for that object. It removes the scheduler entries
     * from the database and cancels the scheduled jobs.
     *
     * @param event   the JSON object containing information about the deleted object
     * @param jobType the type of job associated with the deleted object
     */
    public static void deleteSchedulers(JsonObject event, String jobType)
    {
        // Once object/discovery/runbook/report deleted motadata will check if any scheduler created on it than it will delete that schedulers as well as remove cron job for it.
        var schedulers = SchedulerConfigStore.getStore().getItems(com.mindarray.api.Scheduler.SCHEDULER_CONTEXT, NMSConstants.OBJECTS, new JsonArray(new ArrayList(1)).add(event.getLong(ID)), jobType);

        if (!schedulers.isEmpty())
        {
            // Delete scheduler entries from the database
            Bootstrap.configDBService().deleteAll(ConfigDBConstants.COLLECTION_SCHEDULER,
                    new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, schedulers),
                    DEFAULT_USER, REMOTE_ADDRESS,
                    result ->
                    {
                        if (result.succeeded())
                        {
                            // Remove schedulers from the store
                            SchedulerConfigStore.getStore().deleteItems(schedulers);

                            // Remove each scheduled job
                            for (var index = 0; index < schedulers.size(); index++)
                            {
                                JobScheduler.removeJob(schedulers.getLong(index));
                            }
                        }
                    });
        }
    }

    /**
     * Schedules all custom jobs defined in the scheduler configuration store.
     * <p>
     * This method retrieves all custom job configurations from the store and schedules
     * them using the {@link #scheduleCustomJob(JsonObject)} method. It is called during
     * system initialization to restore scheduled jobs after a restart.
     */
    private static void queueCustomJobs()
    {
        try
        {
            if (scheduler != null)
            {
                // Schedule all custom jobs from the configuration store
                SchedulerConfigStore.getStore().flatMap().values().forEach(JobScheduler::scheduleCustomJob);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Schedules all system jobs that run automatically.
     * <p>
     * This method schedules various system maintenance jobs with their respective CRON expressions.
     * Different jobs are scheduled depending on the bootstrap type (AGENT, OBSERVER, or SERVER).
     * <p>
     * System jobs include:
     * <ul>
     *   <li>Daily miscellaneous jobs (log retention, event ID reset, etc.)</li>
     *   <li>LDAP synchronization</li>
     *   <li>License management</li>
     *   <li>DNS cache flushing</li>
     *   <li>Report cleanup</li>
     *   <li>Geographical database synchronization</li>
     *   <li>Datastore retention</li>
     *   <li>Flap cache backup</li>
     *   <li>System notifications</li>
     *   <li>Flow cache cleanup</li>
     * </ul>
     */
    private static void queueSystemJobs()
    {
        try
        {
            if (scheduler != null)
            {
                // Schedule daily miscellaneous jobs (runs in all bootstrap types)
                scheduler.scheduleJob(JobBuilder.newJob(DailyMiscJobs.class).withIdentity("daily-misc-jobs", SYSTEM_JOB).build(),
                        TriggerBuilder.newTrigger().withIdentity("daily-misc-jobs-trigger", SYSTEM_JOB)
                                .withSchedule(CronScheduleBuilder.cronSchedule(DailyMiscJobs.DAILY_MISC_JOBS_CRON_EXPRESSION)).build());

                // Additional system jobs only for SERVER mode (not for AGENT or OBSERVER)
                if (Bootstrap.bootstrapType() != BootstrapType.AGENT && Bootstrap.bootstrapType() != BootstrapType.OBSERVER)
                {
                    // Schedule LDAP synchronization jobs
                    queueLDAPSyncJob(); // MOTADATA-2596

                    // Schedule license management job
                    scheduler.scheduleJob(JobBuilder.newJob(LicenseJob.class).withIdentity("license-job", SYSTEM_JOB).build(),
                            TriggerBuilder.newTrigger().withIdentity("license-trigger", SYSTEM_JOB)
                                    .withSchedule(CronScheduleBuilder.cronSchedule(LicenseJob.LICENSE_JOB_CRON_EXPRESSION)).build());

                    // Schedule DNS cache flush job
                    scheduler.scheduleJob(JobBuilder.newJob(DNSCacheFlushJob.class).withIdentity("dns-cache-flush-job", SYSTEM_JOB).build(),
                            TriggerBuilder.newTrigger().withIdentity("dns-cache-flush-trigger", SYSTEM_JOB)
                                    .withSchedule(CronScheduleBuilder.cronSchedule(DNSCacheFlushJob.DNS_CACHE_FLUSH_JOB_CRON_EXPRESSION)).build());

                    // Schedule report cleanup job
                    scheduler.scheduleJob(JobBuilder.newJob(ReportCleanupJob.class).withIdentity("report-cleanup-job", SYSTEM_JOB).build(),
                            TriggerBuilder.newTrigger().withIdentity("report-cleanup-trigger", SYSTEM_JOB)
                                    .withSchedule(CronScheduleBuilder.cronSchedule(ReportCleanupJob.REPORT_CLEANUP_JOB_CRON_EXPRESSION)).build());

                    // Schedule geographical database synchronization job
                    scheduler.scheduleJob(JobBuilder.newJob(GeoDBSyncJob.class).withIdentity("geo-db-sync-job", SYSTEM_JOB).build(),
                            TriggerBuilder.newTrigger().withIdentity("geo-db-sync-trigger", SYSTEM_JOB)
                                    .withSchedule(CronScheduleBuilder.cronSchedule(GeoDBSyncJob.GEO_DB_SYNC_JOB_CRON_EXPRESSION)).build());

                    // Schedule datastore retention job
                    scheduler.scheduleJob(JobBuilder.newJob(DatastoreRetentionJob.class).withIdentity("datastore-retention-job", SYSTEM_JOB).build(),
                            TriggerBuilder.newTrigger().withIdentity("datastore-retention-trigger", SYSTEM_JOB)
                                    .withSchedule(CronScheduleBuilder.cronSchedule(DatastoreRetentionJob.DATASTORE_RETENTION_JOB_CRON_EXPRESSION)).build());

                    // Schedule flap cache backup job
                    scheduler.scheduleJob(JobBuilder.newJob(FlapCacheBackupJob.class).withIdentity("flap-cache-backup-job", SYSTEM_JOB).build(),
                            TriggerBuilder.newTrigger().withIdentity("flap-cache-backup-trigger", SYSTEM_JOB)
                                    .withSchedule(CronScheduleBuilder.cronSchedule(FlapCacheBackupJob.FLAP_CACHE_BACKUP_RETENTION_JOB_CRON_EXPRESSION)).build());

                    // Schedule system notification job
                    scheduler.scheduleJob(JobBuilder.newJob(SystemNotificationJob.class).withIdentity("notification-job", SYSTEM_JOB).build(),
                            TriggerBuilder.newTrigger().withIdentity("notification-trigger", SYSTEM_JOB)
                                    .withSchedule(CronScheduleBuilder.cronSchedule(SystemNotificationJob.NOTIFICATION_JOB_CRON_EXPRESSION)).build());

                    //  Schedule a flow cache cleanup job only if a flow license is enabled...
                    if (LicenseUtil.FLOW_ENABLED.get())
                    {
                        // Schedule flow cache cleanup job
                        scheduler.scheduleJob(JobBuilder.newJob(FlowCacheCleanupJob.class).withIdentity("flow-cache-cleanup-job", SYSTEM_JOB).build(),
                                TriggerBuilder.newTrigger().withIdentity("flow-cache-cleanup-trigger", SYSTEM_JOB)
                                        .withSchedule(CronScheduleBuilder.cronSchedule(FlowCacheCleanupJob.FLOW_CACHE_CLEANUP_JOB_CRON_EXPRESSION)).build());
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Queues LDAP synchronization jobs for all LDAP servers in the configuration store.
     * <p>
     * This method is called during system initialization to set up LDAP synchronization
     * jobs for all configured LDAP servers. It's particularly important after a server
     * restart to ensure that LDAP synchronization continues.
     */
    private static void queueLDAPSyncJob()
    {
        LOGGER.info("queuing LDAP sync job");

        // Schedule LDAP sync job for each LDAP server in the configuration store
        LDAPServerConfigStore.getStore().flatMap().values().forEach(item -> scheduleLDAPSyncJob(item, item.getLong(ID)));
    }

    /**
     * Checks if a job with the specified key and group is already scheduled.
     * <p>
     * This method searches through all jobs in the specified job group to find
     * a job with the given key. It's used to prevent duplicate job scheduling.
     *
     * @param jobKey   the key of the job to check
     * @param jobGroup the group of the job to check
     * @return true if a job with the specified key and group is already scheduled, false otherwise
     */
    private static boolean jobScheduled(String jobKey, String jobGroup)
    {
        var result = false;

        try
        {
            if (scheduler != null)
            {
                // Get all job keys in the specified group
                for (var key : scheduler.getJobKeys(GroupMatcher.jobGroupEquals(jobGroup)))
                {
                    // Check if the job key matches the specified key
                    if (key.getName().trim().equalsIgnoreCase(jobKey))
                    {
                        result = true;
                        break;
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }

    /**
     * Removes all jobs whose keys start with the specified prefix.
     * <p>
     * This method is used to remove all jobs related to a specific job key prefix.
     * It's typically used when a job needs to be rescheduled or when an object
     * associated with multiple jobs is deleted.
     *
     * @param jobKey the prefix of the job keys to remove
     */
    private static void removeJob(String jobKey)
    {
        try
        {
            if (scheduler != null)
            {
                // Get all job keys in the custom job group
                for (var key : scheduler.getJobKeys(GroupMatcher.jobGroupEquals(CUSTOM_JOB)))
                {
                    // Remove jobs whose keys start with the specified prefix
                    if (key.getName().trim().startsWith(jobKey))
                    {
                        removeJob(key);
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Removes all jobs associated with the specified ID.
     * <p>
     * This method is used to remove all jobs related to a specific object ID.
     * It's typically called when an object is deleted or when its jobs need
     * to be rescheduled.
     *
     * @param id the ID of the object whose jobs should be removed
     */
    public static void removeJob(long id)
    {
        try
        {
            if (scheduler != null)
            {
                // Get all job keys in the custom job group
                for (var key : scheduler.getJobKeys(GroupMatcher.jobGroupEquals(CUSTOM_JOB)))
                {
                    // Extract the ID from the job key and compare it with the specified ID
                    if (CommonUtil.getLong(key.getName().trim().split("\\.")[1]) == id)
                    {
                        removeJob(key);
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Removes a job with the specified job key.
     * <p>
     * This method is the lowest-level job removal method, directly deleting
     * a job from the scheduler using its Quartz JobKey.
     *
     * @param jobKey the Quartz JobKey of the job to remove
     */
    static void removeJob(JobKey jobKey)
    {
        try
        {
            if (scheduler != null)
            {
                // Delete the job from the scheduler
                scheduler.deleteJob(jobKey);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Triggers immediate execution of a job with the specified context.
     * <p>
     * This method creates a durable job (one that persists even without triggers)
     * and then immediately triggers its execution. It's used for running jobs
     * on-demand rather than according to a schedule.
     *
     * @param context the JSON object containing job configuration details
     */
    public static void triggerJob(JsonObject context)
    {
        try
        {
            if (scheduler != null)
            {
                // Create a job key for the triggered job
                var jobKey = JobKey.jobKey(CommonUtil.getString(context.getLong(GlobalConstants.ID)), TRIGGER_CUSTOM_JOB);

                // Add a durable job to the scheduler
                scheduler.addJob(JobBuilder.newJob(CustomJob.class).storeDurably()
                        .withIdentity(jobKey)
                        .usingJobData(APIConstants.SESSION_ID, context.getString(APIConstants.SESSION_ID))
                        .usingJobData("job.type", "custom")
                        .usingJobData(USERNAME, context.getString(USER_NAME))
                        .usingJobData(ID, context.getLong(GlobalConstants.ID)).build(), true);

                // Trigger immediate execution of the job
                scheduler.triggerJob(jobKey);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Schedules a custom job based on the provided context.
     * <p>
     * This method handles different job types differently. For maintenance jobs,
     * it schedules multiple jobs based on the event context. For other job types,
     * it schedules a single job.
     * <p>
     * Before scheduling a new job, it removes any existing jobs with the same key
     * to prevent duplicate scheduling.
     *
     * @param context the JSON object containing job configuration details
     */
    public static void scheduleCustomJob(JsonObject context)
    {
        try
        {
            if (scheduler != null && context != null && !context.isEmpty())
            {
                var jobType = context.getString(SCHEDULER_JOB_TYPE);
                String jobKey;

                // Special handling for maintenance jobs
                if (jobType.equalsIgnoreCase(JobType.MAINTENANCE.getName()))
                {
                    // Maintenance jobs have multiple event contexts, each requiring a separate job
                    var schedulerParams = context.getJsonObject(SCHEDULER_CONTEXT).getJsonArray(EVENT_CONTEXT);

                    for (var i = 0; i < schedulerParams.size(); i++)
                    {
                        var params = schedulerParams.getJsonObject(i);

                        // Create a unique job key for each maintenance state
                        jobKey = jobType + "." + context.getLong(GlobalConstants.ID) + "." + params.getString(AIOpsObject.OBJECT_STATE);

                        // Remove any existing job with this key
                        removeJob(jobKey);

                        // Merge the parameters into the context
                        context.mergeIn(params);

                        // Schedule the job
                        scheduleCustomJob(context, jobKey);
                    }
                }
                else
                {
                    // For other job types, create a single job key
                    jobKey = jobType + "." + context.getLong(GlobalConstants.ID);

                    // Remove any existing job with this key
                    removeJob(jobKey);

                    // Schedule the job
                    scheduleCustomJob(context, jobKey);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Schedules an LDAP synchronization job for a specific LDAP server.
     * <p>
     * This method first deletes any existing LDAP sync job for the specified server,
     * then schedules a new job if auto-sync is enabled in the context. The job's
     * schedule is determined by the sync interval specified in the context.
     *
     * @param context the JSON object containing LDAP server details
     * @param id      the ID of the LDAP server
     */
    public static void scheduleLDAPSyncJob(JsonObject context, long id)
    {
        try
        {
            // Create a job key for the LDAP sync job
            var jobKey = LDAP_SERVER_SYNC_JOB + "." + id;

            // Delete any existing job with this key
            if (jobScheduled(jobKey, SYSTEM_JOB))
            {
                scheduler.deleteJob(new JobKey(jobKey, SYSTEM_JOB));
            }

            // Schedule a new job if auto-sync is enabled and an interval is specified
            if (context.getString(LDAPServer.LDAP_AUTO_SYNC).equalsIgnoreCase(YES) &&
                    context.containsKey(LDAPServer.LDAP_SYNC_JOB_SCHEDULE_INTERVAL) &&
                    context.getInteger(LDAPServer.LDAP_SYNC_JOB_SCHEDULE_INTERVAL) != null)
            {
                // Get the sync interval (default to 8 hours if not specified)
                var interval = context.getInteger(LDAPServer.LDAP_SYNC_JOB_SCHEDULE_INTERVAL, 8);

                // Schedule the job with the appropriate CRON expression
                scheduler.scheduleJob(JobBuilder.newJob(LDAPSyncJob.class).withIdentity(jobKey, SYSTEM_JOB).usingJobData(ID, id).build(),
                        TriggerBuilder.newTrigger().withIdentity(jobKey + ".trigger", SYSTEM_JOB)
                                .withSchedule(CronScheduleBuilder.cronSchedule(
                                        // If interval < 23 hours, schedule every 'interval' hours
                                        // Otherwise, schedule every 'interval/24' days
                                        interval < 23 ? LDAPSyncJob.LDAP_SYNC_JOB_CRON_EXPRESSION
                                                .replaceAll("@@", CommonUtil.getString(interval).trim())
                                                .replaceAll("##", "1")
                                                : LDAPSyncJob.LDAP_SYNC_JOB_CRON_EXPRESSION.replaceAll("@@", "0")
                                                .replaceAll("##", CommonUtil.getString(interval / 24)))).build());
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Deletes the LDAP synchronization job for a specific LDAP server.
     * <p>
     * This method removes any scheduled LDAP synchronization job associated with
     * the specified LDAP server ID. It's typically called when an LDAP server
     * is deleted or when its synchronization settings are changed.
     *
     * @param id the ID of the LDAP server whose synchronization job should be deleted
     */
    public static void deleteLDAPSyncJob(long id)
    {
        try
        {
            // Create the job key for the LDAP sync job
            var jobKey = LDAP_SERVER_SYNC_JOB + "." + id;

            // Delete the job if it exists
            if (jobScheduled(jobKey, SYSTEM_JOB))
            {
                scheduler.deleteJob(new JobKey(jobKey, SYSTEM_JOB));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Shuts down the job scheduler.
     * <p>
     * This method clears all scheduled jobs and shuts down the Quartz scheduler.
     * It's typically called during system shutdown to ensure a clean termination
     * of all scheduled jobs.
     */
    public static void shutdownJobScheduler()
    {
        try
        {
            LOGGER.info("stopping job scheduler...");

            if (scheduler != null)
            {
                // Clear all scheduled jobs
                scheduler.clear();

                // Shut down the scheduler without waiting for jobs to complete
                scheduler.shutdown(false);

                // Set the scheduler reference to null
                scheduler = null;

                LOGGER.info("job scheduler successfully stopped...");
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Updates the status of schedulers associated with a specific object.
     * <p>
     * This method is called when the status of an object (like a discovery profile,
     * runbook, etc.) changes. It updates the status of all schedulers associated
     * with that object to match the new status.
     * <p>
     * For example, if a discovery profile is disabled, this method will disable
     * all schedulers associated with that profile.
     *
     * @param event   the JSON object containing the new status and object ID
     * @param jobType the type of job associated with the object
     */
    public static void updateSchedulerStatus(JsonObject event, String jobType)
    {
        try
        {
            // Ensure the event contains the necessary fields
            if (event.containsKey(SCHEDULER_STATE) && event.containsKey(ID))
            {
                var state = event.getString(SCHEDULER_STATE);

                // Get all schedulers associated with the object
                var items = SchedulerConfigStore.getStore().getItems(SCHEDULER_CONTEXT, NMSConstants.OBJECTS, new JsonArray(new ArrayList(1)).add(event.getLong(ID)), jobType);

                if (items != null && !items.isEmpty())
                {
                    // Update the status of all schedulers in the database
                    Bootstrap.configDBService().updateAll(ConfigDBConstants.COLLECTION_SCHEDULER,
                            new JsonObject().put(ConfigDBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, items),
                            new JsonObject().put(SCHEDULER_STATE, state),
                            event.containsKey(USER_NAME) ? event.getString(USER_NAME) : DEFAULT_USER,
                            event.containsKey(REMOTE_ADDRESS) ? event.getString(REMOTE_ADDRESS) : SYSTEM_REMOTE_ADDRESS,
                            result ->
                            {
                                if (result.succeeded())
                                {
                                    // Update the schedulers in the store
                                    SchedulerConfigStore.getStore().updateItems(items);
                                }
                                else
                                {
                                    LOGGER.warn("failed to update scheduler, reason > " + result.cause().getMessage());
                                }
                            });
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Enumeration of job types supported by the JobScheduler.
     * <p>
     * This enum defines all the different types of jobs that can be scheduled in the Motadata platform.
     * Each job type has a display name that is used in the UI and logs.
     */
    public enum JobType
    {
        /**
         * Jobs for putting objects into maintenance mode and taking them out of maintenance
         */
        MAINTENANCE("Maintenance"),

        /**
         * Jobs for running discovery profiles to find new devices and services
         */
        DISCOVERY("Discovery Profile"),

        /**
         * Jobs for rediscovering existing objects to update their configuration
         */
        REDISCOVER("Rediscover"),

        /**
         * Jobs for generating network topology maps
         */
        TOPOLOGY("Topology"),

        /**
         * Jobs for compacting the configuration database
         */
        CONFIG_DB_COMPACT("Config DB Compact"),

        /**
         * Jobs for enforcing system log retention policies
         */
        SYSTEM_LOG_RETENTION("System Log Retention"),

        /**
         * Jobs for resetting license quota counters
         */
        LICENSE_QUOTA_RESET("License Quota Reset"),

        /**
         * Jobs for suppressing policy notifications
         */
        POLICY_SUPPRESSION("Policy Suppression"),

        /**
         * Jobs for disabling policy action triggers
         */
        POLICY_ACTION_TRIGGER_DISABLE("Policy Action Trigger Disable"),

        /**
         * Jobs for executing event policies
         */
        EVENT_POLICY("Event Policy"),

        /**
         * Jobs for executing runbooks (automated workflows)
         */
        RUNBOOK("Runbook"),

        /**
         * Jobs for generating reports
         */
        REPORT("Report"),

        /**
         * Jobs for backing up databases
         */
        DATABASE_BACKUP("Database Backup"),

        /**
         * Jobs for backing up configuration files
         */
        CONFIG_BACKUP_OPERATION("Config Backup Ops"),

        /**
         * Jobs for checking compliance policies
         */
        COMPLIANCE_POLICY("Compliance Policy"),

        /**
         * Jobs for upgrading configuration files
         */
        CONFIG_UPGRADE_OPERATION("Config Upgrade Ops");

        /**
         * Map for efficient lookup of job types by name
         */
        private static final Map<String, JobType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(JobType::getName, e -> e)));

        /**
         * The display name of the job type
         */
        private final String name;

        /**
         * Constructor for JobType enum.
         *
         * @param name the display name of the job type
         */
        JobType(String name)
        {
            this.name = name;
        }

        /**
         * Gets a JobType enum value by its display name.
         *
         * @param name the display name of the job type
         * @return the JobType enum value, or null if no matching job type is found
         */
        public static JobType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        /**
         * Gets the display name of the job type.
         *
         * @return the display name of the job type
         */
        public String getName()
        {
            return name;
        }
    }
}
