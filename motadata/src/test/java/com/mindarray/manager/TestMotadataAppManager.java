/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.manager;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.TestUtil;
import com.mindarray.api.*;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.ha.HAConstants;
import com.mindarray.ha.HAManager;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.runbook.Runbook;
import com.mindarray.util.*;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.DeploymentOptions;
import io.vertx.core.Vertx;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.zeromq.SocketType;
import org.zeromq.ZMQ;
import org.zeroturnaround.zip.ZipUtil;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.zip.ZipFile;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.agent.AgentConstants.AGENT;
import static com.mindarray.api.Agent.AGENT_FILE;
import static com.mindarray.api.BackupProfile.BACKUP_PROFILE_TYPE;
import static com.mindarray.db.ConfigDBConstants.BACKUP_FILE;
import static com.mindarray.db.ConfigDBConstants.CONFIG_DB_BACKUP_PATH;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.manager.MotadataAppManager.PATCH_ARTIFACT_FILE;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(100 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "MANAGER")
public class TestMotadataAppManager
{
    private static final ZMQ.Socket SUBSCRIBER = Bootstrap.zcontext().socket(SocketType.PULL);
    private static final AtomicBoolean HAS_MORE_EVENTS = new AtomicBoolean(true);
    private static final Logger LOGGER = new Logger(MotadataAppManager.class, GlobalConstants.MOTADATA_APP_MANAGER, "Test Manager");
    private static final Vertx VERTX = TestUtil.vertx();
    private static final String REGISTRATION_ID = Bootstrap.getRegistrationId();
    private static final JsonObject agentContext = new JsonObject().put(Agent.AGENT_UUID, REGISTRATION_ID).put(SYSTEM_BOOTSTRAP_TYPE, GlobalConstants.BootstrapType.AGENT.name());
    private static final String SYSTEM_VERSION = MotadataConfigUtil.getVersion();
    private static final JsonObject restoreContext = new JsonObject()
            .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_DATABASE_RESTORE)
            .put(VERSION, SYSTEM_VERSION)
            .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, REGISTRATION_ID)
            .put(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.APP.name());
    private static final String CONFIG_DIRECTORY = GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + PATH_SEPARATOR;
    private static MessageConsumer<JsonObject> messageConsumer;
    private static File restoreFile;
    private static String restoreFileVersion;
    private String fileName = EMPTY_VALUE;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        try
        {
            var currentConfigFile = new File(CURRENT_DIR + PATH_SEPARATOR + "motadata");

            var renamedConfigFile = new File(CURRENT_DIR + PATH_SEPARATOR + "motadata-1");

            if (renamedConfigFile.exists())
            {
                renamedConfigFile.renameTo(currentConfigFile);
            }

            var configs = readConfigs();

            configs.put("motadata.process.detection.attempts", 100);

            VERTX.fileSystem().writeFileBlocking(CONFIG_DIRECTORY + "motadata.json", Buffer.buffer(Json.encodePrettily(configs)));

            MotadataConfigUtil.loadConfigs(configs);

            subscribe(MotadataConfigUtil.getMotadataManagerEventPublisherPort());

            TimeUnit.SECONDS.sleep(15);    // wait for manager to start agent

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            testContext.failNow(exception.getMessage());
        }
    }

    private static JsonObject readConfigs()
    {
        return new JsonObject().mergeIn(VERTX.fileSystem().readFileBlocking(CONFIG_DIRECTORY + "motadata.json").toJsonObject());
    }

    private static void subscribe(int port)
    {
        SUBSCRIBER.setHWM(MotadataConfigUtil.getEventBacklogSize());

        SUBSCRIBER.setRcvHWM(MotadataConfigUtil.getEventBacklogSize());

        SUBSCRIBER.bind("tcp://*:" + port);

        LOGGER.info(String.format("bind to port %s", port));

        new Thread(() ->
        {
            while (HAS_MORE_EVENTS.get())
            {
                try
                {
                    var bytes = SUBSCRIBER.recv();

                    if (bytes != null && bytes.length > 0)
                    {
                        var buffer = Buffer.buffer(bytes);

                        var topic = buffer.getString(2, buffer.getShortLE(0) + 2);

                        LOGGER.info(String.format("event received for the topic : %s ", topic));

                        if (topic.contains(MOTADATA_MANAGER_TOPIC))
                        {
                            var event = new JsonObject(buffer.getBuffer(2 + buffer.getShortLE(0), buffer.length()));

                            LOGGER.info(String.format("event received for motadata.manager:%s", event));

                            VERTX.eventBus().send("test.manager", event);
                        }
                    }

                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }
        }).start();
    }

    @AfterEach
    void afterEach(VertxTestContext testContext)
    {
        if (restoreFile != null)
        {
            var file = new File(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + restoreFile.getName() + "-11");

            if (file.exists())
            {
                Assertions.assertTrue(file.renameTo(new File(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + restoreFile.getName())));
            }
        }

        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testMotadataManagerHeartbeat(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        messageConsumer = VERTX.eventBus().localConsumer("test.manager", message ->
        {
            var event = message.body();

            LOGGER.trace(event);

            if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_ACKNOWLEDGEMENT))
            {
                if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                {
                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow(String.format("Failed to get manager heartbeat %s", event.getString(MESSAGE, EMPTY_VALUE)));
                }
            }
        });

        VERTX.eventBus().send(EVENT_REMOTE, agentContext.put(EVENT_TYPE, EVENT_MOTADATA_MANAGER_HEARTBEAT).put(Agent.AGENT_DISABLE, YES));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testAgentStop(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        messageConsumer = VERTX.eventBus().localConsumer("test.manager", message ->
        {
            var event = message.body();

            LOGGER.trace(event);

            if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_STOP))
            {
                if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                {
                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow(String.format("Failed to stop agent %s", event.getString(MESSAGE, EMPTY_VALUE)));
                }
            }
        });

        VERTX.eventBus().send(EVENT_REMOTE, agentContext.put(EVENT_TYPE, EventBusConstants.EVENT_AGENT_STOP).put(Agent.AGENT_DISABLE, YES));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testAgentStart(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        messageConsumer = VERTX.eventBus().localConsumer("test.manager", message ->
        {
            var event = message.body();

            LOGGER.trace(event);

            if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_START))
            {
                if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                {
                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow(String.format("Failed to start agent %s", event.getString(MESSAGE, EMPTY_VALUE)));
                }
            }
        });

        VERTX.eventBus().send(EVENT_REMOTE, agentContext.put(EVENT_TYPE, EventBusConstants.EVENT_AGENT_START));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testAgentAlreadyRunning(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        messageConsumer = VERTX.eventBus().localConsumer("test.manager", message ->
        {
            var event = message.body();

            LOGGER.trace(event);

            if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_START))
            {
                if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                {
                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow(String.format("Failed to start agent %s", event.getString(MESSAGE, EMPTY_VALUE)));
                }
            }
        });

        VERTX.eventBus().send(EVENT_REMOTE, agentContext.put(EVENT_TYPE, EventBusConstants.EVENT_AGENT_START));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testAgentRestart(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        messageConsumer = VERTX.eventBus().localConsumer("test.manager", message ->
        {
            var event = message.body();

            LOGGER.trace(event);

            if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_RESTART))
            {
                if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                {
                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow(String.format("Failed to restart agent %s", event.getString(MESSAGE, EMPTY_VALUE)));
                }
            }
        });

        VERTX.eventBus().send(EVENT_REMOTE, agentContext.put(EVENT_TYPE, EVENT_AGENT_RESTART));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testAgentDisable(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        messageConsumer = VERTX.eventBus().localConsumer("test.manager", message ->
        {
            var event = message.body();

            LOGGER.trace(event);

            if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_DISABLE))
            {
                if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                {
                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow(String.format("Failed to start disable %s", event.getString(MESSAGE, EMPTY_VALUE)));
                }
            }
        });

        VERTX.eventBus().send(EVENT_REMOTE, agentContext.put(EVENT_TYPE, EVENT_AGENT_DISABLE));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testDisabledAgentStart(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        messageConsumer = VERTX.eventBus().localConsumer("test.manager", message ->
        {
            var event = message.body();

            LOGGER.trace(event);

            if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_START) && (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_FAIL)))
            {
                Assertions.assertTrue(event.getString(MESSAGE).contains(DISABLED));

                testContext.completeNow();
            }
        });

        VERTX.eventBus().send(EVENT_REMOTE, agentContext.put(EVENT_TYPE, EventBusConstants.EVENT_AGENT_START));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testAgentEnable(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        messageConsumer = VERTX.eventBus().localConsumer("test.manager", message ->
        {
            var event = message.body();

            LOGGER.trace(event);

            if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_ENABLE))
            {
                if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                {
                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow(String.format("Failed to enable agent %s", event.getString(MESSAGE, EMPTY_VALUE)));
                }
            }
        });

        VERTX.eventBus().send(EVENT_REMOTE, agentContext.put(EVENT_TYPE, EVENT_AGENT_ENABLE));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testAgentUpgrade(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        VERTX.deployVerticle("com.mindarray.api.APIServer", future ->
        {
            if (future.succeeded())
            {
                LOGGER.info("HTTP server started successfully...");

                AgentConfigUtil.getAgentConfigs().put("http.server.port", MotadataConfigUtil.getHTTPServerPort(BootstrapType.APP.name()));

                Bootstrap.getDeployedVerticles().put(APIServer.class.getSimpleName(), future.result());

                TestUtil.createZipFile(new String[]{"upgrade1.json", "upgrade2.json", "VERSION"}, CURRENT_DIR + PATH_SEPARATOR + "uploads" + PATH_SEPARATOR + "agent-upgrade.zip", "linux", VERTX.fileSystem().readFileBlocking(CURRENT_DIR + PATH_SEPARATOR + VERSION_FILE).toString());

                if (new File(CURRENT_DIR + PATH_SEPARATOR + "uploads" + PATH_SEPARATOR + "agent-upgrade.zip").exists())
                {
                    var progress = new AtomicReference<>(-1.0f);

                    messageConsumer = VERTX.eventBus().localConsumer("test.manager", message ->
                    {
                        var event = message.body();

                        LOGGER.trace(event);

                        if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_UPGRADE))
                        {
                            if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                            {
                                LOGGER.debug(event.getString(PROGRESS));

                                Assertions.assertTrue(CommonUtil.getFloat(event.getValue(PROGRESS)) >= progress.get());

                                progress.set(CommonUtil.getFloat(event.getValue(PROGRESS)));

                                if (progress.get() == 100.0 && event.containsKey(PORT))
                                {
                                    testContext.completeNow();
                                }
                            }
                            else
                            {
                                testContext.failNow(String.format("Failed to upgrade agent %s", event.getString(MESSAGE, EMPTY_VALUE)));
                            }
                        }
                    });

                    VERTX.eventBus().send(EVENT_REMOTE, agentContext.put(EventBusConstants.EVENT_TYPE, EVENT_AGENT_UPGRADE)
                            .put(PATCH_ARTIFACT_FILE, "agent-upgrade.zip"));
                }
                else
                {
                    testContext.failNow("Unable to create agent-upgrade zip file");
                }
            }
            else
            {
                testContext.failNow(future.cause().getMessage());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testAgentUpgradeInvalidPort(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var progress = new AtomicReference<>(-1.0f);

        AgentConfigUtil.getAgentConfigs().put("http.server.port", 8080);

        messageConsumer = VERTX.eventBus().localConsumer("test.manager", message ->
        {
            var event = message.body();

            LOGGER.trace(event);

            if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_UPGRADE))
            {
                if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                {
                    LOGGER.debug(CommonUtil.getFloat(event.getValue(PROGRESS)));

                    Assertions.assertTrue(CommonUtil.getFloat(event.getValue(PROGRESS)) >= progress.get());

                    progress.set(CommonUtil.getFloat(event.getValue(PROGRESS)));
                }
                else if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_FAIL))
                {
                    Assertions.assertTrue(event.getString(MESSAGE).contains("Connection refused"));

                    testContext.completeNow();
                }
            }
        });

        VERTX.eventBus().send(EVENT_REMOTE, agentContext.put(EventBusConstants.EVENT_TYPE, EVENT_AGENT_UPGRADE)
                .put(PATCH_ARTIFACT_FILE, "agent-upgrade.zip"));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testAgentUpgradeInvalidZipFile(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var progress = new AtomicReference<>(-1.0f);

        AgentConfigUtil.getAgentConfigs().put("http.server.port", MotadataConfigUtil.getHTTPServerPort(BootstrapType.APP.name()));

        messageConsumer = VERTX.eventBus().localConsumer("test.manager", message ->
        {
            var event = message.body();

            LOGGER.trace(event);

            if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_UPGRADE))
            {
                if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                {
                    LOGGER.debug(CommonUtil.getFloat(event.getValue(PROGRESS)));

                    Assertions.assertTrue(CommonUtil.getFloat(event.getValue(PROGRESS)) >= progress.get());

                    progress.set(CommonUtil.getFloat(event.getValue(PROGRESS)));
                }
                else if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_FAIL))
                {
                    LOGGER.debug(event.getString(MESSAGE));

                    testContext.completeNow();
                }
            }
        });

        VERTX.eventBus().send(EVENT_REMOTE, agentContext.put(EventBusConstants.EVENT_TYPE, EVENT_AGENT_UPGRADE)
                .put(PATCH_ARTIFACT_FILE, "logo.png"));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testAgentDelete(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        messageConsumer = VERTX.eventBus().localConsumer("test.manager", message ->
        {
            var event = message.body();

            LOGGER.trace(event);

            if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_AGENT_DELETE))
            {
                if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                {
                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow(String.format("Failed to delete agent %s", event.getString(MESSAGE, EMPTY_VALUE)));
                }
            }
        });

        VERTX.eventBus().send(EVENT_REMOTE, agentContext.put(EVENT_TYPE, EVENT_AGENT_DELETE));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    @Timeout(300 * 1000)
    void testMotadataManagerUpgrade(VertxTestContext testContext, TestInfo testInfo) throws IOException, InterruptedException
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var upgradeMeFile = new File(CURRENT_DIR + PATH_SEPARATOR + "upgrade.me");

        upgradeMeFile.createNewFile();

        TestUtil.createZipFile(new String[]{"upgrade1.json", "upgrade2.json", "VERSION", "upgrade.me"}, CURRENT_DIR + PATH_SEPARATOR + "uploads" + PATH_SEPARATOR + "manager-upgrade.zip", "linux", VERTX.fileSystem().readFileBlocking(CURRENT_DIR + PATH_SEPARATOR + VERSION_FILE).toString());

        upgradeMeFile.delete();

        VERTX.eventBus().send(EVENT_REMOTE, restoreContext.put(EventBusConstants.EVENT_TYPE, EVENT_MANAGER_UPGRADE)
                .put(PATCH_ARTIFACT_FILE, "manager-upgrade.zip"));

        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        Assertions.assertTrue(upgradeMeFile.exists());

        Assertions.assertFalse(FileUtils.readFileToString(upgradeMeFile, StandardCharsets.UTF_8).trim().isEmpty());

        Assertions.assertEquals(1, FileUtils.readFileToString(upgradeMeFile, StandardCharsets.UTF_8).split(":").length);

        testContext.completeNow();
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testUndeployAPIServer(VertxTestContext testContext, TestInfo testInfo) throws IOException
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var configs = AgentConfigUtil.getChildAgentConfigs();

        configs.getJsonObject(AGENT).put(Agent.AGENT_DELETION_STATUS, NO);

        CommonUtil.dumpConfigs(AGENT_FILE, configs);

        Assertions.assertTrue(Bootstrap.getDeployedVerticles().containsKey(APIServer.class.getSimpleName()));

        Assertions.assertFalse(Bootstrap.getDeployedVerticles().get(APIServer.class.getSimpleName()).isEmpty());

        VERTX.undeploy(Bootstrap.getDeployedVerticles().get(APIServer.class.getSimpleName()));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testConfigDBLOCALBackup(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

            var configDir = new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR);

            var backupDir = new File(ConfigDBConstants.CONFIG_DB_BACKUP_PATH);

            backupDir.mkdir();

            FileUtils.copyFileToDirectory(new File(CURRENT_DIR + PATH_SEPARATOR + VERSION_FILE), configDir);

            var zipFile = new File(configDir.getAbsolutePath() + PATH_SEPARATOR + "config.zip");

            ZipUtil.pack(configDir, zipFile, false);

            FileUtils.copyFileToDirectory(zipFile, backupDir);

            fileName = backupDir + PATH_SEPARATOR + zipFile.getName();

            FileUtils.deleteQuietly(zipFile);

            FileUtils.deleteQuietly(new File(configDir.getAbsolutePath() + PATH_SEPARATOR + VERSION_FILE));

            var file = new File(CURRENT_DIR + "/config-db-backups-LOCAL");

            file.mkdir();

            LOGGER.info(String.format("sending event to manager , zip file %s", fileName));

            VERTX.eventBus().send(EVENT_REMOTE, new JsonObject().put(EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC)
                    .put(EVENT_TYPE, EVENT_DATABASE_BACKUP).put(DELETE_SRC_FILE, YES)
                    .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                    .put(BACKUP_PROFILE_TYPE, BackupProfile.BackupProfileType.CONFIG_DB.getName()).put(SRC_FILE_PATH, fileName)
                    .put(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.APP.name()).put(EVENT_TYPE, EVENT_DATABASE_BACKUP)
                    .put(StorageProfile.STORAGE_PROFILE_PROTOCOL, StorageProfile.StorageProtocol.LOCAL.getName())
                    .put(STORAGE_PATH, file.getAbsolutePath()));

            messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.manager", message ->
            {
                var event = message.body();

                LOGGER.trace(event);

                if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_DATABASE_BACKUP))
                {
                    if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                    {
                        testContext.completeNow();
                    }
                    else
                    {
                        testContext.failNow(String.format("Failed to take backup, reason : %s", event.getString(MESSAGE, EMPTY_VALUE)));
                    }
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            testContext.failNow(exception.getMessage());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testInvalidConfigDBLOCALBackup(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        VERTX.eventBus().send(EVENT_REMOTE, new JsonObject()
                .put(EVENT_TYPE, EVENT_DATABASE_BACKUP).put(DELETE_SRC_FILE, YES)
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                .put(SRC_FILE_PATH, CURRENT_DIR + "/invalid.zip")
                .put(BACKUP_PROFILE_TYPE, BackupProfile.BackupProfileType.CONFIG_DB.getName())
                .put(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.APP.name())
                .put(EVENT_TYPE, EVENT_DATABASE_BACKUP).put(StorageProfile.STORAGE_PROFILE_PROTOCOL, StorageProfile.StorageProtocol.LOCAL.getName()));

        messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.manager", message ->
        {
            var event = message.body();

            LOGGER.trace(event);

            if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_DATABASE_BACKUP))
            {
                if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_FAIL))
                {
                    Assertions.assertTrue(event.containsKey(ERRORS));

                    Assertions.assertFalse(event.getJsonArray(ERRORS).isEmpty());

                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow(String.format("Failed get backup result %s", event.getString(MESSAGE, EMPTY_VALUE)));
                }
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testConfigDBFTPBackup(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        VERTX.eventBus().send(EVENT_REMOTE, new JsonObject()
                .put(EVENT_TYPE, EVENT_DATABASE_BACKUP).put(DELETE_SRC_FILE, YES)
                .put(BACKUP_PROFILE_TYPE, BackupProfile.BackupProfileType.CONFIG_DB.getName()).put(SRC_FILE_PATH, fileName)
                .put(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.APP.name())
                .put(RunbookPlugin.RUNBOOK_PLUGIN_NAME, StorageProfile.StorageProtocol.FTP.getName())
                .put(RunbookPlugin.RUNBOOK_PLUGIN_TYPE, "Custom Script")
                .put(RunbookPlugin.RUNBOOK_PLUGIN_CATEGORY, Runbook.RunbookCategory.STORAGE_PROFILE.getName())
                .put(PluginEngineConstants.SCRIPT, "package main\n\nimport (\n\t\"encoding/base64\"\n\t\"encoding/json\"\n\t\"fmt\"\n\t\"motadatasdk/consts\"\n\t. \"motadatasdk/motadatatypes\"\n\t\"motadatasdk/utils\"\n\t\"os\"\n)\n\nfunc main() {\n\n\tcontext, err := utils.LoadPluginContext(os.Args[2:][0])\n\n\tif err != nil {\n\n\t\tbytes, _ := json.Marshal(MotadataMap{\n\n\t\t\tconsts.Status: consts.StatusFail,\n\n\t\t\tconsts.Errors: []MotadataStringMap{\n\t\t\t\t{\n\t\t\t\t\tconsts.ErrorCode: consts.ErrorCodeInternalError,\n\t\t\t\t\tconsts.Error:     fmt.Sprintf(\"%v\", err),\n\t\t\t\t\tconsts.Message:   \"Failed to load context\",\n\t\t\t\t}},\n\t\t})\n\n\t\tfmt.Println(base64.StdEncoding.EncodeToString(bytes) + consts.BlankString)\n\n\t} else {\n\t\t\n\t\tcontext[consts.Status] = consts.StatusSucceed\n\t\t\n\t\tbytes, err := json.Marshal(context)\n\n\t\tif err != nil {\n\n\t\t\tcontext[consts.Status] = consts.StatusFail\n\n\t\t\terrors := []MotadataStringMap{\n\t\t\t\t{\n\t\t\t\t\tconsts.ErrorCode: consts.ErrorCodeInternalError,\n\t\t\t\t\tconsts.Error:     fmt.Sprintf(\"%v\", err),\n\t\t\t\t\tconsts.Message:   \"Error occurred while marshalling Result\",\n\t\t\t\t}}\n\n\t\t\tcontext[consts.Errors] = append(context.GetStringMapSliceValue(consts.Errors), errors...)\n\t\t}\n\n\t\tfmt.Println(base64.StdEncoding.EncodeToString(bytes) + consts.BlankString)\n\t}\n}")
                .put(PluginEngineConstants.SCRIPT_LANGUAGE, "go")
                .put(GlobalConstants.TIME, 60)
                .put(StorageProfile.STORAGE_PROFILE_PROTOCOL, StorageProfile.StorageProtocol.FTP.getName())
                .put(STORAGE_PATH, "/home/<USER>/test_FTP/")
                .put(AIOpsObject.OBJECT_TARGET, "************")
                .put(USERNAME, "motadata").put(PASSWORD, "motadata")
                .put(PORT, 21));

        messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.manager", message ->
        {
            var event = message.body();

            LOGGER.trace(event);

            if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_DATABASE_BACKUP))
            {
                if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                {
                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow(String.format("Failed to take backup, reason : %s", event.getString(MESSAGE, EMPTY_VALUE)));
                }
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testInvalidConfigDBFTPBackup(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        VERTX.eventBus().send(EVENT_REMOTE, new JsonObject()
                .put(EVENT_TYPE, EVENT_DATABASE_BACKUP).put(DELETE_SRC_FILE, YES)
                .put(BACKUP_PROFILE_TYPE, BackupProfile.BackupProfileType.CONFIG_DB.getName()).put(SRC_FILE_PATH, fileName)
                .put(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.APP.name())
                .put(RunbookPlugin.RUNBOOK_PLUGIN_NAME, StorageProfile.StorageProtocol.FTP.getName())
                .put(RunbookPlugin.RUNBOOK_PLUGIN_TYPE, "Custom Script")
                .put(RunbookPlugin.RUNBOOK_PLUGIN_CATEGORY, Runbook.RunbookCategory.STORAGE_PROFILE.getName())
                .put(PluginEngineConstants.SCRIPT, EMPTY_VALUE)
                .put(PluginEngineConstants.SCRIPT_LANGUAGE, "go")
                .put(GlobalConstants.TIME, 60)
                .put(StorageProfile.STORAGE_PROFILE_PROTOCOL, StorageProfile.StorageProtocol.FTP.getName())
                .put(STORAGE_PATH, "/home/<USER>/test_FTP/")
                .put(AIOpsObject.OBJECT_TARGET, "************")
                .put(USERNAME, "motadata").put(PASSWORD, "motadata")
                .put(PORT, 21));

        messageConsumer = TestUtil.vertx().eventBus().localConsumer("test.manager", message ->
        {
            var event = message.body();

            LOGGER.trace(event);

            if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_DATABASE_BACKUP))
            {
                if (event.containsKey(STATUS) && event.getString(STATUS).equalsIgnoreCase(STATUS_FAIL))
                {
                    testContext.completeNow();
                }
                else
                {
                    testContext.failNow(event.getString(STATUS));
                }
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    @Timeout(300 * 1000)
    void testRestartManager(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        HAS_MORE_EVENTS.set(false);

        SUBSCRIBER.close();

        Assertions.assertTrue(CommonUtil.isNotNullOrEmpty(Bootstrap.getDeployedVerticles().get(MotadataAppManager.class.getSimpleName())), "manager verticle not deployed");

        TestUtil.vertx().undeploy(Bootstrap.getDeployedVerticles().get(MotadataAppManager.class.getSimpleName()), result ->
        {
            try
            {
                if (result.succeeded())
                {
                    LOGGER.info("Manager verticle undeployed successfully!");

                    var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + "motadata.json");

                    var configs = new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8));

                    MotadataConfigUtil.loadConfigs(configs.put(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.APP.name()));

                    TestUtil.vertx().deployVerticle(new MotadataAppManager(), new DeploymentOptions().setConfig(new JsonObject().put(EVENT_TYPE, EVENT_REMOTE))).onComplete(asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            try
                            {
                                Bootstrap.getDeployedVerticles().put(MotadataAppManager.class.getSimpleName(), asyncResult.result());

                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(150), timer ->
                                {
                                    LOGGER.info("Manager verticle deployed successfully!");

                                    testContext.completeNow();
                                });

                            }
                            catch (Exception exception)
                            {
                                testContext.failNow(exception.getMessage());
                            }
                        }
                        else
                        {
                            testContext.failNow(asyncResult.cause());
                        }
                    });
                }
                else
                {
                    testContext.failNow(result.cause().getMessage());
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception.getMessage());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testGetRestoreFile(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        try
        {
            var files = new File(CONFIG_DB_BACKUP_PATH + "-LOCAL").listFiles();

            Assertions.assertNotNull(files);

            if (files.length > 0)
            {
                restoreFile = files[0];
            }

            Assertions.assertNotNull(restoreFile);

            FileUtils.copyFileToDirectory(restoreFile, new File(CURRENT_DIR + PATH_SEPARATOR + UPLOADS), false);

            try (var file = new ZipFile(restoreFile))
            {
                var reader = new BufferedReader(new InputStreamReader(file.getInputStream(file.getEntry("VERSION"))));

                var line = reader.readLine();

                if (CommonUtil.isNotNullOrEmpty(line))
                {
                    restoreFileVersion = line.trim();
                }

                reader.close();
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }

            Assertions.assertNotNull(restoreFileVersion);

            Assertions.assertFalse(restoreFileVersion.isEmpty());

            testContext.completeNow();
        }
        catch (Exception exception)
        {
            testContext.failNow(exception.getMessage());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testConfigDBRestoreInvalidVersion(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        try
        {
            var progress = new AtomicReference<>(-1.0f);

            restoreContext.put(BACKUP_FILE, restoreFile.getName());

            restoreContext.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_DATABASE_RESTORE);

            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_UI, message ->
                    testContext.verify(() ->
                    {
                        try
                        {
                            if (message.body().getBinary(EVENT_CONTEXT) != null)
                            {
                                var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                                LOGGER.trace(eventContext);

                                if (eventContext.containsKey(EVENT_TYPE) && eventContext.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_DATABASE_RESTORE) && eventContext.containsKey(PROGRESS) && eventContext.containsKey(STATUS))
                                {
                                    if (eventContext.containsKey(STATUS) && eventContext.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                                    {
                                        LOGGER.debug(CommonUtil.getFloat(eventContext.getValue(PROGRESS)));

                                        Assertions.assertTrue(CommonUtil.getFloat(eventContext.getValue(PROGRESS)) > progress.get(), "Progress mismatch");

                                        progress.set(CommonUtil.getFloat(eventContext.getValue(PROGRESS)));
                                    }
                                    else if (eventContext.getString(STATUS).equalsIgnoreCase(STATUS_FAIL) && eventContext.containsKey(MESSAGE))
                                    {
                                        Assertions.assertTrue(eventContext.getString(MESSAGE).equalsIgnoreCase("failed to verify artifacts"));

                                        restoreContext.put(VERSION, SYSTEM_VERSION);

                                        testContext.completeNow();
                                    }
                                }
                            }
                        }
                        catch (Exception exception)
                        {
                            testContext.failNow(exception.getMessage());
                        }
                    }));

            VERTX.eventBus().send(EVENT_REMOTE, restoreContext.put(VERSION, "0.0.0"));

            sendWebsocketClientRegistrationEvent();
        }
        catch (Exception exception)
        {
            testContext.failNow(exception.getMessage());
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    @Timeout(500 * 1000)
    void testConfigDBRestoreInvalidCurrentConfig(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        var progress = new AtomicReference<>(-1.0f);

        var originalConfig = new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR);

        var renamedConfig = new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + "-1");

        if (renamedConfig.exists())
        {
            FileUtils.deleteQuietly(renamedConfig);
        }

        Assertions.assertTrue(originalConfig.renameTo(renamedConfig), "Failed to rename config to config-1");

        restoreContext.put(BACKUP_FILE, restoreFile.getName());

        messageConsumer = VERTX.eventBus().localConsumer(EVENT_UI, message ->
        {
            try
            {
                if (message.body().getBinary(EVENT_CONTEXT) != null)
                {
                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    LOGGER.trace(eventContext);

                    if (eventContext.containsKey(EVENT_TYPE) && eventContext.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_DATABASE_RESTORE) && eventContext.containsKey(PROGRESS) && eventContext.containsKey(STATUS))
                    {
                        if (eventContext.containsKey(STATUS) && eventContext.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                        {
                            LOGGER.debug(CommonUtil.getFloat(eventContext.getValue(PROGRESS)));

                            Assertions.assertTrue(CommonUtil.getFloat(eventContext.getValue(PROGRESS)) > progress.get());

                            progress.set(CommonUtil.getFloat(eventContext.getValue(PROGRESS)));
                        }
                        else if (eventContext.getString(STATUS).equalsIgnoreCase(STATUS_FAIL) && eventContext.containsKey(MESSAGE))
                        {
                            Assertions.assertTrue(eventContext.getString(MESSAGE).contains("failed to backup"));

                            if (renamedConfig.exists())
                            {
                                LOGGER.info("renaming config-1 to config");

                                Assertions.assertTrue(renamedConfig.renameTo(originalConfig), "Fail to rename the config-1 to config");
                            }

                            if (eventContext.containsKey(PORT))
                            {
                                testContext.completeNow();
                            }
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception.getMessage());
            }

        });

        VERTX.eventBus().send(EVENT_REMOTE, restoreContext.put(VERSION, SYSTEM_VERSION));

        sendWebsocketClientRegistrationEvent();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    @Timeout(500 * 1000)
    void testConfigDBRestoreInvalidRestoreFileName(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var progress = new AtomicReference<>(-1.0f);

        restoreContext.put(BACKUP_FILE, restoreFile.getName());

        messageConsumer = VERTX.eventBus().localConsumer(EVENT_UI, message ->
        {
            try
            {
                if (message.body().getBinary(EVENT_CONTEXT) != null)
                {
                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    LOGGER.trace(eventContext);

                    if (eventContext.containsKey(EVENT_TYPE) && eventContext.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_DATABASE_RESTORE) && eventContext.containsKey(PROGRESS) && eventContext.containsKey(STATUS))
                    {
                        if (eventContext.containsKey(STATUS) && eventContext.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                        {
                            LOGGER.debug(CommonUtil.getFloat(eventContext.getValue(PROGRESS)));

                            Assertions.assertTrue(CommonUtil.getFloat(eventContext.getValue(PROGRESS)) > progress.get());

                            if (CommonUtil.getFloat(eventContext.getValue(PROGRESS)) == 10.0)
                            {
                                LOGGER.debug("Renaming the restore zip");

                                Assertions.assertTrue(new File(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + restoreFile.getName()).renameTo(new File(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + restoreFile.getName() + "-11")));
                            }

                            progress.set(CommonUtil.getFloat(eventContext.getValue(PROGRESS)));
                        }
                        else if (eventContext.getString(STATUS).equalsIgnoreCase(STATUS_FAIL) && eventContext.containsKey(MESSAGE))
                        {
                            Assertions.assertTrue(eventContext.getString(MESSAGE).equalsIgnoreCase("failed to restore confing, Recovering old config and starting motadata service"));

                            if (eventContext.containsKey(PORT))
                            {
                                testContext.completeNow();
                            }
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception.getMessage());
            }

        });

        VERTX.eventBus().send(EVENT_REMOTE, restoreContext);

        sendWebsocketClientRegistrationEvent();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    @Timeout(700 * 1000)
    void testConfigDBRestoreInvalidStartMotadata(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var progress = new AtomicReference<>(-1.0f);

        var currentConfigFile = new File(CURRENT_DIR + PATH_SEPARATOR + "motadata");

        var renamedConfigFile = new File(CURRENT_DIR + PATH_SEPARATOR + "motadata-1");

        Assertions.assertTrue(currentConfigFile.renameTo(renamedConfigFile), "Failed to rename motadata to motadata-1");

        restoreContext.put(BACKUP_FILE, restoreFile.getName());

        messageConsumer = VERTX.eventBus().localConsumer(EVENT_UI, message ->
        {
            try
            {
                if (message.body().getBinary(EVENT_CONTEXT) != null)
                {
                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    LOGGER.trace(eventContext);

                    if (eventContext.containsKey(EVENT_TYPE) && eventContext.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_DATABASE_RESTORE) && eventContext.containsKey(PROGRESS) && eventContext.containsKey(STATUS))
                    {
                        if (eventContext.containsKey(STATUS) && eventContext.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                        {
                            Assertions.assertTrue(CommonUtil.getFloat(eventContext.getValue(PROGRESS)) > progress.get());

                            LOGGER.debug(CommonUtil.getFloat(eventContext.getValue(PROGRESS)));

                            progress.set(CommonUtil.getFloat(eventContext.getValue(PROGRESS)));
                        }
                        else if (eventContext.getString(STATUS).equalsIgnoreCase(STATUS_FAIL) && eventContext.containsKey(MESSAGE))
                        {
                            Assertions.assertTrue(eventContext.getString(MESSAGE).contains("failed to start"));

                            Assertions.assertTrue(eventContext.getString(MESSAGE).contains("Recovering old config"));

                            if (renamedConfigFile.exists())
                            {
                                Assertions.assertTrue(renamedConfigFile.renameTo(currentConfigFile));
                            }

                            if (progress.get() == 100.0 && eventContext.containsKey(PORT))
                            {
                                testContext.completeNow();
                            }
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception.getMessage());
            }

        });

        VERTX.eventBus().send(EVENT_REMOTE, restoreContext);

        sendWebsocketClientRegistrationEvent();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    @Timeout(700 * 1000)
    void testConfigDBRestore(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var progress = new AtomicReference<>(-1.0f);

        restoreContext.put(BACKUP_FILE, restoreFile.getName());

        messageConsumer = VERTX.eventBus().localConsumer(EVENT_UI, message ->
        {
            try
            {
                if (message.body().getBinary(EVENT_CONTEXT) != null)
                {
                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    LOGGER.trace(eventContext);

                    if (eventContext.containsKey(EVENT_TYPE) && eventContext.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_DATABASE_RESTORE) && eventContext.containsKey(PROGRESS) && eventContext.containsKey(STATUS) && (eventContext.containsKey(STATUS) && eventContext.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED)))
                    {
                        LOGGER.debug(CommonUtil.getFloat(eventContext.getValue(PROGRESS)));

                        Assertions.assertTrue(CommonUtil.getFloat(eventContext.getValue(PROGRESS)) >= progress.get());

                        progress.set(CommonUtil.getFloat(eventContext.getValue(PROGRESS)));

                        if (progress.get() == 100.0 && eventContext.containsKey(PORT))
                        {
                            testContext.completeNow();
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception.getMessage());
            }

        });

        VERTX.eventBus().send(EVENT_REMOTE, restoreContext);

        sendWebsocketClientRegistrationEvent();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    @Timeout(700 * 1000)
    void testMotadataUpgrade(VertxTestContext testContext, TestInfo testInfo) throws IOException
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + "motadata.json");

        var configs = new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8));

        MotadataConfigUtil.loadConfigs(configs.put(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.APP.name()));

        VERTX.fileSystem().writeFileBlocking(CONFIG_DIRECTORY + "motadata.json", Buffer.buffer(Json.encodePrettily(configs)));

        TestUtil.createZipFile(new String[]{"upgrade1.json", "upgrade2.json", "VERSION"}, CURRENT_DIR + PATH_SEPARATOR + "uploads" + PATH_SEPARATOR + "master-upgrade.zip", "linux", VERTX.fileSystem().readFileBlocking(CURRENT_DIR + PATH_SEPARATOR + VERSION_FILE).toString());

        var progress = new AtomicReference<>(-1.0f);

        messageConsumer = VERTX.eventBus().localConsumer(EVENT_UI, message ->
        {
            try
            {
                if (message.body().getBinary(EVENT_CONTEXT) != null)
                {
                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    LOGGER.trace(eventContext);

                    if (eventContext.containsKey(EVENT_TYPE) && eventContext.getString(EVENT_TYPE).equalsIgnoreCase(EVENT_MASTER_UPGRADE) && eventContext.containsKey(PROGRESS) && eventContext.containsKey(STATUS) && (eventContext.containsKey(STATUS) && eventContext.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED)))
                    {
                        LOGGER.debug(CommonUtil.getFloat(eventContext.getValue(PROGRESS)));

                        Assertions.assertTrue(CommonUtil.getFloat(eventContext.getValue(PROGRESS)) >= progress.get());

                        progress.set(CommonUtil.getFloat(eventContext.getValue(PROGRESS)));

                        if (progress.get() == 100.0 && eventContext.containsKey(PORT))
                        {
                            testContext.completeNow();
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception.getMessage());
            }

        });

        VERTX.eventBus().send(EVENT_REMOTE, restoreContext.put(EventBusConstants.EVENT_TYPE, EVENT_MASTER_UPGRADE)
                .put(PATCH_ARTIFACT_FILE, "master-upgrade.zip"));

        sendWebsocketClientRegistrationEvent();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    @Timeout(700 * 1000)
    void testRestartManagerToSecondary(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        TestUtil.vertx().undeploy(Bootstrap.getDeployedVerticles().get(MotadataAppManager.class.getSimpleName()), result ->
        {
            try
            {
                if (result.succeeded())
                {
                    LOGGER.info("Manager verticle undeployed successfully!");

                    var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + "motadata.json");

                    var configs = new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8));

                    configs.put(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.APP.name());

                    configs.put(INSTALLATION_MODE, InstallationMode.SECONDARY.name());

                    configs.put("vip.ip", "************");

                    configs.put("vip.command", "sudo ifconfig ens160:2 %s netmask ************* %s");

                    configs.put(INSTALLATION_TYPE, 1);

                    MotadataConfigUtil.loadConfigs(configs);

                    TestUtil.vertx().fileSystem().writeFileBlocking(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "motadata.json", Buffer.buffer(Json.encodePrettily(configs)));

                    TestUtil.vertx().deployVerticle(new MotadataAppManager(), new DeploymentOptions().setConfig(new JsonObject().put(EVENT_TYPE, EVENT_REMOTE)));

                    try
                    {
                        TimeUnit.SECONDS.sleep(50); // wait for manager to start default and agent exe

                        LOGGER.info("Manager verticle deployed successfully!");

                        testContext.completeNow();
                    }
                    catch (Exception exception)
                    {
                        testContext.failNow(exception.getMessage());
                    }
                }
                else
                {
                    testContext.failNow(result.cause().getMessage());
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception.getMessage());
            }
        });

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    @Timeout(700 * 1000)
    void testMotadataSwitchOverEngineSecondaryToPrimary(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        TestUtil.vertx().setTimer(120 * 1000, id ->
        {
            var retries = new AtomicInteger();

            TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(2), timer ->
            {
                try
                {
                    var configs = new JsonObject(FileUtils.readFileToString(new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "motadata.json")));

                    LOGGER.info("configs: " + configs.encode());

                    if (configs.getString(INSTALLATION_MODE).equalsIgnoreCase(InstallationMode.PRIMARY.name()))
                    {
                        TestUtil.vertx().cancelTimer(timer);

                        assertTrue(configs.getString(INSTALLATION_MODE).equalsIgnoreCase(InstallationMode.PRIMARY.name()));

                        assertTrue(configs.getString(EVENT_PUBLISHER_HOST).equalsIgnoreCase(MotadataConfigUtil.getRemoteEventSecondarySubscriber()));

                        assertTrue(configs.getString(EVENT_SUBSCRIBER_HOST).equalsIgnoreCase(MotadataConfigUtil.getRemoteEventSecondarySubscriber()));

                        assertTrue(configs.getString(EVENT_SUBSCRIBER_SECONDARY_HOST).equalsIgnoreCase(MotadataConfigUtil.getRemoteEventPublisher()));

                        Bootstrap.undeployVerticle(HAManager.class.getSimpleName()).onComplete(result ->
                        {
                            if (result.succeeded())
                            {
                                testContext.completeNow();
                            }
                            else
                            {
                                testContext.failNow(result.cause());
                            }
                        });
                    }
                    else if (retries.incrementAndGet() > 15)
                    {
                        TestUtil.vertx().cancelTimer(timer);

                        testContext.failNow("Failed to get installation mode primary after 5 retries");
                    }
                }
                catch (IOException e)
                {
                    testContext.failNow(e.getMessage());
                }
            });
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    @Timeout(700 * 1000)
    void testMotadataSwitchOverEnginePrimaryToSecondary(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        MotadataConfigUtil.loadConfigs(new JsonObject().put(INSTALLATION_MODE, GlobalConstants.InstallationMode.PRIMARY.name()).put(HAConstants.HA_MODE, HAMode.PASSIVE.name()).put(EVENT_PUBLISHER_PORT, 9441));

        Bootstrap.startEngine(new HAManager(), HAManager.class.getSimpleName(), null);

        TestUtil.vertx().setTimer(150 * 1000, id ->
        {
            var retries = new AtomicInteger();

            TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(5), timer ->
            {
                try
                {
                    var configs = new JsonObject(FileUtils.readFileToString(new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "motadata.json")));

                    LOGGER.info("configs: " + configs.encode());

                    if (configs.getString(INSTALLATION_MODE).equalsIgnoreCase(InstallationMode.SECONDARY.name()))
                    {
                        TestUtil.vertx().cancelTimer(timer);

                        assertTrue(configs.getString(INSTALLATION_MODE).equalsIgnoreCase(InstallationMode.SECONDARY.name()));

                        assertTrue(configs.getString(EVENT_PUBLISHER_HOST).equalsIgnoreCase(MotadataConfigUtil.getRemoteEventSecondarySubscriber()));

                        assertTrue(configs.getString(EVENT_SUBSCRIBER_HOST).equalsIgnoreCase(MotadataConfigUtil.getRemoteEventSecondarySubscriber()));

                        assertTrue(configs.getString(EVENT_SUBSCRIBER_SECONDARY_HOST).equalsIgnoreCase(MotadataConfigUtil.getRemoteEventPublisher()));

                        Bootstrap.undeployVerticle(HAManager.class.getSimpleName()).onComplete(result ->
                        {
                            if (result.succeeded())
                            {
                                testContext.completeNow();
                            }
                            else
                            {
                                testContext.failNow(result.cause());
                            }
                        });
                    }
                    else if (retries.incrementAndGet() > 15)
                    {
                        TestUtil.vertx().cancelTimer(timer);

                        testContext.failNow("Failed to get installation mode primary after 5 retries");
                    }
                }
                catch (IOException e)
                {
                    testContext.failNow(e.getMessage());
                }
            });
        });
    }

    private void sendWebsocketClientRegistrationEvent()
    {
        var retries = new AtomicInteger();

        VERTX.setPeriodic(3000, timer ->
        {
            if (retries.incrementAndGet() > 3)
            {
                VERTX.cancelTimer(timer);
            }
            else
            {
                VERTX.eventBus().send("websocket.client.registered", new JsonObject());
            }
        });
    }
}
