/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.aiops;

import com.mindarray.*;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.Metric;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.LocalEventRouter;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.MetricConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Handler;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.METRIC_API_ENDPOINT;
import static com.mindarray.aiops.AIOpsConstants.DEPENDENCY_PARENT;
import static com.mindarray.api.APIConstants.ENTITY_ID;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.eventbus.EventBusConstants.*;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * Test class for the DependencyManager and DependencyQueryProcessor.
 * <p>
 * This class contains tests for various dependency scenarios:
 * <ul>
 *   <li>Cloud dependencies (AWS, Azure, Office 365)</li>
 *   <li>Network device dependencies</li>
 *   <li>Virtualization dependencies (vCenter, Hyper-V, Citrix Xen)</li>
 *   <li>HCI dependencies (Prism, Nutanix)</li>
 *   <li>SDN dependencies (Cisco vManage, vEdge)</li>
 *   <li>Process dependencies (Windows, Linux, Solaris)</li>
 *   <li>Application dependencies</li>
 *   <li>Manual link creation and management</li>
 * </ul>
 * <p>
 * These tests verify that dependencies are correctly created, queried, and managed
 * in different formats (flat, hierarchical) and for different types of IT components.
 */
@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(100 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestDependency
{

    private static final DeliveryOptions DELIVERY_OPTIONS = new DeliveryOptions().setSendTimeout(40 * 1000L);

    private static final Logger LOGGER = new Logger(TestDependency.class, MOTADATA_AIOPS, "Dependency Test");

    private static final JsonArray NETWORK_TOPOLOGY_DEPENDENT_LEVELS = new JsonArray(new ArrayList<>(1)).add(AIOpsConstants.DependencyLevel.FOUR.getName());

    private static JsonObject metricContext = null;

    private static MessageConsumer<JsonObject> messageConsumer = null;

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testAWSCloudDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.AWS_CLOUD.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().mergeIn(object).put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.CLOUD.name());

        assertCloudDependenciesTestResult(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testAzureCloudDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.AZURE_CLOUD.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().mergeIn(object).put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.CLOUD.name());

        assertCloudDependenciesTestResult(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testOffice365Dependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.OFFICE_365.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().mergeIn(object).put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.CLOUD.name());

        query(eventContext, testContext, false, items -> testContext.completeNow());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testCreateManualLink(VertxTestContext testContext)
    {
        LOGGER.info("running : testCreateManualLink");

        var item = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "**********");

        Assertions.assertNotNull(item);

        var context = new JsonObject("{\"dependency.mapper.parent\":\"**********\",\"dependency.mapper.child\":\"************\",\"dependency.mapper.type\":\"Network\",\"dependency.mapper.archived\":\"no\",\"dependency.mapper.context\":{\"dependency.mapper.parent.interface\":\"58\",\"dependency.mapper.parent.interface.name\":\"Vl40\",\"dependency.mapper.child.interface\":\"100\",\"dependency.mapper.child.interface.name\":\"Vl100\",\"dependency.mapper.link.layer\":\"L2\"}}");

        TestAPIUtil.post(TestAPIConstants.DEPENDENCY_MAPPER_API_ENDPOINT, context, response -> testContext.verify(() ->
        {
            var body = response.result().bodyAsJsonObject();

            LOGGER.info(String.format("response : %s ", body.encode()));

            assertEquals(SC_OK, response.result().statusCode());

            Assertions.assertNotNull(body);

            Assertions.assertTrue(body.containsKey(STATUS));

            testContext.completeNow();
        }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    @Timeout(value = 120, timeUnit = TimeUnit.SECONDS)
    void testNetworkDeviceDependencies(VertxTestContext testContext) throws Exception
    {
        LOGGER.info("running : testNetworkDeviceDependencies");

        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "**********");

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().mergeIn(object).put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.NETWORK.name());

        query(eventContext, testContext, true, items ->
        {
            LOGGER.info(String.format("neighbors : %s ", items));

            // means neighbor is connected
            Assertions.assertFalse(items.isEmpty());

            var levels = items.stream().filter(item -> JsonObject.mapFrom(item).containsKey(AIOpsConstants.DEPENDENCY_LEVEL) && CommonUtil.getByteValue(JsonObject.mapFrom(item).getValue(AIOpsConstants.DEPENDENCY_LEVEL)) != null)
                    .map(item -> CommonUtil.getByteValue(JsonObject.mapFrom(item).getValue(AIOpsConstants.DEPENDENCY_LEVEL))).distinct().collect(Collectors.toCollection(ArrayList::new));

            LOGGER.info(String.format("levels : %s ", items));

            Assertions.assertFalse(levels.isEmpty());

            levels.remove(AIOpsConstants.DependencyLevel.MINUS_ONE.getName());

            queryHierarchy(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName(), result ->
            {
                LOGGER.info(String.format("result : %s ", result));

                Assertions.assertNotNull(result);

                Assertions.assertTrue(result.containsKey(AIOpsConstants.DEPENDENCY_CHILDREN));

                Assertions.assertFalse(result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).isEmpty());

                var child = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).getJsonObject(0);

                Assertions.assertNotNull(child);

                Assertions.assertFalse(child.isEmpty());

                testContext.completeNow();
            });
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testNetworkDeviceInterfaceDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "**********");

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        assertObjectDependenciesTestResult(object, true, NMSConstants.MetricPlugin.SNMP_INTERFACE.getName(), NMSConstants.INTERFACE_NAME, testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testUpdateNetworkDeviceDependencies(VertxTestContext testContext, TestInfo testInfo)
    {
        Assertions.assertNotNull(metricContext);

        var interfaces = metricContext.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS);

        interfaces.remove(0);

        interfaces.remove(1);

        TestAPIUtil.put(METRIC_API_ENDPOINT + "/" + metricContext.getLong(ID),
                new JsonObject().put(APIConstants.REQUEST_PARAMS, new JsonArray().add(metricContext)),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var retries = new AtomicInteger();

                            TestUtil.vertx().setPeriodic(2000, timer ->
                            {
                                retries.getAndIncrement();

                                var context = new JsonObject().put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName()).put(AIOpsConstants.DEPENDENCY_FORMAT, AIOpsConstants.DependencyFormat.FLAT.getName())
                                        .put(AIOpsConstants.DEPENDENCY_SOURCE, "**********").put(AIOpsConstants.DEPENDENCY_LEVEL, NETWORK_TOPOLOGY_DEPENDENT_LEVELS);

                                query(context, testContext, true, AIOpsConstants.DependencyLevel.FOUR.getName(), timer, objects ->
                                {
                                    if (interfaces.size() == objects.size())
                                    {
                                        TestUtil.vertx().cancelTimer(timer);

                                        testContext.completeNow();
                                    }
                                    else if (retries.get() == 10)
                                    {
                                        TestUtil.vertx().cancelTimer(timer);

                                        testContext.failNow(new Exception("failed to update dependency"));
                                    }
                                });
                            });
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testSolarisProcessDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "************");

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        assertObjectDependenciesTestResult(object, false, NMSConstants.MetricPlugin.SOLARIS_PROCESS.getName(), AIOpsObject.OBJECT_NAME, testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testLinuxProcessDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "************");

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        assertObjectDependenciesTestResult(object, false, NMSConstants.MetricPlugin.LINUX_PROCESS.getName(), AIOpsObject.OBJECT_NAME, testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testApplicationDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "************");

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().mergeIn(object).put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.NETWORK.name());

        query(eventContext, testContext, true, items ->
        {
            // means application is connected
            Assertions.assertFalse(items.isEmpty());

            var valid = false;

            for (var index = 0; index < items.size(); index++)
            {
                var dependency = items.getJsonObject(index);

                if (dependency.containsKey(AIOpsConstants.DEPENDENCY_CONNECTED_LINK) && !dependency.getString(AIOpsConstants.DEPENDENCY_CONNECTED_LINK).equalsIgnoreCase(UNKNOWN))
                {
                    Assertions.assertTrue(dependency.containsKey(AIOpsConstants.DEPENDENCY_SOURCE));

                    assertEquals(object.getLong(ID), dependency.getLong(AIOpsConstants.DEPENDENCY_SOURCE));

                    Assertions.assertTrue(dependency.containsKey(AIOpsConstants.DEPENDENCY_LEVEL));

                    var level = CommonUtil.getByteValue(dependency.getValue(AIOpsConstants.DEPENDENCY_LEVEL));

                    if (level.equals(AIOpsConstants.DependencyLevel.ZERO.getName()) || level.equals(AIOpsConstants.DependencyLevel.ONE.getName()) || level.equals(AIOpsConstants.DependencyLevel.TWO.getName()) || level.equals(AIOpsConstants.DependencyLevel.THREE.getName()))
                    {
                        Assertions.assertTrue(true);
                    }

                    Assertions.assertTrue(dependency.containsKey(AIOpsConstants.DEPENDENCY_CONNECTED_LINK));

                    var connectedLink = dependency.getString(AIOpsConstants.DEPENDENCY_CONNECTED_LINK);

                    Assertions.assertTrue(dependency.containsKey(AIOpsConstants.DEPENDENCY_DESTINATION));

                    Assertions.assertFalse(dependency.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).isEmpty());

                    Assertions.assertTrue(NMSConstants.APPLICATION_TYPES.contains(dependency.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).getString(0)));

                    Assertions.assertNotNull(metricContext);

                    Assertions.assertNotNull(metricContext.getJsonObject(Metric.METRIC_CONTEXT));

                    Assertions.assertNotNull(metricContext.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS));

                    var objects = metricContext.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS).stream().map(item -> JsonObject.mapFrom(item).getString(AIOpsObject.OBJECT_NAME).trim())
                            .distinct().toList();

                    Assertions.assertFalse(object.isEmpty());

                    Assertions.assertTrue(objects.contains(connectedLink));

                    valid = true;

                    break;
                }
            }

            if (!valid)
            {
                testContext.failNow(new Exception("failed to find application dependency..."));
            }
            else
            {
                queryHierarchy(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName(), result ->
                {
                    Assertions.assertNotNull(result);

                    Assertions.assertFalse(result.isEmpty());

                    var children = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN);

                    Assertions.assertNotNull(children);

                    Assertions.assertFalse(children.isEmpty());

                    var child = children.getJsonObject(0);

                    Assertions.assertNotNull(child);

                    Assertions.assertTrue(child.containsKey(AIOpsConstants.DEPENDENCY_DESTINATION));

                    Assertions.assertTrue(NMSConstants.APPLICATION_TYPES.contains(child.getString(AIOpsConstants.DEPENDENCY_DESTINATION)));

                    Assertions.assertTrue(child.containsKey(AIOpsConstants.DEPENDENCY_LEVEL));

                    var level = CommonUtil.getByteValue(child.getValue(AIOpsConstants.DEPENDENCY_LEVEL));

                    if (level.equals(AIOpsConstants.DependencyLevel.ZERO.getName()) || level.equals(AIOpsConstants.DependencyLevel.ONE.getName()) || level.equals(AIOpsConstants.DependencyLevel.TWO.getName()) || level.equals(AIOpsConstants.DependencyLevel.THREE.getName()))
                    {
                        Assertions.assertTrue(true);
                    }

                    testContext.completeNow();
                });
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testWindowsProcessDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemsByValue(AIOpsObject.OBJECT_IP, "************").stream()
                .filter(item -> JsonObject.mapFrom(item).getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.WINDOWS.getName()))
                .map(JsonObject::mapFrom)
                .findFirst().orElse(null);

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        assertObjectDependenciesTestResult(object, false, NMSConstants.MetricPlugin.WINDOWS_PROCESS.getName(), AIOpsObject.OBJECT_NAME, testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    @Disabled
    void testCiscoUCSDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.CISCO_UCS.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.OTHER.name());

        var dependencyLevels = new JsonArray().add(AIOpsConstants.DependencyLevel.TWELVE.getName())
                .add(AIOpsConstants.DependencyLevel.TEN.getName())
                .add(AIOpsConstants.DependencyLevel.NINE.getName())
                .add(AIOpsConstants.DependencyLevel.EIGHT.getName());

        query(eventContext, testContext, true, items ->
        {
            // means cisco ucs dependencies available
            Assertions.assertFalse(items.isEmpty());

            var levels = items.stream().map(item -> CommonUtil.getByteValue(JsonObject.mapFrom(item).getValue(AIOpsConstants.DEPENDENCY_LEVEL)))
                    .distinct().toList();

            Assertions.assertFalse(levels.isEmpty());

            var size = dependencyLevels.size();

            levels.stream().filter(dependencyLevels::contains).forEach(dependencyLevels::remove);

            Assertions.assertNotEquals(dependencyLevels.size(), size);

            testContext.completeNow();
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testvCenterDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.VCENTER.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.VIRTUALIZATION.name());

        query(eventContext, testContext, true, items ->
        {
            // means vcenter dependencies available (datacenter -> vcenter -> vm)
            Assertions.assertFalse(items.isEmpty());

            var dataCenter = false;

            var cluster = false;

            var esxi = false;

            for (var index = 0; index < items.size(); index++)
            {
                var result = items.getJsonObject(index);

                if (CommonUtil.getByteValue(result.getValue(AIOpsConstants.DEPENDENCY_LEVEL)).equals(AIOpsConstants.DependencyLevel.TWELVE.getName()))
                {
                    dataCenter = true;

                    assertEquals(object.getLong(ID), result.getLong(AIOpsConstants.DEPENDENCY_SOURCE));

                    Assertions.assertFalse(result.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).isEmpty());
                }
                else if (CommonUtil.getByteValue(result.getValue(AIOpsConstants.DEPENDENCY_LEVEL)).equals(AIOpsConstants.DependencyLevel.EIGHT.getName()))
                {
                    Assertions.assertFalse(result.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).isEmpty());
                }
                else if (CommonUtil.getByteValue(result.getValue(AIOpsConstants.DEPENDENCY_LEVEL)).equals(AIOpsConstants.DependencyLevel.TEN.getName()))
                {
                    cluster = true;
                }
                else if (CommonUtil.getByteValue(result.getValue(AIOpsConstants.DEPENDENCY_LEVEL)).equals(AIOpsConstants.DependencyLevel.NINE.getName()))
                {
                    esxi = true;
                }
            }

            Assertions.assertTrue(dataCenter);

            Assertions.assertTrue(cluster);

            Assertions.assertTrue(esxi);

            assertParentChildVirtualizationClusterDependency(eventContext, testContext);
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testHyperVDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.HYPER_V.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.VIRTUALIZATION.name());

        query(eventContext, testContext, true, items -> assertVMDependenciesTestResult(items, testContext, object.getLong(ID), eventContext));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testHyperVClusterDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemsByType(NMSConstants.Type.HYPER_V_CLUSTER).getLong(0);

        Assertions.assertNotNull(object);

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, object)
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.VIRTUALIZATION.name());

        query(eventContext, testContext, true, items -> assertVirtualizationClusterDependencyTestResult(items, testContext, eventContext));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testCitrixXenDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.CITRIX_XEN.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.VIRTUALIZATION.name());

        query(eventContext, testContext, true, items -> assertVMDependenciesTestResult(items, testContext, object.getLong(ID), eventContext));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testCitrixXenClusterDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.CITRIX_XEN_CLUSTER.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.VIRTUALIZATION.name());

        query(eventContext, testContext, true, items -> assertVirtualizationClusterDependencyTestResult(items, testContext, eventContext));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testWindowsClusterDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.WINDOWS_CLUSTER.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.NETWORK.name());

        query(eventContext, testContext, true, items ->
        {
            Assertions.assertFalse(items.isEmpty());

            var os = false;

            for (var index = 0; index < items.size(); index++)
            {
                var result = items.getJsonObject(index);

                if (CommonUtil.getByteValue(result.getValue(AIOpsConstants.DEPENDENCY_LEVEL)).equals(AIOpsConstants.DependencyLevel.SEVEN.getName()))
                {
                    os = true;

                    Assertions.assertFalse(result.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).isEmpty());

                    break;
                }
            }

            Assertions.assertTrue(os);

            testContext.completeNow();
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testWindowsClusterDependenciesWithTopBottomHierarchy(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.WINDOWS_CLUSTER.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.NETWORK.name());

        queryHierarchy(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName(), result ->
        {
            Assertions.assertNotNull(result);

            Assertions.assertTrue(result.containsKey(AIOpsConstants.DEPENDENCY_CHILDREN));

            Assertions.assertFalse(result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).isEmpty());

            var child = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).getJsonObject(0);

            Assertions.assertNotNull(child);

            Assertions.assertFalse(child.isEmpty());

            testContext.completeNow();
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testChildParentDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "************");

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().mergeIn(object).put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.NETWORK.name());

        queryHierarchy(eventContext, testContext, AIOpsConstants.DependencyFormat.BOTTOM_TOP_HIERARCHY.getName(), result ->
        {
            Assertions.assertFalse(result.isEmpty());

            Assertions.assertTrue(result.containsKey(AIOpsConstants.DEPENDENCY_PARENT));

            Assertions.assertFalse(result.getJsonArray(DEPENDENCY_PARENT).isEmpty());

            var parent = result.getJsonArray(AIOpsConstants.DEPENDENCY_PARENT).getJsonObject(0);

            Assertions.assertNotNull(parent);

            Assertions.assertTrue(parent.containsKey(AIOpsConstants.DEPENDENCY_DESTINATION));

            assertEquals(ObjectConfigStore.getStore().flatItems(AIOpsObject.OBJECT_IP, "**********", ID).getLong(0), parent.getLong(AIOpsConstants.DEPENDENCY_DESTINATION));

            testContext.completeNow();
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testCreateMultipleDependencies(VertxTestContext testContext)
    {
        var item = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "***********");

        Assertions.assertNotNull(item);

        var context = new JsonObject("{\"dependency.mapper.parent\":\"***********\",\"dependency.mapper.child\":\"**********\",\"dependency.mapper.type\":\"Network\",\"dependency.mapper.archived\":\"no\",\"dependency.mapper.context\":{\"dependency.mapper.parent.interface\":\"15\",\"dependency.mapper.parent.interface.name\":\"motadata-DMZ\",\"dependency.mapper.child.interface\":\"63\",\"dependency.mapper.child.interface.name\":\"Vl500\",\"dependency.mapper.link.layer\":\"L2\"}}");

        TestAPIUtil.post(TestAPIConstants.DEPENDENCY_MAPPER_API_ENDPOINT, context, httpResponse ->
                testContext.verify(() ->
                {
                    assertEquals(SC_OK, httpResponse.result().statusCode());

                    var body = httpResponse.result().bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    Assertions.assertTrue(body.containsKey(STATUS));

                    TestAPIUtil.post(TestAPIConstants.DEPENDENCY_MAPPER_API_ENDPOINT, new JsonObject("{\"dependency.mapper.parent\":\"***********\",\"dependency.mapper.child\":\"**********\",\"dependency.mapper.type\":\"Network\",\"dependency.mapper.archived\":\"no\",\"dependency.mapper.context\":{\"dependency.mapper.parent.interface\":\"11\",\"dependency.mapper.parent.interface.name\":\"motadata-LAN\",\"dependency.mapper.child.interface\":\"8\",\"dependency.mapper.child.interface.name\":\"Gi1/0/1\",\"dependency.mapper.link.layer\":\"L2\"}}"), response ->
                            testContext.verify(() ->
                            {
                                assertEquals(SC_OK, response.result().statusCode());

                                var result = response.result().bodyAsJsonObject();

                                Assertions.assertNotNull(result);

                                Assertions.assertTrue(result.containsKey(STATUS));

                                testContext.completeNow();
                            }));
                }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    @Timeout(value = 120, timeUnit = TimeUnit.SECONDS)
    @Disabled
    void testMultipleDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "***********");

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().mergeIn(object).put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.NETWORK.name());

        query(eventContext, testContext, true, items ->
        {
            // means neighbor is connected
            Assertions.assertFalse(items.isEmpty());

            var levels = items.stream().filter(item -> JsonObject.mapFrom(item).containsKey(AIOpsConstants.DEPENDENCY_LEVEL) && CommonUtil.getByteValue(JsonObject.mapFrom(item).getValue(AIOpsConstants.DEPENDENCY_LEVEL)) != null)
                    .map(item -> CommonUtil.getByteValue(JsonObject.mapFrom(item).getValue(AIOpsConstants.DEPENDENCY_LEVEL))).distinct().collect(Collectors.toCollection(ArrayList::new));

            Assertions.assertFalse(levels.isEmpty());

            levels.remove(AIOpsConstants.DependencyLevel.MINUS_ONE.getName());

            queryHierarchy(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName(), result ->
            {
                Assertions.assertNotNull(result);

                Assertions.assertTrue(result.containsKey(AIOpsConstants.DEPENDENCY_CHILDREN));

                Assertions.assertFalse(result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).isEmpty());

                var child = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).getJsonObject(0);

                Assertions.assertNotNull(child);

                Assertions.assertFalse(child.isEmpty());

                testContext.completeNow();
            });
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
        //#24868
    void testDuplicateChildManualLinkDependency(VertxTestContext testContext)
    {

        var item = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "***********");

        Assertions.assertNotNull(item);

        var context = new JsonObject("{\"dependency.mapper.parent\":\"**********\",\"dependency.mapper.child\":\"***********\",\"dependency.mapper.type\":\"Network\",\"dependency.mapper.archived\":\"no\",\"dependency.mapper.context\":{\"dependency.mapper.parent.interface\":\"62\",\"dependency.mapper.parent.interface.name\":\"vl100\",\"dependency.mapper.child.interface\":\"15\",\"dependency.mapper.child.interface.name\":\"motadata-DMZ\",\"dependency.mapper.link.layer\":\"L2\"}}");

        TestAPIUtil.post(TestAPIConstants.DEPENDENCY_MAPPER_API_ENDPOINT, context, response ->
                testContext.verify(() ->
                {
                    var result = response.result().bodyAsJsonObject();

                    Assertions.assertNotNull(result);

                    Assertions.assertTrue(result.containsKey(STATUS));

                    Assertions.assertEquals(STATUS_FAIL, result.getString(STATUS));

                    Assertions.assertEquals(String.format(ErrorMessageConstants.DEPENDENCY_MAPPER_FAILED, ErrorMessageConstants.DEPENDENCY_MAPPER_FAILED_DUPLICATE_ERROR), result.getString(MESSAGE));

                    testContext.completeNow();
                }));
    }

    @Test
    @Order(24)
    void testTopologyParentChildDependency(VertxTestContext testContext)
    {
        var eventContext = new JsonObject().put(ENTITY_ID, ObjectConfigStore.getStore().getItemByIP("**********"))
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.NETWORK)
                .put(AIOpsConstants.DEPENDENCY_FORMAT, AIOpsConstants.DependencyFormat.FLAT.getName());

        messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            try
            {
                var context = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                Assertions.assertNotNull(context);

                if (context.containsKey(EVENT_TYPE) && context.getString(EVENT_TYPE).equalsIgnoreCase(EventBusConstants.UI_ACTION_TOPOLOGY_HIERARCHY_FETCH))
                {
                    var result = context.getJsonArray(RESULT);

                    Assertions.assertNotNull(result);

                    Assertions.assertFalse(result.isEmpty());

                    messageConsumer.unregister();

                    testContext.completeNow();
                }
            }
            catch (Exception exception)
            {
                messageConsumer.unregister();

                testContext.failNow(exception);
            }
        });

        TestUtil.vertx().eventBus().send(UI_ACTION_TOPOLOGY_HIERARCHY_FETCH, eventContext.put(SESSION_ID, TestUtil.getSessionId()));
    }

    @Test
    @Order(25)
    void testTopologyParentFetchNetwork(VertxTestContext testContext)
    {
        var eventContext = new JsonObject().put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.NETWORK);

        assertParentChildDependencyTestResult(testContext, ObjectConfigStore.getStore().getItemByIP("**********"));

        TestUtil.vertx().eventBus().send(UI_ACTION_TOPOLOGY_PARENT_FETCH, eventContext.put(SESSION_ID, TestUtil.getSessionId()));
    }

    @Test
    @Order(26)
    void testTopologyParentFetchVirtualization(VertxTestContext testContext)
    {
        var eventContext = new JsonObject().put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.VIRTUALIZATION);

        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.VCENTER.getName());

        assertParentChildDependencyTestResult(testContext, object.getLong(ID));

        TestUtil.vertx().eventBus().send(UI_ACTION_TOPOLOGY_PARENT_FETCH, eventContext.put(SESSION_ID, TestUtil.getSessionId()));
    }

    @Test
    @Order(27)
    void testTopologyParentFetchCloud(VertxTestContext testContext)
    {
        var eventContext = new JsonObject().put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.CLOUD);

        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.AWS_CLOUD.getName());

        assertParentChildDependencyTestResult(testContext, object.getLong(ID));

        TestUtil.vertx().eventBus().send(UI_ACTION_TOPOLOGY_PARENT_FETCH, eventContext.put(SESSION_ID, TestUtil.getSessionId()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    void testPrismDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.PRISM.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var context = new JsonObject().put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.HCI.name());

        query(context, testContext, true, items ->
        {
            // means prism dependencies available ( prism -> cluster -> nutanix)
            Assertions.assertFalse(items.isEmpty());

            var cluster = false;

            var nutanix = false;

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                // todo ; for flat , why odd condition in DQP?

                if (CommonUtil.getByteValue(item.getValue(AIOpsConstants.DEPENDENCY_LEVEL)).equals(AIOpsConstants.DependencyLevel.EIGHT.getName()))
                {
                    Assertions.assertFalse(item.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).isEmpty());

                    nutanix = true;
                }
                else if (CommonUtil.getByteValue(item.getValue(AIOpsConstants.DEPENDENCY_LEVEL)).equals(AIOpsConstants.DependencyLevel.NINE.getName()))
                {
                    cluster = true;
                }
            }

            Assertions.assertTrue(cluster);

            Assertions.assertTrue(nutanix);

            assertParentChildHCIDependency(context, testContext, true);
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testNutanixDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.NUTANIX.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.HCI.name());

        query(eventContext, testContext, true, items ->
        {
            Assertions.assertFalse(items.isEmpty());

            var nutanix = false;

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                if (CommonUtil.getByteValue(item.getValue(AIOpsConstants.DEPENDENCY_LEVEL)).equals(AIOpsConstants.DependencyLevel.EIGHT.getName()))
                {
                    Assertions.assertFalse(item.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).isEmpty());

                    nutanix = true;
                }
            }

            Assertions.assertTrue(nutanix);

            assertParentChildHCIDependency(eventContext, testContext, false);
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testCiscovManageDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_TYPE, NMSConstants.Type.CISCO_VMANAGE.getName());

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.SDN.name());

        queryHierarchy(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName(), result ->
        {
            Assertions.assertNotNull(result);

            Assertions.assertTrue(result.containsKey(AIOpsConstants.DEPENDENCY_CHILDREN));

            Assertions.assertFalse(result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).isEmpty());

            var child = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).getJsonObject(0);

            Assertions.assertNotNull(child);

            Assertions.assertFalse(child.isEmpty());

            testContext.completeNow();
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    void testCiscovEdgeDependencies(VertxTestContext testContext)
    {
        var object = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemByIP("**********"));

        Assertions.assertNotNull(object);

        Assertions.assertFalse(object.isEmpty());

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, object.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.SDN.name());

        queryHierarchy(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName(), result ->
        {
            Assertions.assertNotNull(result);

            Assertions.assertTrue(result.containsKey(AIOpsConstants.DEPENDENCY_CHILDREN));

            Assertions.assertFalse(result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).isEmpty());

            var child = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).getJsonObject(0);

            Assertions.assertNotNull(child);

            Assertions.assertFalse(child.isEmpty());

            testContext.completeNow();
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    void testCiscoACIDependencies(VertxTestContext testContext)
    {
        var item = ObjectConfigStore.getStore().getItem(ObjectConfigStore.getStore().getItemByIP("***********"));

        Assertions.assertNotNull(item);

        Assertions.assertFalse(item.isEmpty());

        var eventContext = new JsonObject().put(AIOpsConstants.ENTITY_ID, item.getLong(ID))
                .put(EVENT_REPLY, YES)
                .put(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.SDN.name());

        queryHierarchy(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName(), result ->
        {
            Assertions.assertNotNull(result);

            Assertions.assertTrue(result.containsKey(AIOpsConstants.DEPENDENCY_CHILDREN));

            Assertions.assertFalse(result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).isEmpty());

            var child = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).getJsonObject(0);

            Assertions.assertNotNull(child);

            Assertions.assertFalse(child.isEmpty());

            testContext.completeNow();
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testCreateDependencyMappings(VertxTestContext testContext)
    {
        TestUtil.vertx().eventBus().<JsonArray>request(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName() + ".1.read",
                new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName() + ".1"),
                DELIVERY_OPTIONS,
                reply ->
                {
                    if (reply.failed())
                    {
                        testContext.failNow(reply.cause());
                    }
                    else if (reply.result().body().isEmpty())
                    {
                        testContext.failNow(new Exception("failed to read dependency query..."));
                    }
                    else
                    {
                        Assertions.assertFalse(reply.result().body().isEmpty());

                        testContext.completeNow();
                    }
                });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    @Timeout(value = 30, timeUnit = TimeUnit.SECONDS)
    void testAddNewLocalEventRouter(VertxTestContext testContext)
    {
        TestUtil.vertx().eventBus().<String>request(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName() + ".add.router",
                new JsonObject(), reply ->
                {
                    if (reply.failed())
                    {
                        testContext.failNow(reply.cause());
                    }
                    else
                    {
                        Assertions.assertNotNull(reply.result().body());

                        Assertions.assertEquals(reply.result().body(), EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName() + "." + (MotadataConfigUtil.getDependencyLocalDomainInstances() + 1));

                        testContext.completeNow();
                    }
                });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(32)
    @Timeout(value = 30, timeUnit = TimeUnit.SECONDS)
    void testRemoveLocalEventRouter(VertxTestContext testContext)
    {
        TestUtil.vertx().eventBus().<String>request(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName() + ".remove.router",
                new JsonObject(), reply ->
                {
                    if (reply.failed())
                    {
                        testContext.failNow(reply.cause());
                    }
                    else
                    {
                        Assertions.assertNotNull(reply.result().body());

                        Assertions.assertEquals(reply.result().body(), EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName() + "." + (MotadataConfigUtil.getDependencyLocalDomainInstances() + 1));

                        testContext.completeNow();
                    }
                });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(33)
    @Timeout(value = 45, timeUnit = TimeUnit.SECONDS)
    void testUndeployDependencyManager(VertxTestContext testContext)
    {
        var engine = Bootstrap.getDeployedVerticles().get(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName());

        Assertions.assertNotNull(engine);

        Bootstrap.undeployVerticle(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName()).onComplete(asyncResult ->
        {
            if (asyncResult.failed())
            {
                testContext.failNow(asyncResult.cause());
            }
            else
            {
                Assertions.assertNull(Bootstrap.getDeployedVerticles().get(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName()));

                Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(), AIOpsConstants.DEPENDENCY_SOURCE,
                                MotadataConfigUtil.getDependencyCrossDomainInstances(), "com.mindarray.aiops.DependencyManager", false), EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName(), null)
                        .onComplete(result ->
                        {
                            if (result.failed())
                            {
                                testContext.failNow(result.cause());
                            }
                            else
                            {
                                Assertions.assertNotNull(Bootstrap.getDeployedVerticles().get(EventBusConstants.EVENT_DEPENDENCY + "." + AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName()));

                                testContext.completeNow();
                            }
                        });
            }
        });
    }

    private void assertVirtualizationClusterDependencyTestResult(JsonArray items, VertxTestContext testContext, JsonObject eventContext)
    {
        Assertions.assertFalse(items.isEmpty());

        var vms = false;

        var esxi = false;

        for (var index = 0; index < items.size(); index++)
        {
            var result = items.getJsonObject(index);

            if (CommonUtil.getByteValue(result.getValue(AIOpsConstants.DEPENDENCY_LEVEL)).equals(AIOpsConstants.DependencyLevel.EIGHT.getName()))
            {
                vms = true;

                Assertions.assertFalse(result.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).isEmpty());
            }
            else if (CommonUtil.getByteValue(result.getValue(AIOpsConstants.DEPENDENCY_LEVEL)).equals(AIOpsConstants.DependencyLevel.NINE.getName()))
            {
                esxi = true;

                Assertions.assertFalse(result.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).isEmpty());
            }
        }

        Assertions.assertTrue(esxi);

        Assertions.assertTrue(vms);

        assertParentChildVirtualizationClusterDependency(eventContext, testContext);
    }

    private void assertParentChildVirtualizationClusterDependency(JsonObject eventContext, VertxTestContext testContext)
    {
        queryHierarchy(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName(), result ->
        {

            Assertions.assertFalse(result.isEmpty());

            var children = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN);

            Assertions.assertNotNull(children);

            Assertions.assertFalse(children.isEmpty());

            if (ObjectConfigStore.getStore().getItem(result.getLong(ID)).getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.VCENTER.getName()))
            {

                var response = new JsonObject(CommonUtil.getString(Bootstrap.vertx().fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "test-plugin-metrics" + GlobalConstants.PATH_SEPARATOR + "vcenter_poll.json")));

                Assertions.assertNotNull(response);

                assertEquals(response.getJsonObject(ObjectConfigStore.getStore().getItem(result.getLong(ID)).getString(AIOpsObject.OBJECT_IP)).getJsonObject(RESULT).getInteger("vcenter.nodes"), children.size());

            }

            var levels = new HashSet<Byte>();

            for (var index = 0; index < children.size(); index++)
            {
                var child = children.getJsonObject(0);

                if (child.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN) != null && !child.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).isEmpty())
                {
                    levels.add(CommonUtil.getByteValue(child.getValue(AIOpsConstants.DEPENDENCY_LEVEL)));

                    var nestedChild = child.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).getJsonObject(0);

                    if (nestedChild != null && nestedChild.containsKey(AIOpsConstants.DEPENDENCY_LEVEL))
                    {
                        levels.add(CommonUtil.getByteValue(nestedChild.getValue(AIOpsConstants.DEPENDENCY_LEVEL)));
                    }
                }
            }

            var valid = true;

            if (!levels.contains(AIOpsConstants.DependencyLevel.NINE.getName()))
            {
                valid = false;

                testContext.failNow(new Exception("failed to find esxi..."));
            }

            if (!levels.contains(AIOpsConstants.DependencyLevel.EIGHT.getName()))
            {
                valid = false;

                testContext.failNow(new Exception("failed to find vms..."));
            }

            if (valid)
            {
                testContext.completeNow();
            }
        });
    }

    private void assertParentChildHCIDependency(JsonObject eventContext, VertxTestContext testContext, boolean prism)
    {
        queryHierarchy(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName(), result ->
        {
            Assertions.assertFalse(result.isEmpty());

            var children = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN);

            Assertions.assertNotNull(children);

            Assertions.assertFalse(children.isEmpty());

            var levels = new HashSet<Byte>();

            for (var index = 0; index < children.size(); index++)
            {
                var child = children.getJsonObject(0);

                levels.add(CommonUtil.getByteValue(child.getValue(AIOpsConstants.DEPENDENCY_LEVEL)));

                if (child.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN) != null && !child.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).isEmpty())
                {
                    var nestedChild = child.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN).getJsonObject(0);

                    if (nestedChild != null && nestedChild.containsKey(AIOpsConstants.DEPENDENCY_LEVEL))
                    {
                        levels.add(CommonUtil.getByteValue(nestedChild.getValue(AIOpsConstants.DEPENDENCY_LEVEL)));
                    }
                }
            }

            var valid = true;

            if (prism && !levels.contains(AIOpsConstants.DependencyLevel.NINE.getName()))
            {
                valid = false;

                testContext.failNow(new Exception("failed to find nutanix..."));
            }

            if (!levels.contains(AIOpsConstants.DependencyLevel.EIGHT.getName()))
            {
                valid = false;

                testContext.failNow(new Exception("failed to find vms..."));
            }

            if (valid)
            {
                testContext.completeNow();
            }
        });

    }

    private void assertCloudDependenciesTestResult(JsonObject eventContext, VertxTestContext testContext, String hierarchy)
    {
        query(eventContext, testContext, true, items ->
        {
            Assertions.assertFalse(items.isEmpty());

            assertCloudConnectedServices(new JsonArray(items.stream().filter(item -> JsonObject.mapFrom(item).getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION) != null)
                    .flatMap(item -> JsonObject.mapFrom(item).getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION)
                            .stream().map(value -> new AbstractMap.SimpleEntry<>(item, value)))
                    .map(item -> CommonUtil.getLong(item.getValue()))
                    .collect(Collectors.toList())));

            queryHierarchy(eventContext, testContext, hierarchy, result ->
            {
                Assertions.assertFalse(result.isEmpty());

                var destinations = new JsonArray();

                qualifyChildren(result, destinations);

                assertCloudConnectedServices(destinations);

                testContext.completeNow();
            });

        });
    }

    private void assertCloudConnectedServices(JsonArray destinations)
    {
        Assertions.assertFalse(destinations.isEmpty());

        var connectedServices = ObjectConfigStore.getStore().getItems(destinations).stream().map(item -> JsonObject.mapFrom(item).getString(AIOpsObject.OBJECT_TYPE)).distinct().collect(Collectors.toList());

        Assertions.assertNotNull(connectedServices);

        Assertions.assertFalse(connectedServices.isEmpty());
    }

    private void qualifyChildren(JsonObject result, JsonArray qualifiedChildren)
    {
        var children = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN);

        if (children != null && !children.isEmpty())
        {
            for (var index = 0; index < children.size(); index++)
            {
                var child = children.getJsonObject(index);

                var destination = child.getValue(AIOpsConstants.DEPENDENCY_DESTINATION);

                if (destination instanceof Long && CommonUtil.getByteValue(child.getValue(AIOpsConstants.DEPENDENCY_LEVEL)).equals(AIOpsConstants.DependencyLevel.EIGHT.getName()))
                {
                    qualifiedChildren.add(destination);
                }

                var connections = child.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN);

                if (connections != null && !connections.isEmpty())
                {
                    qualifyChildren(child, qualifiedChildren);
                }
            }
        }
    }

    private void query(JsonObject context, VertxTestContext testContext, boolean connectedDependency, Byte level, Long timer, Handler<JsonArray> event)
    {
        TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_DEPENDENCY + "." + context.getString(AIOpsConstants.DEPENDENCY_TYPE) + ".query",
                context,
                new DeliveryOptions().setSendTimeout(120000L),
                reply ->
                {
                    if (reply.failed())
                    {
                        if (timer != null)
                        {
                            TestUtil.vertx().cancelTimer(timer);
                        }

                        testContext.failNow(reply.cause());
                    }

                    Assertions.assertTrue(reply.result().body().containsKey(AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName()));

                    var result = reply.result().body().getJsonObject(AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName());

                    LOGGER.info(String.format("result from query : %s ", result));

                    //means connected neighbors
                    if (connectedDependency)
                    {
                        var valid = false;

                        for (var item : result.getMap().keySet())
                        {
                            if (item.contains(VALUE_SEPARATOR))
                            {
                                valid = true;

                                break;
                            }
                        }

                        Assertions.assertTrue(valid);
                    }

                    var dependencies = result.getJsonObject(context.getString(AIOpsConstants.DEPENDENCY_SOURCE));

                    Assertions.assertNotNull(dependencies);

                    Assertions.assertTrue(dependencies.containsKey(level.toString()));

                    var objects = dependencies.getJsonArray(level.toString());

                    Assertions.assertNotNull(objects);

                    Assertions.assertFalse(objects.isEmpty());

                    event.handle(objects);
                });
    }

    private void query(JsonObject eventContext, VertxTestContext testContext, boolean checkResult, Handler<JsonArray> event)
    {
        TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_DEPENDENCY_QUERY,
                eventContext.put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName()).put(AIOpsConstants.DEPENDENCY_FORMAT, AIOpsConstants.DependencyFormat.FLAT.getName()),
                DELIVERY_OPTIONS,
                reply ->
                {
                    if (reply.failed())
                    {
                        testContext.failNow(reply.cause());
                    }

                    Assertions.assertNotNull(reply.result().body());

                    Assertions.assertFalse(reply.result().body().isEmpty());

                    var items = reply.result().body().getJsonArray(RESULT);

                    LOGGER.info(String.format("result from dependency query : %s ", items));

                    if (checkResult)
                    {
                        Assertions.assertNotNull(items);
                    }

                    event.handle(items);
                });
    }

    private void queryHierarchy(JsonObject eventContext, VertxTestContext testContext, String resultType, Handler<JsonObject> event)
    {
        TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_DEPENDENCY_QUERY,
                eventContext.put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName()).put(AIOpsConstants.DEPENDENCY_FORMAT, resultType),
                DELIVERY_OPTIONS,
                reply ->
                {
                    if (reply.failed())
                    {
                        testContext.failNow(reply.cause());
                    }

                    Assertions.assertNotNull(reply.result().body());

                    Assertions.assertFalse(reply.result().body().isEmpty());

                    var result = reply.result().body().getJsonObject(RESULT);

                    Assertions.assertNotNull(result);

                    event.handle(result);
                });
    }

    private void assertVMDependenciesTestResult(JsonArray items, VertxTestContext testContext, long id, JsonObject eventContext)
    {
        Assertions.assertFalse(items.isEmpty());

        var dependency = items.getJsonObject(0);

        Assertions.assertTrue(dependency.containsKey(AIOpsConstants.DEPENDENCY_CONNECTED_LINK));

        Assertions.assertTrue(dependency.getString(AIOpsConstants.DEPENDENCY_CONNECTED_LINK).equalsIgnoreCase(UNKNOWN));

        assertEquals(id, dependency.getLong(AIOpsConstants.DEPENDENCY_SOURCE));

        assertEquals(AIOpsConstants.DependencyLevel.EIGHT.getName(), CommonUtil.getByteValue(dependency.getValue(AIOpsConstants.DEPENDENCY_LEVEL)));

        Assertions.assertFalse(dependency.getJsonArray(AIOpsConstants.DEPENDENCY_DESTINATION).isEmpty());

        queryHierarchy(eventContext, testContext, AIOpsConstants.DependencyFormat.TOP_BOTTOM_HIERARCHY.getName(), result ->
        {
            Assertions.assertFalse(result.isEmpty());

            var children = result.getJsonArray(AIOpsConstants.DEPENDENCY_CHILDREN);

            Assertions.assertNotNull(children);

            Assertions.assertFalse(children.isEmpty());

            var child = children.getJsonObject(0);

            Assertions.assertTrue(child.containsKey(AIOpsConstants.DEPENDENCY_CONNECTED_LINK));

            Assertions.assertTrue(child.getString(AIOpsConstants.DEPENDENCY_CONNECTED_LINK).equalsIgnoreCase(UNKNOWN));

            assertEquals(id, child.getLong(AIOpsConstants.DEPENDENCY_SOURCE));

            assertEquals(AIOpsConstants.DependencyLevel.EIGHT.getName(), CommonUtil.getByteValue(child.getValue(AIOpsConstants.DEPENDENCY_LEVEL)));

            Assertions.assertTrue(child.containsKey(AIOpsConstants.DEPENDENCY_DESTINATION));

            testContext.completeNow();
        });
    }

    private void assertObjectDependenciesTestResult(JsonObject object, boolean connectedDependency, String metricPlugin, String objectName, VertxTestContext testContext)
    {
        var context = new JsonObject().put(AIOpsConstants.DEPENDENCY_TYPE, AIOpsConstants.DependencyType.LOCAL_DOMAIN.getName()).put(AIOpsConstants.DEPENDENCY_SOURCE, object.getString(AIOpsObject.OBJECT_IP)).put(AIOpsConstants.DEPENDENCY_LEVEL, NETWORK_TOPOLOGY_DEPENDENT_LEVELS);

        query(context, testContext, connectedDependency, AIOpsConstants.DependencyLevel.FOUR.getName(), null, objects ->
        {
            var metric = MetricConfigStore.getStore().getItemsByObject(object.getLong(ID)).stream().filter(item -> item.getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(metricPlugin))
                    .map(JsonObject::copy).findFirst().orElse(null);

            LOGGER.info(String.format("metric %s for object %s ", metric, object.getString(AIOpsObject.OBJECT_NAME)));

            Assertions.assertNotNull(metric);

            Assertions.assertNotNull(metric.getJsonObject(Metric.METRIC_CONTEXT));

            Assertions.assertNotNull(metric.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS));

            metricContext = metric;

            var interfaces = metric.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(NMSConstants.OBJECTS)
                    .stream().map(item -> JsonObject.mapFrom(item).getString(objectName)).distinct().toList();

            Assertions.assertFalse(interfaces.isEmpty());

            assertEquals(objects.size(), interfaces.size());

            testContext.completeNow();
        });
    }

    void assertParentChildDependencyTestResult(VertxTestContext testContext, long id)
    {
        messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            try
            {
                var context = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                Assertions.assertTrue(context.containsKey(DEPENDENCY_PARENT));

                Assertions.assertFalse(context.getJsonArray(DEPENDENCY_PARENT).isEmpty());

                Assertions.assertTrue(context.getJsonArray(DEPENDENCY_PARENT).contains(id));

                messageConsumer.unregister();

                testContext.completeNow();

            }
            catch (Exception exception)
            {
                messageConsumer.unregister();

                testContext.failNow(exception);
            }
        });
    }
}
