/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.visualization;

import com.mindarray.*;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.CompliancePolicy;
import com.mindarray.api.ComplianceRule;
import com.mindarray.compliance.ComplianceConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.LocalEventRouter;
import com.mindarray.netroute.NetRouteConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.nms.SNMPTrapProcessor;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.report.ReportConstants;
import com.mindarray.store.*;
import com.mindarray.util.*;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import junit.framework.AssertionFailedError;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.zeromq.SocketType;
import org.zeromq.ZMQ;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.EVENT_POLICY_API_ENDPOINT;
import static com.mindarray.TestAPIConstants.USER_API_ENDPOINT;
import static com.mindarray.api.AIOpsObject.*;
import static com.mindarray.api.NetRoute.NETROUTE_DESTINATION;
import static com.mindarray.api.RemoteEventProcessor.*;
import static com.mindarray.api.TestLogParser.SESSION_ID;
import static com.mindarray.api.User.*;
import static com.mindarray.db.ConfigDBConstants.FIELD_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.eventbus.EventBusConstants.ChangeNotificationType.ADD_EVENT_SOURCE;
import static com.mindarray.log.LogEngineConstants.EVENT_CATEGORY;
import static com.mindarray.log.LogEngineConstants.SOURCE_GROUPS;
import static com.mindarray.manager.MotadataAppManager.HEARTBEAT_STATE;
import static com.mindarray.nms.NMSConstants.STATE_RUNNING;
import static com.mindarray.visualization.VisualizationConstants.*;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(50 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestVisualizationManager
{
    private static final Logger LOGGER = new Logger(TestVisualizationManager.class, GlobalConstants.MOTADATA_VISUALIZATION, "Visualization Manager Test");
    private static final ZMQ.Socket SUBSCRIBER = Bootstrap.zcontext().socket(SocketType.SUB);
    private static final Map<Long, VertxTestContext> QUERY_CONTEXT = new HashMap<>();//queryid
    private static final String VISUALIZATION_TEST = "test.visualization.render";
    private static final String VISUALIZATION_DATA_RESULT_KEY = "data.result.key";
    private static final JsonObject RESPONSES_BY_TYPE = new JsonObject().put("application.status", new JsonObject("{\"result1\":[{\"monitor\":\"1\",\"system.process\":\"httpd.exe|\\\"C:\\\\Program Files\\\\Apache Software Foundation\\\\Apache2.4\\\\bin\\\\httpd.exe\\\" -k runservice\",\"system.process~uptime.percent^avg\":100.0}],\"result2\":[{\"monitor\":\"1\",\"system.service\":\"TermService\",\"system.service~uptime.percent^avg\":100.0}],\"result3\":[{\"monitor\":\"1\",\"system.service\":\"TermService\",\"system.service~uptime.percent^avg\":40.0,\"system.service~downtime.percent^avg\":16.0,\"system.service~unknowntime.percent^avg\":24.0,\"system.service~unreachabletime.percent^avg\":20.0,\"system.service~uptime.seconds^sum\":1820,\"system.service~downtime.seconds^sum\":80,\"system.service~unknowntime.seconds^sum\":10,\"system.service~unreachabletime.seconds^sum\":200}]}"))
            .put(VisualizationConstants.VisualizationDataSource.TRAP_FLAP.getName(), new JsonObject("{\"result\":[{\"event.source\":\"127.0.0.1\",\"trap.oid\":\".*******.4.1.644.2.4.0.1023\",\"timestamp^last\":1669635346000,\"trap.message^last\":\"A warmStart trap signifies that the SNMP entity, supporting a notification originator application, is reinitializing itself such that its configuration is unaltered.\",\"trap.severity^last\":\"unknown\",\"trap.vendor^last\":\"NXNETWORK\",\"trap.type^last\":\"warmStart\",\"trap.version^last\":\"v2c\"},{\"event.source\":\"127.0.0.1\",\"trap.oid\":\".*******.4.1.644.2.4.0.1020\",\"timestamp^last\":1669635367000,\"trap.message^last\":\"A linkUp trap signifies that the SNMP entity, acting in an agent role, has detected that the ifOperStatus object for one of its communication links left the down state and transitioned into some other state (but not into the notPresent state). This other state is indicated by the included value of ifOperStatus.\",\"trap.severity^last\":\"unknown\",\"trap.vendor^last\":\"NXNETWORK\",\"trap.type^last\":\"linkUp\",\"trap.version^last\":\"v2c\"},{\"event.source\":\"127.0.0.1\",\"trap.oid\":\".*******.4.1.644.2.4.0.1021\",\"timestamp^last\":1669635354000,\"trap.message^last\":\"A linkDown trap signifies that the SNMP entity, acting in an agent role, has detected that the ifOperStatus object for one of its communication links is about to enter the down state from some other state (but not from the notPresent state). This other state is indicated by the included value of ifOperStatus.\",\"trap.severity^last\":\"critical\",\"trap.vendor^last\":\"NXNETWORK\",\"trap.type^last\":\"linkDown\",\"trap.version^last\":\"v2c\"},{\"event.source\":\"127.0.0.1\",\"trap.oid\":\".*******.4.1.644.2.4.0.1020\",\"timestamp^last\":1669635339000,\"trap.message^last\":\"A coldStart trap signifies that the SNMP entity, supporting a notification originator application, is reinitializing itself and that its configuration may have been altered.\",\"trap.severity^last\":\"unknown\",\"trap.vendor^last\":\"NXNETWORK\",\"trap.type^last\":\"coldStart\",\"trap.version^last\":\"v2c\"},{\"event.source\":\"127.0.0.1\",\"trap.oid\":\".*******.4.1.644.2.4.0.1040\",\"timestamp^last\":16696353464560,\"trap.message^last\":\"A warmStart trap signifies that the SNMP entity, supporting a notification originator application, is reinitializing itself such that its configuration is unaltered.\",\"trap.severity^last\":\"unknown\",\"trap.vendor^last\":\"NXNETWORK\",\"trap.type^last\":\"warmStart\",\"trap.version^last\":\"v2c\"},{\"event.source\":\"127.0.0.1\",\"trap.oid\":\".0.0\",\"timestamp^last\":16696353464560,\"trap.message^last\":\"A warmStart trap signifies that the SNMP entity, supporting a notification originator application, is reinitializing itself such that its configuration is unaltered.\",\"trap.severity^last\":\"unknown\",\"trap.vendor^last\":\"NXNETWORK\",\"trap.type^last\":\"warmStart\",\"trap.version^last\":\"v2c\"},{\"event.source\":\"127.0.0.1\",\"trap.oid\":\".*******.*******.5.5\",\"timestamp^last\":16696353464560,\"trap.message^last\":\"A warmStart trap signifies that the SNMP entity, supporting a notification originator application, is reinitializing itself such that its configuration is unaltered.\",\"trap.severity^last\":\"unknown\",\"trap.vendor^last\":\"NXNETWORK\",\"trap.type^last\":\"warmStart\",\"trap.version^last\":\"v2c\"}, {\"event.source\":\"127.0.0.1\",\"trap.oid\":\".*******.*******.5.150\",\"timestamp^last\":16696353464560,\"trap.message^last\":\"A warmStart trap signifies that the SNMP entity, supporting a notification originator application, is reinitializing itself such that its configuration is unaltered.\",\"trap.severity^last\":\"unknown\",\"trap.vendor^last\":\"NXNETWORK\",\"trap.type^last\":\"warmStart\",\"trap.version^last\":\"v2c\"}, {\"event.source\":\"127.0.0.1\",\"trap.oid\":\".*******.*******.5.5\",\"timestamp^last\":16696353464560,\"trap.message^last\":\"A warmStart trap signifies that the SNMP entity, supporting a notification originator application, is reinitializing itself such that its configuration is unaltered.\",\"trap.severity^last\":\"unknown\",\"trap.vendor^last\":\"NXNETWORK\",\"trap.type^last\":\"warmStart\",\"trap.version^last\":\"v2c\"}, {\"event.source\":\"127.0.0.1\",\"trap.oid\":\".*******.*******.5.250\",\"timestamp^last\":16696353464560,\"trap.message^last\":\"A warmStart trap signifies that the SNMP entity, supporting a notification originator application, is reinitializing itself such that its configuration is unaltered.\",\"trap.severity^last\":\"unknown\",\"trap.vendor^last\":\"NXNETWORK\",\"trap.type^last\":\"warmStart\",\"trap.version^last\":\"v2c\"}]}"))
            .put(VisualizationConstants.VisualizationDataSource.TRAP.getName(), new JsonObject("{\"result\":[{\"event.source\":\"127.0.0.1\",\"trap.oid\":\".*******.4.1.644.2.4.0.1023\",\"trap.message^count\":2},{\"event.source\":\"127.0.0.1\",\"trap.oid\":\".*******.4.1.644.2.4.0.1020\",\"trap.message^count\":2},{\"event.source\":\"127.0.0.1\",\"trap.oid\":\".*******.4.1.644.2.4.0.1021\",\"trap.message^count\":2},{\"event.source\":\"127.0.0.1\",\"trap.oid\":\".*******.4.1.644.2.4.0.1020\",\"trap.message^count\":2},{\"event.source\":\"127.0.0.1\",\"trap.oid\":\".*******.4.1.644.2.4.0.1040\",\"trap.message^count\":4},{\"event.source\":\"127.0.0.1\",\"trap.oid\":\".0.0\",\"trap.message^count\":7},{\"event.source\":\"127.0.0.1\",\"trap.oid\":\".*******.*******.5.5\",\"trap.message^count\":20},{\"event.source\":\"127.0.0.1\",\"trap.oid\":\".*******.*******.5.150\",\"trap.message^count\":200}, {\"event.source\":\"127.0.0.1\",\"trap.oid\":\".*******.*******.5.250\",\"trap.message^count\":200}]}"))
            .put(VisualizationConstants.VisualizationDataSource.TRAP_ACKNOWLEDGEMENT.getName(), new JsonObject("{\"result\":[{\"event.source\":\"127.0.0.1\",\"trap.oid\":\".*******.*******.5.4\",\"trap.acknowledged^last\":0},{\"event.source\":\"127.0.0.1\",\"trap.oid\":\".*******.*******.5.2\",\"trap.acknowledged^last\":0},{\"event.source\":\"127.0.0.1\",\"trap.oid\":\".*******.*******.5.3\",\"trap.acknowledged^last\":0},{\"event.source\":\"127.0.0.1\",\"trap.oid\":\".*******.*******.5.1\",\"trap.acknowledged^last\":0}]}"))
            .put("event.history", new JsonObject("{\"result\":[{\"Timestamp\":1698645404000,\"event.id^value\":\"120808604-0-0-600004-linux.login.audit\",\"event^value\":\"LQAAAAsAMTAuMjAuNDAuNzQ4AAAAAwBsb2c5AAAAAgA4NjoAAAAJAGxvY2FsaG9zdDsAAAAEAHNzaGQ8AAAABAA0NjIzPQAAAB8Ac2VjdXJpdHkvYXV0aG9yaXphdGlvbiBtZXNzYWdlcz4AAAANAEluZm9ybWF0aW9uYWw_AAAACQBtaW5kYXJyYXlKAAAADQAxOTIuMTY4LjEuMTE4SwAAAAUANTk2ODI1AAAAEQBMaW51eCBMb2dpbiBBdWRpdEAAAAAGAGZhaWxlZDYAAAAFAExpbnV4NwAAAAIAOTQ\",\"event.pattern.id^value\":78}]}"))
            .put("horizontal.TopN", new JsonObject("{\"result\":[{\"monitor\":\"1\",\"network.service\":\"50000 (IBM Db2)\",\"network.service~port^avg\":50000},{\"monitor\":\"1\",\"network.service\":\"5985 (Powershell)\",\"network.service~port^avg\":5985},{\"monitor\":\"1\",\"network.service\":\"5432 (PostgreSQL)\",\"network.service~port^avg\":5432},{\"monitor\":\"1\",\"network.service\":\"3389 (RDP)\",\"network.service~port^avg\":3389},{\"monitor\":\"1\",\"network.service\":\"3306 (MySQL)\",\"network.service~port^avg\":3306},{\"monitor\":\"1\",\"network.service\":\"1521 (Oracle Database)\",\"network.service~port^avg\":1521},{\"monitor\":\"1\",\"network.service\":\"443 (HTTPS)\",\"network.service~port^avg\":443},{\"monitor\":\"1\",\"network.service\":\"143 (IMAP)\",\"network.service~port^avg\":143},{\"monitor\":\"1\",\"network.service\":\"139 (NetBIOS-SSN)\",\"network.service~port^avg\":139},{\"monitor\":\"1\",\"network.service\":\"110 (POP3)\",\"network.service~port^avg\":110}]}"))
            .put("testMapWithSourceCountry", new JsonObject("{\"result\":[{\"source.country\":\"South Korea\",\"volume.bytes^avg\":2636000,\"source.latitude\":\"35.907757\",\"source.longitude\":\"127.766922\"},{\"source.country\":\"Germany\",\"volume.bytes^avg\":1622500,\"source.latitude\":\"51.165691\",\"source.longitude\":\"10.451526\"},{\"source.country\":\"India\",\"volume.bytes^avg\":2007449,\"source.latitude\":\"20.593684\",\"source.longitude\":\"78.96288\"},{\"source.country\":\"Australia\",\"volume.bytes^avg\":242000,\"source.latitude\":\"-25.274398\",\"source.longitude\":\"133.775136\"},{\"source.country\":\"Singapore\",\"volume.bytes^avg\":1579818,\"source.latitude\":\"1.352083\",\"source.longitude\":\"103.819836\"},{\"source.country\":\"Hong Kong\",\"volume.bytes^avg\":3036000,\"source.latitude\":\"22.396428\",\"source.longitude\":\"114.109497\"},{\"source.country\":\"United States\",\"volume.bytes^avg\":2007932,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\"},{\"source.country\":\"United Kingdom\",\"volume.bytes^avg\":3014548,\"source.latitude\":\"55.378051\",\"source.longitude\":\"-3.435973\"},{\"source.country\":\"Japan\",\"volume.bytes^avg\":1202000,\"source.latitude\":\"36.204824\",\"source.longitude\":\"138.252924\"},{\"source.country\":\"France\",\"volume.bytes^avg\":1576000,\"source.latitude\":\"46.227638\",\"source.longitude\":\"2.213749\"}]}"))
            .put("testMapWithDestinationCountryAndCity", new JsonObject("{\"result\":[{\"destination.country\":\"United States\",\"destination.city\":\"Newark\",\"volume.bytes^avg\":423666,\"destination.latitude\":\"35.7171\",\"destination.longitude\":\"-91.4356\"},{\"destination.country\":\"Vietnam\",\"destination.city\":\"Hanoi\",\"volume.bytes^avg\":116000,\"destination.latitude\":\"21.0292\",\"destination.longitude\":\"105.8526\"},{\"destination.country\":\"Hong Kong\",\"destination.city\":\"Tseung Kwan O\",\"volume.bytes^avg\":124000,\"destination.latitude\":\"22.2908\",\"destination.longitude\":\"114.1501\"},{\"destination.country\":\"Australia\",\"destination.city\":\"Melbourne\",\"volume.bytes^avg\":116000,\"destination.latitude\":\"-37.8159\",\"destination.longitude\":\"144.9669\"},{\"destination.country\":\"France\",\"destination.city\":\"Paris\",\"volume.bytes^avg\":3036000,\"destination.latitude\":\"7.15\",\"destination.longitude\":\"1.0833\"},{\"destination.country\":\"United States\",\"destination.city\":\"Colbert\",\"volume.bytes^avg\":865714,\"destination.latitude\":\"34.035\",\"destination.longitude\":\"-83.2191\"},{\"destination.country\":\"Ireland\",\"destination.city\":\"Dublin\",\"volume.bytes^avg\":1559250,\"destination.latitude\":\"-34.4558\",\"destination.longitude\":\"138.3547\"},{\"destination.country\":\"India\",\"destination.city\":\"Raigarh\",\"volume.bytes^avg\":231428,\"destination.latitude\":\"21.896\",\"destination.longitude\":\"83.4001\"},{\"destination.country\":\"India\",\"destination.city\":\"Jaipur\",\"volume.bytes^avg\":232000,\"destination.latitude\":\"26.9525\",\"destination.longitude\":\"75.7105\"},{\"destination.country\":\"India\",\"destination.city\":\"New Delhi\",\"volume.bytes^avg\":140000,\"destination.latitude\":\"28.6328\",\"destination.longitude\":\"77.2204\"},{\"destination.country\":\"India\",\"destination.city\":\"Pune\",\"volume.bytes^avg\":1027377,\"destination.latitude\":\"18.6161\",\"destination.longitude\":\"73.7286\"},{\"destination.country\":\"United States\",\"destination.city\":\"The Dalles\",\"volume.bytes^avg\":140000,\"destination.latitude\":\"45.5999\",\"destination.longitude\":\"-121.1871\"},{\"destination.country\":\"United States\",\"destination.city\":\"Columbus\",\"volume.bytes^avg\":1422000,\"destination.latitude\":\"32.4783\",\"destination.longitude\":\"-84.8985\"},{\"destination.country\":\"United States\",\"destination.city\":\"Secaucus\",\"volume.bytes^avg\":116000,\"destination.latitude\":\"40.7876\",\"destination.longitude\":\"-74.06\"},{\"destination.country\":\"India\",\"destination.city\":\"Hyderabad\",\"volume.bytes^avg\":234532,\"destination.latitude\":\"25.3507\",\"destination.longitude\":\"68.3534\"},{\"destination.country\":\"India\",\"destination.city\":\"Madurai\",\"volume.bytes^avg\":147542,\"destination.latitude\":\"9.9327\",\"destination.longitude\":\"78.1141\"},{\"destination.country\":\"United States\",\"destination.city\":\"Tappahannock\",\"volume.bytes^avg\":1539481,\"destination.latitude\":\"37.9273\",\"destination.longitude\":\"-76.8545\"},{\"destination.country\":\"India\",\"destination.city\":\"Ahmedabad\",\"volume.bytes^avg\":232571,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\"},{\"destination.country\":\"United States\",\"destination.city\":\"Los Angeles\",\"volume.bytes^avg\":363193,\"destination.latitude\":\"13.3724\",\"destination.longitude\":\"-87.0314\"},{\"destination.country\":\"India\",\"destination.city\":\"Bhopal\",\"volume.bytes^avg\":464972,\"destination.latitude\":\"23.2487\",\"destination.longitude\":\"77.4066\"},{\"destination.country\":\"India\",\"destination.city\":\"Mumbai\",\"volume.bytes^avg\":314148,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\"},{\"destination.country\":\"Japan\",\"destination.city\":\"Osaka\",\"volume.bytes^avg\":1060411,\"destination.latitude\":\"34.6933\",\"destination.longitude\":\"135.5067\"},{\"destination.country\":\"United States\",\"destination.city\":\"Dallas\",\"volume.bytes^avg\":127750,\"destination.latitude\":\"57.6101\",\"destination.longitude\":\"-3.6176\"},{\"destination.country\":\"United States\",\"destination.city\":\"Saluda\",\"volume.bytes^avg\":180000,\"destination.latitude\":\"35.2348\",\"destination.longitude\":\"-82.3351\"},{\"destination.country\":\"India\",\"destination.city\":\"Lucknow\",\"volume.bytes^avg\":362000,\"destination.latitude\":\"26.8756\",\"destination.longitude\":\"80.9115\"},{\"destination.country\":\"United States\",\"destination.city\":\"Kansas City\",\"volume.bytes^avg\":785120,\"destination.latitude\":\"39.0828\",\"destination.longitude\":\"-94.777\"},{\"destination.country\":\"Netherlands\",\"destination.city\":\"Amsterdam\",\"volume.bytes^avg\":3036000,\"destination.latitude\":\"-31.6252\",\"destination.longitude\":\"25.6244\"},{\"destination.country\":\"United States\",\"destination.city\":\"San Jose\",\"volume.bytes^avg\":1507333,\"destination.latitude\":\"15.7494\",\"destination.longitude\":\"121.0832\"},{\"destination.country\":\"India\",\"destination.city\":\"Bengaluru\",\"volume.bytes^avg\":630384,\"destination.latitude\":\"12.9634\",\"destination.longitude\":\"77.5855\"},{\"destination.country\":\"United States\",\"destination.city\":\"Washington\",\"volume.bytes^avg\":1268000,\"destination.latitude\":\"54.9\",\"destination.longitude\":\"-1.5167\"},{\"destination.country\":\"United States\",\"destination.city\":\"Boardman\",\"volume.bytes^avg\":1243090,\"destination.latitude\":\"41.0158\",\"destination.longitude\":\"-80.8041\"},{\"destination.country\":\"United States\",\"destination.city\":\"Boston\",\"volume.bytes^avg\":140000,\"destination.latitude\":\"52.9791\",\"destination.longitude\":\"-0.0269\"},{\"destination.country\":\"United States\",\"destination.city\":\"Council Bluffs\",\"volume.bytes^avg\":148000,\"destination.latitude\":\"41.2615\",\"destination.longitude\":\"-95.8304\"},{\"destination.country\":\"India\",\"destination.city\":\"Delhi\",\"volume.bytes^avg\":217625,\"destination.latitude\":\"28.6542\",\"destination.longitude\":\"77.2373\"},{\"destination.country\":\"France\",\"destination.city\":\"Marseille\",\"volume.bytes^avg\":154400,\"destination.latitude\":\"43.2951\",\"destination.longitude\":\"5.3861\"},{\"destination.country\":\"United States\",\"destination.city\":\"Boydton\",\"volume.bytes^avg\":2234428,\"destination.latitude\":\"36.6534\",\"destination.longitude\":\"-78.375\"},{\"destination.country\":\"Japan\",\"destination.city\":\"Tokyo\",\"volume.bytes^avg\":1249294,\"destination.latitude\":\"35.6837\",\"destination.longitude\":\"139.6805\"},{\"destination.country\":\"United States\",\"destination.city\":\"Mountain View\",\"volume.bytes^avg\":128000,\"destination.latitude\":\"35.824\",\"destination.longitude\":\"-92.0391\"},{\"destination.country\":\"India\",\"destination.city\":\"Limbdi\",\"volume.bytes^avg\":312363,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\"},{\"destination.country\":\"United States\",\"destination.city\":\"Redmond\",\"volume.bytes^avg\":2299869,\"destination.latitude\":\"44.2762\",\"destination.longitude\":\"-121.1847\"},{\"destination.country\":\"Germany\",\"destination.city\":\"Frankfurt am Main\",\"volume.bytes^avg\":123625,\"destination.latitude\":\"50.1188\",\"destination.longitude\":\"8.6843\"},{\"destination.country\":\"United States\",\"destination.city\":\"Ashburn\",\"volume.bytes^avg\":1825744,\"destination.latitude\":\"31.7097\",\"destination.longitude\":\"-83.6625\"},{\"destination.country\":\"United States\",\"destination.city\":\"Cincinnati\",\"volume.bytes^avg\":118000,\"destination.latitude\":\"39.2413\",\"destination.longitude\":\"-84.5455\"},{\"destination.country\":\"United Kingdom\",\"destination.city\":\"London\",\"volume.bytes^avg\":3036000,\"destination.latitude\":\"6.0097\",\"destination.longitude\":\"125.1294\"},{\"destination.country\":\"Sweden\",\"destination.city\":\"Stockholm\",\"volume.bytes^avg\":1112000,\"destination.latitude\":\"59.3274\",\"destination.longitude\":\"18.0653\"},{\"destination.country\":\"Australia\",\"destination.city\":\"Sydney\",\"volume.bytes^avg\":358000,\"destination.latitude\":\"-33.8715\",\"destination.longitude\":\"151.2006\"},{\"destination.country\":\"South Korea\",\"destination.city\":\"Seoul\",\"volume.bytes^avg\":316444,\"destination.latitude\":\"37.5576\",\"destination.longitude\":\"126.9937\"},{\"destination.country\":\"United States\",\"destination.city\":\"El Paso\",\"volume.bytes^avg\":320000,\"destination.latitude\":\"28.6475\",\"destination.longitude\":\"-17.884\"},{\"destination.country\":\"United States\",\"destination.city\":\"San Antonio\",\"volume.bytes^avg\":1769454,\"destination.latitude\":\"14.9591\",\"destination.longitude\":\"120.0913\"},{\"destination.country\":\"India\",\"destination.city\":\"Dombivali\",\"volume.bytes^avg\":403000,\"destination.latitude\":\"19.216\",\"destination.longitude\":\"73.1034\"},{\"destination.country\":\"India\",\"destination.city\":\"Chennai\",\"volume.bytes^avg\":810387,\"destination.latitude\":\"12.8996\",\"destination.longitude\":\"80.2209\"},{\"destination.country\":\"India\",\"destination.city\":\"Ambala\",\"volume.bytes^avg\":204000,\"destination.latitude\":\"30.3557\",\"destination.longitude\":\"76.8019\"},{\"destination.country\":\"Hong Kong\",\"destination.city\":\"Hong Kong\",\"volume.bytes^avg\":988070,\"destination.latitude\":\"22.2842\",\"destination.longitude\":\"114.1759\"},{\"destination.country\":\"United States\",\"destination.city\":\"Des Moines\",\"volume.bytes^avg\":1792666,\"destination.latitude\":\"41.6381\",\"destination.longitude\":\"-93.6203\"}]}"))
            .put("testMapWithSourceCountryAndDestinationCountry", new JsonObject("{\"result\":[{\"destination.country\":\"Japan\",\"source.country\":\"India\",\"volume.bytes^avg\":2753000,\"source.latitude\":\"20.593684\",\"source.longitude\":\"78.96288\",\"destination.latitude\":\"36.204824\",\"destination.longitude\":\"138.252924\"},{\"destination.country\":\"United Kingdom\",\"source.country\":\"United States\",\"volume.bytes^avg\":2805083,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"55.378051\",\"destination.longitude\":\"-3.435973\"},{\"destination.country\":\"Ireland\",\"source.country\":\"United States\",\"volume.bytes^avg\":1915600,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"53.41291\",\"destination.longitude\":\"-8.24389\"},{\"destination.country\":\"United States\",\"source.country\":\"France\",\"volume.bytes^avg\":943000,\"source.latitude\":\"46.227638\",\"source.longitude\":\"2.213749\",\"destination.latitude\":\"37.09024\",\"destination.longitude\":\"-95.712891\"},{\"destination.country\":\"Japan\",\"source.country\":\"India\",\"volume.bytes^avg\":1972000,\"source.latitude\":\"20.593684\",\"source.longitude\":\"78.96288\",\"destination.latitude\":\"36.204824\",\"destination.longitude\":\"138.252924\"},{\"destination.country\":\"India\",\"source.country\":\"Singapore\",\"volume.bytes^avg\":2879459,\"source.latitude\":\"1.352083\",\"source.longitude\":\"103.819836\",\"destination.latitude\":\"20.593684\",\"destination.longitude\":\"78.96288\"},{\"destination.country\":\"United States\",\"source.country\":\"India\",\"volume.bytes^avg\":2454746,\"source.latitude\":\"20.593684\",\"source.longitude\":\"78.96288\",\"destination.latitude\":\"37.09024\",\"destination.longitude\":\"-95.712891\"},{\"destination.country\":\"Japan\",\"source.country\":\"United States\",\"volume.bytes^avg\":2126250,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"36.204824\",\"destination.longitude\":\"138.252924\"},{\"destination.country\":\"United States\",\"source.country\":\"Singapore\",\"volume.bytes^avg\":1580000,\"source.latitude\":\"1.352083\",\"source.longitude\":\"103.819836\",\"destination.latitude\":\"37.09024\",\"destination.longitude\":\"-95.712891\"},{\"destination.country\":\"Sweden\",\"source.country\":\"United States\",\"volume.bytes^avg\":2670000,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"60.128161\",\"destination.longitude\":\"18.643501\"},{\"destination.country\":\"India\",\"source.country\":\"Singapore\",\"volume.bytes^avg\":3012000,\"source.latitude\":\"1.352083\",\"source.longitude\":\"103.819836\",\"destination.latitude\":\"20.593684\",\"destination.longitude\":\"78.96288\"},{\"destination.country\":\"Singapore\",\"source.country\":\"United Kingdom\",\"volume.bytes^avg\":3036000,\"source.latitude\":\"55.378051\",\"source.longitude\":\"-3.435973\",\"destination.latitude\":\"1.352083\",\"destination.longitude\":\"103.819836\"},{\"destination.country\":\"United States\",\"source.country\":\"United States\",\"volume.bytes^avg\":1956842,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"37.09024\",\"destination.longitude\":\"-95.712891\"},{\"destination.country\":\"India\",\"source.country\":\"India\",\"volume.bytes^avg\":2839883,\"source.latitude\":\"20.593684\",\"source.longitude\":\"78.96288\",\"destination.latitude\":\"20.593684\",\"destination.longitude\":\"78.96288\"},{\"destination.country\":\"United States\",\"source.country\":\"United Kingdom\",\"volume.bytes^avg\":3035586,\"source.latitude\":\"55.378051\",\"source.longitude\":\"-3.435973\",\"destination.latitude\":\"37.09024\",\"destination.longitude\":\"-95.712891\"},{\"destination.country\":\"United States\",\"source.country\":\"Hong Kong\",\"volume.bytes^avg\":2819000,\"source.latitude\":\"22.396428\",\"source.longitude\":\"114.109497\",\"destination.latitude\":\"37.09024\",\"destination.longitude\":\"-95.712891\"},{\"destination.country\":\"United States\",\"source.country\":\"Germany\",\"volume.bytes^avg\":116000,\"source.latitude\":\"51.165691\",\"source.longitude\":\"10.451526\",\"destination.latitude\":\"37.09024\",\"destination.longitude\":\"-95.712891\"},{\"destination.country\":\"India\",\"source.country\":\"Germany\",\"volume.bytes^avg\":140000,\"source.latitude\":\"51.165691\",\"source.longitude\":\"10.451526\",\"destination.latitude\":\"20.593684\",\"destination.longitude\":\"78.96288\"},{\"destination.country\":\"Singapore\",\"source.country\":\"Japan\",\"volume.bytes^avg\":322857,\"source.latitude\":\"36.204824\",\"source.longitude\":\"138.252924\",\"destination.latitude\":\"1.352083\",\"destination.longitude\":\"103.819836\"},{\"destination.country\":\"Australia\",\"source.country\":\"India\",\"volume.bytes^avg\":2070666,\"source.latitude\":\"20.593684\",\"source.longitude\":\"78.96288\",\"destination.latitude\":\"-25.274398\",\"destination.longitude\":\"133.775136\"},{\"destination.country\":\"India\",\"source.country\":\"Ireland\",\"volume.bytes^avg\":140000,\"source.latitude\":\"53.41291\",\"source.longitude\":\"-8.24389\",\"destination.latitude\":\"20.593684\",\"destination.longitude\":\"78.96288\"},{\"destination.country\":\"Australia\",\"source.country\":\"United States\",\"volume.bytes^avg\":2048400,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"-25.274398\",\"destination.longitude\":\"133.775136\"},{\"destination.country\":\"India\",\"source.country\":\"United Kingdom\",\"volume.bytes^avg\":3036000,\"source.latitude\":\"55.378051\",\"source.longitude\":\"-3.435973\",\"destination.latitude\":\"20.593684\",\"destination.longitude\":\"78.96288\"},{\"destination.country\":\"Germany\",\"source.country\":\"United States\",\"volume.bytes^avg\":194000,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"51.165691\",\"destination.longitude\":\"10.451526\"},{\"destination.country\":\"United States\",\"source.country\":\"Ireland\",\"volume.bytes^avg\":838000,\"source.latitude\":\"53.41291\",\"source.longitude\":\"-8.24389\",\"destination.latitude\":\"37.09024\",\"destination.longitude\":\"-95.712891\"},{\"destination.country\":\"India\",\"source.country\":\"United States\",\"volume.bytes^avg\":1990045,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"20.593684\",\"destination.longitude\":\"78.96288\"},{\"destination.country\":\"India\",\"source.country\":\"Japan\",\"volume.bytes^avg\":2105368,\"source.latitude\":\"36.204824\",\"source.longitude\":\"138.252924\",\"destination.latitude\":\"20.593684\",\"destination.longitude\":\"78.96288\"},{\"destination.country\":\"India\",\"source.country\":\"France\",\"volume.bytes^avg\":1564000,\"source.latitude\":\"46.227638\",\"source.longitude\":\"2.213749\",\"destination.latitude\":\"20.593684\",\"destination.longitude\":\"78.96288\"},{\"destination.country\":\"France\",\"source.country\":\"India\",\"volume.bytes^avg\":2746000,\"source.latitude\":\"20.593684\",\"source.longitude\":\"78.96288\",\"destination.latitude\":\"46.227638\",\"destination.longitude\":\"2.213749\"},{\"destination.country\":\"Singapore\",\"source.country\":\"United States\",\"volume.bytes^avg\":2401875,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"1.352083\",\"destination.longitude\":\"103.819836\"},{\"destination.country\":\"\",\"source.country\":\"United States\",\"volume.bytes^avg\":1889872,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"\",\"destination.longitude\":\"\"},{\"destination.country\":\"Singapore\",\"source.country\":\"India\",\"volume.bytes^avg\":2850533,\"source.latitude\":\"20.593684\",\"source.longitude\":\"78.96288\",\"destination.latitude\":\"1.352083\",\"destination.longitude\":\"103.819836\"},{\"destination.country\":\"United States\",\"source.country\":\"Japan\",\"volume.bytes^avg\":420695,\"source.latitude\":\"36.204824\",\"source.longitude\":\"138.252924\",\"destination.latitude\":\"37.09024\",\"destination.longitude\":\"-95.712891\"},{\"destination.country\":\"United States\",\"source.country\":\"Denmark\",\"volume.bytes^avg\":116000,\"source.latitude\":\"56.26392\",\"source.longitude\":\"9.501785\",\"destination.latitude\":\"37.09024\",\"destination.longitude\":\"-95.712891\"},{\"destination.country\":\"Hong Kong\",\"source.country\":\"United States\",\"volume.bytes^avg\":3012000,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"22.396428\",\"destination.longitude\":\"114.109497\"},{\"destination.country\":\"France\",\"source.country\":\"United States\",\"volume.bytes^avg\":2306000,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"46.227638\",\"destination.longitude\":\"2.213749\"}]}"))
            .put("testMapWithSourceAndDestination", new JsonObject("{\"result\":[{\"destination.country\":\"Japan\",\"source.country\":\"India\",\"volume.bytes^avg\":2753000,\"source.latitude\":\"20.593684\",\"source.longitude\":\"78.96288\",\"destination.latitude\":\"36.204824\",\"destination.longitude\":\"138.252924\"},{\"destination.country\":\"United Kingdom\",\"source.country\":\"United States\",\"volume.bytes^avg\":2805083,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"55.378051\",\"destination.longitude\":\"-3.435973\"},{\"destination.country\":\"Ireland\",\"source.country\":\"United States\",\"volume.bytes^avg\":1915600,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"53.41291\",\"destination.longitude\":\"-8.24389\"},{\"destination.country\":\"United States\",\"source.country\":\"France\",\"volume.bytes^avg\":943000,\"source.latitude\":\"46.227638\",\"source.longitude\":\"2.213749\",\"destination.latitude\":\"37.09024\",\"destination.longitude\":\"-95.712891\"},{\"destination.country\":\"Japan\",\"source.country\":\"India\",\"volume.bytes^avg\":1972000,\"source.latitude\":\"20.593684\",\"source.longitude\":\"78.96288\",\"destination.latitude\":\"36.204824\",\"destination.longitude\":\"138.252924\"},{\"destination.country\":\"India\",\"source.country\":\"Singapore\",\"volume.bytes^avg\":2879459,\"source.latitude\":\"1.352083\",\"source.longitude\":\"103.819836\",\"destination.latitude\":\"20.593684\",\"destination.longitude\":\"78.96288\"},{\"destination.country\":\"United States\",\"source.country\":\"India\",\"volume.bytes^avg\":2454746,\"source.latitude\":\"20.593684\",\"source.longitude\":\"78.96288\",\"destination.latitude\":\"37.09024\",\"destination.longitude\":\"-95.712891\"},{\"destination.country\":\"Japan\",\"source.country\":\"United States\",\"volume.bytes^avg\":2126250,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"36.204824\",\"destination.longitude\":\"138.252924\"},{\"destination.country\":\"United States\",\"source.country\":\"Singapore\",\"volume.bytes^avg\":1580000,\"source.latitude\":\"1.352083\",\"source.longitude\":\"103.819836\",\"destination.latitude\":\"37.09024\",\"destination.longitude\":\"-95.712891\"},{\"destination.country\":\"Sweden\",\"source.country\":\"United States\",\"volume.bytes^avg\":2670000,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"60.128161\",\"destination.longitude\":\"18.643501\"},{\"destination.country\":\"India\",\"source.country\":\"Singapore\",\"volume.bytes^avg\":3012000,\"source.latitude\":\"1.352083\",\"source.longitude\":\"103.819836\",\"destination.latitude\":\"20.593684\",\"destination.longitude\":\"78.96288\"},{\"destination.country\":\"Singapore\",\"source.country\":\"United Kingdom\",\"volume.bytes^avg\":3036000,\"source.latitude\":\"55.378051\",\"source.longitude\":\"-3.435973\",\"destination.latitude\":\"1.352083\",\"destination.longitude\":\"103.819836\"},{\"destination.country\":\"United States\",\"source.country\":\"United States\",\"volume.bytes^avg\":1956842,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"37.09024\",\"destination.longitude\":\"-95.712891\"},{\"destination.country\":\"India\",\"source.country\":\"India\",\"volume.bytes^avg\":2839883,\"source.latitude\":\"20.593684\",\"source.longitude\":\"78.96288\",\"destination.latitude\":\"20.593684\",\"destination.longitude\":\"78.96288\"},{\"destination.country\":\"United States\",\"source.country\":\"United Kingdom\",\"volume.bytes^avg\":3035586,\"source.latitude\":\"55.378051\",\"source.longitude\":\"-3.435973\",\"destination.latitude\":\"37.09024\",\"destination.longitude\":\"-95.712891\"},{\"destination.country\":\"United States\",\"source.country\":\"Hong Kong\",\"volume.bytes^avg\":2819000,\"source.latitude\":\"22.396428\",\"source.longitude\":\"114.109497\",\"destination.latitude\":\"37.09024\",\"destination.longitude\":\"-95.712891\"},{\"destination.country\":\"United States\",\"source.country\":\"Germany\",\"volume.bytes^avg\":116000,\"source.latitude\":\"51.165691\",\"source.longitude\":\"10.451526\",\"destination.latitude\":\"37.09024\",\"destination.longitude\":\"-95.712891\"},{\"destination.country\":\"India\",\"source.country\":\"Germany\",\"volume.bytes^avg\":140000,\"source.latitude\":\"51.165691\",\"source.longitude\":\"10.451526\",\"destination.latitude\":\"20.593684\",\"destination.longitude\":\"78.96288\"},{\"destination.country\":\"Singapore\",\"source.country\":\"Japan\",\"volume.bytes^avg\":322857,\"source.latitude\":\"36.204824\",\"source.longitude\":\"138.252924\",\"destination.latitude\":\"1.352083\",\"destination.longitude\":\"103.819836\"},{\"destination.country\":\"Australia\",\"source.country\":\"India\",\"volume.bytes^avg\":2070666,\"source.latitude\":\"20.593684\",\"source.longitude\":\"78.96288\",\"destination.latitude\":\"-25.274398\",\"destination.longitude\":\"133.775136\"},{\"destination.country\":\"India\",\"source.country\":\"Ireland\",\"volume.bytes^avg\":140000,\"source.latitude\":\"53.41291\",\"source.longitude\":\"-8.24389\",\"destination.latitude\":\"20.593684\",\"destination.longitude\":\"78.96288\"},{\"destination.country\":\"Australia\",\"source.country\":\"United States\",\"volume.bytes^avg\":2048400,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"-25.274398\",\"destination.longitude\":\"133.775136\"},{\"destination.country\":\"India\",\"source.country\":\"United Kingdom\",\"volume.bytes^avg\":3036000,\"source.latitude\":\"55.378051\",\"source.longitude\":\"-3.435973\",\"destination.latitude\":\"20.593684\",\"destination.longitude\":\"78.96288\"},{\"destination.country\":\"Germany\",\"source.country\":\"United States\",\"volume.bytes^avg\":194000,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"51.165691\",\"destination.longitude\":\"10.451526\"},{\"destination.country\":\"United States\",\"source.country\":\"Ireland\",\"volume.bytes^avg\":838000,\"source.latitude\":\"53.41291\",\"source.longitude\":\"-8.24389\",\"destination.latitude\":\"37.09024\",\"destination.longitude\":\"-95.712891\"},{\"destination.country\":\"India\",\"source.country\":\"United States\",\"volume.bytes^avg\":1990045,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"20.593684\",\"destination.longitude\":\"78.96288\"},{\"destination.country\":\"India\",\"source.country\":\"Japan\",\"volume.bytes^avg\":2105368,\"source.latitude\":\"36.204824\",\"source.longitude\":\"138.252924\",\"destination.latitude\":\"20.593684\",\"destination.longitude\":\"78.96288\"},{\"destination.country\":\"India\",\"source.country\":\"France\",\"volume.bytes^avg\":1564000,\"source.latitude\":\"46.227638\",\"source.longitude\":\"2.213749\",\"destination.latitude\":\"20.593684\",\"destination.longitude\":\"78.96288\"},{\"destination.country\":\"France\",\"source.country\":\"India\",\"volume.bytes^avg\":2746000,\"source.latitude\":\"20.593684\",\"source.longitude\":\"78.96288\",\"destination.latitude\":\"46.227638\",\"destination.longitude\":\"2.213749\"},{\"destination.country\":\"Singapore\",\"source.country\":\"United States\",\"volume.bytes^avg\":2401875,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"1.352083\",\"destination.longitude\":\"103.819836\"},{\"destination.country\":\"\",\"source.country\":\"United States\",\"volume.bytes^avg\":1889872,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"\",\"destination.longitude\":\"\"},{\"destination.country\":\"Singapore\",\"source.country\":\"India\",\"volume.bytes^avg\":2850533,\"source.latitude\":\"20.593684\",\"source.longitude\":\"78.96288\",\"destination.latitude\":\"1.352083\",\"destination.longitude\":\"103.819836\"},{\"destination.country\":\"United States\",\"source.country\":\"Japan\",\"volume.bytes^avg\":420695,\"source.latitude\":\"36.204824\",\"source.longitude\":\"138.252924\",\"destination.latitude\":\"37.09024\",\"destination.longitude\":\"-95.712891\"},{\"destination.country\":\"United States\",\"source.country\":\"Denmark\",\"volume.bytes^avg\":116000,\"source.latitude\":\"56.26392\",\"source.longitude\":\"9.501785\",\"destination.latitude\":\"37.09024\",\"destination.longitude\":\"-95.712891\"},{\"destination.country\":\"Hong Kong\",\"source.country\":\"United States\",\"volume.bytes^avg\":3012000,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"22.396428\",\"destination.longitude\":\"114.109497\"},{\"destination.country\":\"France\",\"source.country\":\"United States\",\"volume.bytes^avg\":2306000,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\",\"destination.latitude\":\"46.227638\",\"destination.longitude\":\"2.213749\"}]}"))
            .put("testMapWithDestinationCountry", new JsonObject("{\"result\":[{\"destination.country\":\"Vietnam\",\"volume.bytes^avg\":116000,\"destination.latitude\":\"14.058324\",\"destination.longitude\":\"108.277199\"},{\"destination.country\":\"Singapore\",\"volume.bytes^avg\":490724,\"destination.latitude\":\"1.352083\",\"destination.longitude\":\"103.819836\"},{\"destination.country\":\"United Kingdom\",\"volume.bytes^avg\":258074,\"destination.latitude\":\"55.378051\",\"destination.longitude\":\"-3.435973\"},{\"destination.country\":\"Japan\",\"volume.bytes^avg\":1095140,\"destination.latitude\":\"36.204824\",\"destination.longitude\":\"138.252924\"},{\"destination.country\":\"Ireland\",\"volume.bytes^avg\":1723333,\"destination.latitude\":\"53.41291\",\"destination.longitude\":\"-8.24389\"},{\"destination.country\":\"India\",\"volume.bytes^avg\":474168,\"destination.latitude\":\"20.593684\",\"destination.longitude\":\"78.96288\"},{\"destination.country\":\"Germany\",\"volume.bytes^avg\":123176,\"destination.latitude\":\"51.165691\",\"destination.longitude\":\"10.451526\"},{\"destination.country\":\"Netherlands\",\"volume.bytes^avg\":3036000,\"destination.latitude\":\"52.132633\",\"destination.longitude\":\"5.291266\"},{\"destination.country\":\"United States\",\"volume.bytes^avg\":759760,\"destination.latitude\":\"37.09024\",\"destination.longitude\":\"-95.712891\"},{\"destination.country\":\"Sweden\",\"volume.bytes^avg\":1112000,\"destination.latitude\":\"60.128161\",\"destination.longitude\":\"18.643501\"},{\"destination.country\":\"South Korea\",\"volume.bytes^avg\":663090,\"destination.latitude\":\"35.907757\",\"destination.longitude\":\"127.766922\"},{\"destination.country\":\"Australia\",\"volume.bytes^avg\":498421,\"destination.latitude\":\"-25.274398\",\"destination.longitude\":\"133.775136\"},{\"destination.country\":\"Hong Kong\",\"volume.bytes^avg\":886151,\"destination.latitude\":\"22.396428\",\"destination.longitude\":\"114.109497\"},{\"destination.country\":\"France\",\"volume.bytes^avg\":634666,\"destination.latitude\":\"46.227638\",\"destination.longitude\":\"2.213749\"}]}"))
            .put("testMapWithDestinationCity", new JsonObject("{\"result\":[{\"destination.city\":\"Mountain View\",\"volume.bytes^avg\":126285,\"destination.latitude\":\"35.824\",\"destination.longitude\":\"-92.0391\"},{\"destination.city\":\"Marseille\",\"volume.bytes^avg\":154400,\"destination.latitude\":\"43.2951\",\"destination.longitude\":\"5.3861\"},{\"destination.city\":\"Sacramento\",\"volume.bytes^avg\":633000,\"destination.latitude\":\"-19.8804\",\"destination.longitude\":\"-47.226\"},{\"destination.city\":\"Boardman\",\"volume.bytes^avg\":1627285,\"destination.latitude\":\"41.0158\",\"destination.longitude\":\"-80.8041\"},{\"destination.city\":\"Bengaluru\",\"volume.bytes^avg\":639548,\"destination.latitude\":\"12.9634\",\"destination.longitude\":\"77.5855\"},{\"destination.city\":\"Melbourne\",\"volume.bytes^avg\":116000,\"destination.latitude\":\"-37.8159\",\"destination.longitude\":\"144.9669\"},{\"destination.city\":\"Sydney\",\"volume.bytes^avg\":358000,\"destination.latitude\":\"-33.8715\",\"destination.longitude\":\"151.2006\"},{\"destination.city\":\"Limbdi\",\"volume.bytes^avg\":311200,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\"},{\"destination.city\":\"New Delhi\",\"volume.bytes^avg\":140000,\"destination.latitude\":\"28.6328\",\"destination.longitude\":\"77.2204\"},{\"destination.city\":\"Tokyo\",\"volume.bytes^avg\":1171710,\"destination.latitude\":\"35.6837\",\"destination.longitude\":\"139.6805\"},{\"destination.city\":\"Raigarh\",\"volume.bytes^avg\":231428,\"destination.latitude\":\"21.896\",\"destination.longitude\":\"83.4001\"},{\"destination.city\":\"The Dalles\",\"volume.bytes^avg\":140000,\"destination.latitude\":\"45.5999\",\"destination.longitude\":\"-121.1871\"},{\"destination.city\":\"Hong Kong\",\"volume.bytes^avg\":935451,\"destination.latitude\":\"22.2842\",\"destination.longitude\":\"114.1759\"},{\"destination.city\":\"Ahmedabad\",\"volume.bytes^avg\":253500,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\"},{\"destination.city\":\"Columbus\",\"volume.bytes^avg\":1422000,\"destination.latitude\":\"32.4783\",\"destination.longitude\":\"-84.8985\"},{\"destination.city\":\"Madurai\",\"volume.bytes^avg\":147542,\"destination.latitude\":\"9.9327\",\"destination.longitude\":\"78.1141\"},{\"destination.city\":\"Des Moines\",\"volume.bytes^avg\":1954000,\"destination.latitude\":\"41.6381\",\"destination.longitude\":\"-93.6203\"},{\"destination.city\":\"Dublin\",\"volume.bytes^avg\":1559250,\"destination.latitude\":\"-34.4558\",\"destination.longitude\":\"138.3547\"},{\"destination.city\":\"Ashburn\",\"volume.bytes^avg\":1839153,\"destination.latitude\":\"31.7097\",\"destination.longitude\":\"-83.6625\"},{\"destination.city\":\"Mumbai\",\"volume.bytes^avg\":312915,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\"},{\"destination.city\":\"London\",\"volume.bytes^avg\":1734000,\"destination.latitude\":\"6.0097\",\"destination.longitude\":\"125.1294\"},{\"destination.city\":\"Boydton\",\"volume.bytes^avg\":2234428,\"destination.latitude\":\"36.6534\",\"destination.longitude\":\"-78.375\"},{\"destination.city\":\"Cincinnati\",\"volume.bytes^avg\":118000,\"destination.latitude\":\"39.2413\",\"destination.longitude\":\"-84.5455\"},{\"destination.city\":\"Chennai\",\"volume.bytes^avg\":810387,\"destination.latitude\":\"12.8996\",\"destination.longitude\":\"80.2209\"},{\"destination.city\":\"Osaka\",\"volume.bytes^avg\":1015424,\"destination.latitude\":\"34.6933\",\"destination.longitude\":\"135.5067\"},{\"destination.city\":\"Los Angeles\",\"volume.bytes^avg\":348771,\"destination.latitude\":\"13.3724\",\"destination.longitude\":\"-87.0314\"},{\"destination.city\":\"Ambala\",\"volume.bytes^avg\":204000,\"destination.latitude\":\"30.3557\",\"destination.longitude\":\"76.8019\"},{\"destination.city\":\"San Jose\",\"volume.bytes^avg\":1533923,\"destination.latitude\":\"15.7494\",\"destination.longitude\":\"121.0832\"},{\"destination.city\":\"Delhi\",\"volume.bytes^avg\":215272,\"destination.latitude\":\"28.6542\",\"destination.longitude\":\"77.2373\"},{\"destination.city\":\"Frankfurt am Main\",\"volume.bytes^avg\":123176,\"destination.latitude\":\"50.1188\",\"destination.longitude\":\"8.6843\"},{\"destination.city\":\"Tseung Kwan O\",\"volume.bytes^avg\":124000,\"destination.latitude\":\"22.2908\",\"destination.longitude\":\"114.1501\"},{\"destination.city\":\"Jaipur\",\"volume.bytes^avg\":232000,\"destination.latitude\":\"26.9525\",\"destination.longitude\":\"75.7105\"},{\"destination.city\":\"Secaucus\",\"volume.bytes^avg\":116000,\"destination.latitude\":\"40.7876\",\"destination.longitude\":\"-74.06\"},{\"destination.city\":\"Dombivali\",\"volume.bytes^avg\":403000,\"destination.latitude\":\"19.216\",\"destination.longitude\":\"73.1034\"},{\"destination.city\":\"Newark\",\"volume.bytes^avg\":383142,\"destination.latitude\":\"35.7171\",\"destination.longitude\":\"-91.4356\"},{\"destination.city\":\"Council Bluffs\",\"volume.bytes^avg\":148000,\"destination.latitude\":\"41.2615\",\"destination.longitude\":\"-95.8304\"},{\"destination.city\":\"Saluda\",\"volume.bytes^avg\":184000,\"destination.latitude\":\"35.2348\",\"destination.longitude\":\"-82.3351\"},{\"destination.city\":\"Redmond\",\"volume.bytes^avg\":2299869,\"destination.latitude\":\"44.2762\",\"destination.longitude\":\"-121.1847\"},{\"destination.city\":\"Washington\",\"volume.bytes^avg\":1152800,\"destination.latitude\":\"54.9\",\"destination.longitude\":\"-1.5167\"},{\"destination.city\":\"Paris\",\"volume.bytes^avg\":3036000,\"destination.latitude\":\"7.15\",\"destination.longitude\":\"1.0833\"},{\"destination.city\":\"Kansas City\",\"volume.bytes^avg\":805925,\"destination.latitude\":\"39.0828\",\"destination.longitude\":\"-94.777\"},{\"destination.city\":\"Hanoi\",\"volume.bytes^avg\":116000,\"destination.latitude\":\"21.0292\",\"destination.longitude\":\"105.8526\"},{\"destination.city\":\"Seoul\",\"volume.bytes^avg\":663090,\"destination.latitude\":\"37.5576\",\"destination.longitude\":\"126.9937\"},{\"destination.city\":\"Dallas\",\"volume.bytes^avg\":127750,\"destination.latitude\":\"57.6101\",\"destination.longitude\":\"-3.6176\"},{\"destination.city\":\"Colbert\",\"volume.bytes^avg\":826918,\"destination.latitude\":\"34.035\",\"destination.longitude\":\"-83.2191\"},{\"destination.city\":\"Hyderabad\",\"volume.bytes^avg\":242767,\"destination.latitude\":\"25.3507\",\"destination.longitude\":\"68.3534\"},{\"destination.city\":\"Bhopal\",\"volume.bytes^avg\":456421,\"destination.latitude\":\"23.2487\",\"destination.longitude\":\"77.4066\"},{\"destination.city\":\"Lucknow\",\"volume.bytes^avg\":362000,\"destination.latitude\":\"26.8756\",\"destination.longitude\":\"80.9115\"},{\"destination.city\":\"Tappahannock\",\"volume.bytes^avg\":1593800,\"destination.latitude\":\"37.9273\",\"destination.longitude\":\"-76.8545\"},{\"destination.city\":\"Nashville\",\"volume.bytes^avg\":118000,\"destination.latitude\":\"33.9581\",\"destination.longitude\":\"-93.875\"},{\"destination.city\":\"Stockholm\",\"volume.bytes^avg\":1112000,\"destination.latitude\":\"59.3274\",\"destination.longitude\":\"18.0653\"},{\"destination.city\":\"Boston\",\"volume.bytes^avg\":140000,\"destination.latitude\":\"52.9791\",\"destination.longitude\":\"-0.0269\"},{\"destination.city\":\"El Paso\",\"volume.bytes^avg\":320000,\"destination.latitude\":\"28.6475\",\"destination.longitude\":\"-17.884\"},{\"destination.city\":\"Amsterdam\",\"volume.bytes^avg\":3036000,\"destination.latitude\":\"-31.6252\",\"destination.longitude\":\"25.6244\"},{\"destination.city\":\"San Antonio\",\"volume.bytes^avg\":1518769,\"destination.latitude\":\"14.9591\",\"destination.longitude\":\"120.0913\"},{\"destination.city\":\"Pune\",\"volume.bytes^avg\":1004417,\"destination.latitude\":\"18.6161\",\"destination.longitude\":\"73.7286\"}]}"))
            .put("testMapWithSourceCity", new JsonObject("{\"result\":[{\"source.city\":\"Amsterdam\",\"volume.bytes^avg\":2092000,\"source.latitude\":\"-31.6252\",\"source.longitude\":\"25.6244\"},{\"source.city\":\"Sydney\",\"volume.bytes^avg\":206250,\"source.latitude\":\"-33.8715\",\"source.longitude\":\"151.2006\"},{\"source.city\":\"Jaipur\",\"volume.bytes^avg\":1219100,\"source.latitude\":\"26.9525\",\"source.longitude\":\"75.7105\"},{\"source.city\":\"Boston\",\"volume.bytes^avg\":3036000,\"source.latitude\":\"52.9791\",\"source.longitude\":\"-0.0269\"},{\"source.city\":\"Madurai\",\"volume.bytes^avg\":1921947,\"source.latitude\":\"9.9327\",\"source.longitude\":\"78.1141\"},{\"source.city\":\"Los Angeles\",\"volume.bytes^avg\":437855,\"source.latitude\":\"13.3724\",\"source.longitude\":\"-87.0314\"},{\"source.city\":\"Las Vegas\",\"volume.bytes^avg\":204000,\"source.latitude\":\"9.5426\",\"source.longitude\":\"-68.6361\"},{\"source.city\":\"San Antonio\",\"volume.bytes^avg\":135333,\"source.latitude\":\"14.9591\",\"source.longitude\":\"120.0913\"},{\"source.city\":\"Rajkot\",\"volume.bytes^avg\":383428,\"source.latitude\":\"22.2904\",\"source.longitude\":\"70.7915\"},{\"source.city\":\"North Charleston\",\"volume.bytes^avg\":1536000,\"source.latitude\":\"32.8608\",\"source.longitude\":\"-79.9746\"},{\"source.city\":\"Laucha\",\"volume.bytes^avg\":116000,\"source.latitude\":\"51.226\",\"source.longitude\":\"11.6762\"},{\"source.city\":\"Columbus\",\"volume.bytes^avg\":3036000,\"source.latitude\":\"32.4783\",\"source.longitude\":\"-84.8985\"},{\"source.city\":\"Boardman\",\"volume.bytes^avg\":1037333,\"source.latitude\":\"41.0158\",\"source.longitude\":\"-80.8041\"},{\"source.city\":\"Delhi\",\"volume.bytes^avg\":3029422,\"source.latitude\":\"28.6542\",\"source.longitude\":\"77.2373\"},{\"source.city\":\"The Dalles\",\"volume.bytes^avg\":2997005,\"source.latitude\":\"45.5999\",\"source.longitude\":\"-121.1871\"},{\"source.city\":\"Marseille\",\"volume.bytes^avg\":1592000,\"source.latitude\":\"43.2951\",\"source.longitude\":\"5.3861\"},{\"source.city\":\"San Jose\",\"volume.bytes^avg\":715666,\"source.latitude\":\"15.7494\",\"source.longitude\":\"121.0832\"},{\"source.city\":\"Hyderabad\",\"volume.bytes^avg\":2913602,\"source.latitude\":\"25.3507\",\"source.longitude\":\"68.3534\"},{\"source.city\":\"Gurugram\",\"volume.bytes^avg\":218000,\"source.latitude\":\"28.4597\",\"source.longitude\":\"77.0282\"},{\"source.city\":\"Tseung Kwan O\",\"volume.bytes^avg\":3036000,\"source.latitude\":\"22.2908\",\"source.longitude\":\"114.1501\"},{\"source.city\":\"Clifton\",\"volume.bytes^avg\":140000,\"source.latitude\":\"-34.2633\",\"source.longitude\":\"150.9716\"},{\"source.city\":\"New York\",\"volume.bytes^avg\":3036000,\"source.latitude\":\"53.0834\",\"source.longitude\":\"-0.4292\"},{\"source.city\":\"Seattle\",\"volume.bytes^avg\":208000,\"source.latitude\":\"47.4902\",\"source.longitude\":\"-122.3004\"},{\"source.city\":\"Newark\",\"volume.bytes^avg\":795076,\"source.latitude\":\"35.7171\",\"source.longitude\":\"-91.4356\"},{\"source.city\":\"Sacramento\",\"volume.bytes^avg\":3036000,\"source.latitude\":\"-19.8804\",\"source.longitude\":\"-47.226\"},{\"source.city\":\"Colbert\",\"volume.bytes^avg\":1206000,\"source.latitude\":\"34.035\",\"source.longitude\":\"-83.2191\"},{\"source.city\":\"Des Moines\",\"volume.bytes^avg\":399454,\"source.latitude\":\"41.6381\",\"source.longitude\":\"-93.6203\"},{\"source.city\":\"Mountain View\",\"volume.bytes^avg\":728800,\"source.latitude\":\"35.824\",\"source.longitude\":\"-92.0391\"},{\"source.city\":\"El Paso\",\"volume.bytes^avg\":116000,\"source.latitude\":\"28.6475\",\"source.longitude\":\"-17.884\"},{\"source.city\":\"Limbdi\",\"volume.bytes^avg\":2151047,\"source.latitude\":\"22.5677\",\"source.longitude\":\"71.8106\"},{\"source.city\":\"Redmond\",\"volume.bytes^avg\":2256612,\"source.latitude\":\"44.2762\",\"source.longitude\":\"-121.1847\"},{\"source.city\":\"Seoul\",\"volume.bytes^avg\":1132363,\"source.latitude\":\"37.5576\",\"source.longitude\":\"126.9937\"},{\"source.city\":\"Dublin\",\"volume.bytes^avg\":611555,\"source.latitude\":\"-34.4558\",\"source.longitude\":\"138.3547\"},{\"source.city\":\"Boydton\",\"volume.bytes^avg\":342421,\"source.latitude\":\"36.6534\",\"source.longitude\":\"-78.375\"},{\"source.city\":\"Nashville\",\"volume.bytes^avg\":1406000,\"source.latitude\":\"33.9581\",\"source.longitude\":\"-93.875\"},{\"source.city\":\"Mumbai\",\"volume.bytes^avg\":1903409,\"source.latitude\":\"19.0748\",\"source.longitude\":\"72.8856\"},{\"source.city\":\"Tappahannock\",\"volume.bytes^avg\":398487,\"source.latitude\":\"37.9273\",\"source.longitude\":\"-76.8545\"},{\"source.city\":\"Osaka\",\"volume.bytes^avg\":925644,\"source.latitude\":\"34.6933\",\"source.longitude\":\"135.5067\"},{\"source.city\":\"Dallas\",\"volume.bytes^avg\":1619428,\"source.latitude\":\"57.6101\",\"source.longitude\":\"-3.6176\"},{\"source.city\":\"Hong Kong\",\"volume.bytes^avg\":1266763,\"source.latitude\":\"22.2842\",\"source.longitude\":\"114.1759\"},{\"source.city\":\"London\",\"volume.bytes^avg\":1420000,\"source.latitude\":\"6.0097\",\"source.longitude\":\"125.1294\"},{\"source.city\":\"Tokyo\",\"volume.bytes^avg\":1002112,\"source.latitude\":\"35.6837\",\"source.longitude\":\"139.6805\"},{\"source.city\":\"Dombivali\",\"volume.bytes^avg\":2054666,\"source.latitude\":\"19.216\",\"source.longitude\":\"73.1034\"},{\"source.city\":\"Frankfurt am Main\",\"volume.bytes^avg\":742352,\"source.latitude\":\"50.1188\",\"source.longitude\":\"8.6843\"},{\"source.city\":\"Saluda\",\"volume.bytes^avg\":271000,\"source.latitude\":\"35.2348\",\"source.longitude\":\"-82.3351\"},{\"source.city\":\"Ashburn\",\"volume.bytes^avg\":450947,\"source.latitude\":\"31.7097\",\"source.longitude\":\"-83.6625\"},{\"source.city\":\"Chennai\",\"volume.bytes^avg\":2290920,\"source.latitude\":\"12.8996\",\"source.longitude\":\"80.2209\"},{\"source.city\":\"Kansas City\",\"volume.bytes^avg\":619206,\"source.latitude\":\"39.0828\",\"source.longitude\":\"-94.777\"},{\"source.city\":\"Washington\",\"volume.bytes^avg\":450727,\"source.latitude\":\"54.9\",\"source.longitude\":\"-1.5167\"},{\"source.city\":\"Detroit\",\"volume.bytes^avg\":2948000,\"source.latitude\":\"34.0258\",\"source.longitude\":\"-88.1687\"},{\"source.city\":\"Maiden\",\"volume.bytes^avg\":3036000,\"source.latitude\":\"35.5759\",\"source.longitude\":\"-81.1745\"},{\"source.city\":\"Secaucus\",\"volume.bytes^avg\":228666,\"source.latitude\":\"40.7876\",\"source.longitude\":\"-74.06\"},{\"source.city\":\"Pune\",\"volume.bytes^avg\":1229801,\"source.latitude\":\"18.6161\",\"source.longitude\":\"73.7286\"},{\"source.city\":\"Lucknow\",\"volume.bytes^avg\":116000,\"source.latitude\":\"26.8756\",\"source.longitude\":\"80.9115\"},{\"source.city\":\"Bhopal\",\"volume.bytes^avg\":2272488,\"source.latitude\":\"23.2487\",\"source.longitude\":\"77.4066\"},{\"source.city\":\"Paris\",\"volume.bytes^avg\":1572000,\"source.latitude\":\"7.15\",\"source.longitude\":\"1.0833\"},{\"source.city\":\"Bengaluru\",\"volume.bytes^avg\":1631846,\"source.latitude\":\"12.9634\",\"source.longitude\":\"77.5855\"},{\"source.city\":\"Ahmedabad\",\"volume.bytes^avg\":2640100,\"source.latitude\":\"23.0276\",\"source.longitude\":\"72.5871\"},{\"source.city\":\"Raigarh\",\"volume.bytes^avg\":2633333,\"source.latitude\":\"21.896\",\"source.longitude\":\"83.4001\"},{\"source.city\":\"Council Bluffs\",\"volume.bytes^avg\":140000,\"source.latitude\":\"41.2615\",\"source.longitude\":\"-95.8304\"},{\"source.city\":\"Slough\",\"volume.bytes^avg\":3012000,\"source.latitude\":\"51.5368\",\"source.longitude\":\"-0.6718\"}]}"))
            .put("testLogGroupMapWithSourceCountry", new JsonObject("{\"result\":[{\"fortinet.traffic.source.country\":\"France\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"46.227638\",\"source.longitude\":\"2.213749\"},{\"fortinet.traffic.source.country\":\"United States\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"37.09024\",\"source.longitude\":\"-95.712891\"},{\"fortinet.traffic.source.country\":\"United Kingdom\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"55.378051\",\"source.longitude\":\"-3.435973\"},{\"fortinet.traffic.source.country\":\"China\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"35.86166\",\"source.longitude\":\"104.195397\"},{\"fortinet.traffic.source.country\":\"Germany\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"51.165691\",\"source.longitude\":\"10.451526\"},{\"fortinet.traffic.source.country\":\"Bulgaria\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"42.733883\",\"source.longitude\":\"25.48583\"},{\"fortinet.traffic.source.country\":\"Ireland\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"53.41291\",\"source.longitude\":\"-8.24389\"},{\"fortinet.traffic.source.country\":\"Australia\",\"fortinet.traffic.bytes^avg\":18,\"source.latitude\":\"-25.274398\",\"source.longitude\":\"133.775136\"},{\"fortinet.traffic.source.country\":\"Sweden\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"60.128161\",\"source.longitude\":\"18.643501\"},{\"fortinet.traffic.source.country\":\"Canada\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"56.130366\",\"source.longitude\":\"-106.346771\"},{\"fortinet.traffic.source.country\":\"India\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"20.593684\",\"source.longitude\":\"78.96288\"},{\"fortinet.traffic.source.country\":\"Singapore\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"1.352083\",\"source.longitude\":\"103.819836\"},{\"fortinet.traffic.source.country\":\"Netherlands\",\"fortinet.traffic.bytes^avg\":199,\"source.latitude\":\"52.132633\",\"source.longitude\":\"5.291266\"}]}"))
            .put("testLogGroupMapWithSourceCity", new JsonObject("{\"result\":[{\"fortinet.traffic.source.country\":\"Germany\",\"fortinet.traffic.source.city\":\"Frankfurt am Main\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"50.1188\",\"source.longitude\":\"8.6843\"},{\"fortinet.traffic.source.country\":\"Chile\",\"fortinet.traffic.source.city\":\"Santiago\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"17.2956\",\"source.longitude\":\"120.4412\"},{\"fortinet.traffic.source.country\":\"United Kingdom\",\"fortinet.traffic.source.city\":\"Slough\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"51.5368\",\"source.longitude\":\"-0.6718\"},{\"fortinet.traffic.source.country\":\"Sweden\",\"fortinet.traffic.source.city\":\"Maersta\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"59.6131\",\"source.longitude\":\"17.8495\"},{\"fortinet.traffic.source.country\":\"India\",\"fortinet.traffic.source.city\":\"Dhekiajuli\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"26.7021\",\"source.longitude\":\"92.4813\"},{\"fortinet.traffic.source.country\":\"Netherlands\",\"fortinet.traffic.source.city\":\"Alblasserdam\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"51.8572\",\"source.longitude\":\"4.6766\"},{\"fortinet.traffic.source.country\":\"India\",\"fortinet.traffic.source.city\":\"Bengaluru\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"12.9634\",\"source.longitude\":\"77.5855\"},{\"fortinet.traffic.source.country\":\"India\",\"fortinet.traffic.source.city\":\"Kochi\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"9.9185\",\"source.longitude\":\"76.2558\"},{\"fortinet.traffic.source.country\":\"United States\",\"fortinet.traffic.source.city\":\"Cazadero\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"38.5923\",\"source.longitude\":\"-123.1946\"},{\"fortinet.traffic.source.country\":\"Taiwan\",\"fortinet.traffic.source.city\":\"New Taipei\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"24.9466\",\"source.longitude\":\"121.586\"},{\"fortinet.traffic.source.country\":\"Egypt\",\"fortinet.traffic.source.city\":\"Cairo\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"30.0588\",\"source.longitude\":\"31.2268\"},{\"fortinet.traffic.source.country\":\"Korea, Republic of\",\"fortinet.traffic.source.city\":\"Buk-gu\",\"fortinet.traffic.bytes^avg\":187,\"source.latitude\":\"35.9034\",\"source.longitude\":\"128.5923\"},{\"fortinet.traffic.source.country\":\"India\",\"fortinet.traffic.source.city\":\"Nellikkuppam\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"11.7755\",\"source.longitude\":\"79.6702\"},{\"fortinet.traffic.source.country\":\"China\",\"fortinet.traffic.source.city\":\"Hangzhou\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"30.2994\",\"source.longitude\":\"120.1612\"},{\"fortinet.traffic.source.country\":\"India\",\"fortinet.traffic.source.city\":\"Hyderabad\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"25.3507\",\"source.longitude\":\"68.3534\"},{\"fortinet.traffic.source.country\":\"Australia\",\"fortinet.traffic.source.city\":\"unknown\",\"fortinet.traffic.bytes^avg\":23,\"source.latitude\":\"-2.0001\",\"source.longitude\":\"30.0\"},{\"fortinet.traffic.source.country\":\"Netherlands\",\"fortinet.traffic.source.city\":\"Delhi\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"28.6542\",\"source.longitude\":\"77.2373\"},{\"fortinet.traffic.source.country\":\"Hong Kong\",\"fortinet.traffic.source.city\":\"unknown\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"-2.0001\",\"source.longitude\":\"30.0\"},{\"fortinet.traffic.source.country\":\"India\",\"fortinet.traffic.source.city\":\"Pune\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"18.6161\",\"source.longitude\":\"73.7286\"},{\"fortinet.traffic.source.country\":\"China\",\"fortinet.traffic.source.city\":\"Chengdu\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"30.6498\",\"source.longitude\":\"104.0555\"},{\"fortinet.traffic.source.country\":\"Romania\",\"fortinet.traffic.source.city\":\"unknown\",\"fortinet.traffic.bytes^avg\":200,\"source.latitude\":\"-2.0001\",\"source.longitude\":\"30.0\"},{\"fortinet.traffic.source.country\":\"China\",\"fortinet.traffic.source.city\":\"Lanzhou\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"36.0647\",\"source.longitude\":\"103.8397\"},{\"fortinet.traffic.source.country\":\"Netherlands\",\"fortinet.traffic.source.city\":\"Amsterdam\",\"fortinet.traffic.bytes^avg\":1,\"source.latitude\":\"-31.6252\",\"source.longitude\":\"25.6244\"},{\"fortinet.traffic.source.country\":\"China\",\"fortinet.traffic.source.city\":\"Beijing\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"39.9143\",\"source.longitude\":\"116.3861\"},{\"fortinet.traffic.source.country\":\"China\",\"fortinet.traffic.source.city\":\"Linfen\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"36.0964\",\"source.longitude\":\"111.5218\"},{\"fortinet.traffic.source.country\":\"India\",\"fortinet.traffic.source.city\":\"Thrissur\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"10.516\",\"source.longitude\":\"76.2157\"},{\"fortinet.traffic.source.country\":\"India\",\"fortinet.traffic.source.city\":\"Chennai\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"12.8996\",\"source.longitude\":\"80.2209\"},{\"fortinet.traffic.source.country\":\"China\",\"fortinet.traffic.source.city\":\"Linyi\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"35.0624\",\"source.longitude\":\"118.3364\"},{\"fortinet.traffic.source.country\":\"United Kingdom\",\"fortinet.traffic.source.city\":\"unknown\",\"fortinet.traffic.bytes^avg\":44,\"source.latitude\":\"-2.0001\",\"source.longitude\":\"30.0\"},{\"fortinet.traffic.source.country\":\"India\",\"fortinet.traffic.source.city\":\"Visakhapatnam\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"17.6774\",\"source.longitude\":\"83.2036\"},{\"fortinet.traffic.source.country\":\"India\",\"fortinet.traffic.source.city\":\"Veraval\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"20.9094\",\"source.longitude\":\"70.364\"},{\"fortinet.traffic.source.country\":\"United States\",\"fortinet.traffic.source.city\":\"Seattle\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"47.4902\",\"source.longitude\":\"-122.3004\"},{\"fortinet.traffic.source.country\":\"Germany\",\"fortinet.traffic.source.city\":\"Los Angeles\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"13.3724\",\"source.longitude\":\"-87.0314\"},{\"fortinet.traffic.source.country\":\"United States\",\"fortinet.traffic.source.city\":\"Santa Clara\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"15.7063\",\"source.longitude\":\"120.7158\"},{\"fortinet.traffic.source.country\":\"United Kingdom\",\"fortinet.traffic.source.city\":\"London\",\"fortinet.traffic.bytes^avg\":17,\"source.latitude\":\"6.0097\",\"source.longitude\":\"125.1294\"},{\"fortinet.traffic.source.country\":\"India\",\"fortinet.traffic.source.city\":\"Gurugram\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"28.4597\",\"source.longitude\":\"77.0282\"},{\"fortinet.traffic.source.country\":\"Bulgaria\",\"fortinet.traffic.source.city\":\"unknown\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"-2.0001\",\"source.longitude\":\"30.0\"},{\"fortinet.traffic.source.country\":\"United States\",\"fortinet.traffic.source.city\":\"Staten Island\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"40.6063\",\"source.longitude\":\"-74.1774\"},{\"fortinet.traffic.source.country\":\"Russian Federation\",\"fortinet.traffic.source.city\":\"unknown\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"-2.0001\",\"source.longitude\":\"30.0\"},{\"fortinet.traffic.source.country\":\"Thailand\",\"fortinet.traffic.source.city\":\"Narathiwat\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"5.9329\",\"source.longitude\":\"101.899\"},{\"fortinet.traffic.source.country\":\"Canada\",\"fortinet.traffic.source.city\":\"Beauharnois\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"45.3161\",\"source.longitude\":\"-73.8736\"},{\"fortinet.traffic.source.country\":\"Netherlands\",\"fortinet.traffic.source.city\":\"Eygelshoven\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"50.8897\",\"source.longitude\":\"6.0563\"},{\"fortinet.traffic.source.country\":\"United States\",\"fortinet.traffic.source.city\":\"unknown\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"-2.0001\",\"source.longitude\":\"30.0\"},{\"fortinet.traffic.source.country\":\"India\",\"fortinet.traffic.source.city\":\"Etah\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"27.5588\",\"source.longitude\":\"78.6569\"},{\"fortinet.traffic.source.country\":\"France\",\"fortinet.traffic.source.city\":\"unknown\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"-2.0001\",\"source.longitude\":\"30.0\"},{\"fortinet.traffic.source.country\":\"India\",\"fortinet.traffic.source.city\":\"Ghaziabad\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"28.665\",\"source.longitude\":\"77.4477\"},{\"fortinet.traffic.source.country\":\"Ireland\",\"fortinet.traffic.source.city\":\"Dublin\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"-34.4558\",\"source.longitude\":\"138.3547\"},{\"fortinet.traffic.source.country\":\"United States\",\"fortinet.traffic.source.city\":\"Mosier\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"45.6514\",\"source.longitude\":\"-121.3833\"},{\"fortinet.traffic.source.country\":\"Netherlands\",\"fortinet.traffic.source.city\":\"Ashburn\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"31.7097\",\"source.longitude\":\"-83.6625\"},{\"fortinet.traffic.source.country\":\"India\",\"fortinet.traffic.source.city\":\"Mannargudi\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"10.6681\",\"source.longitude\":\"79.4504\"},{\"fortinet.traffic.source.country\":\"India\",\"fortinet.traffic.source.city\":\"Thanjavur\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"10.7831\",\"source.longitude\":\"79.1359\"},{\"fortinet.traffic.source.country\":\"India\",\"fortinet.traffic.source.city\":\"unknown\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"-2.0001\",\"source.longitude\":\"30.0\"},{\"fortinet.traffic.source.country\":\"Korea, Republic of\",\"fortinet.traffic.source.city\":\"Jeju City\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"33.5109\",\"source.longitude\":\"126.5264\"},{\"fortinet.traffic.source.country\":\"China\",\"fortinet.traffic.source.city\":\"Ningbo\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"29.8797\",\"source.longitude\":\"121.5513\"},{\"fortinet.traffic.source.country\":\"United States\",\"fortinet.traffic.source.city\":\"San Francisco\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"15.9667\",\"source.longitude\":\"120.6833\"},{\"fortinet.traffic.source.country\":\"India\",\"fortinet.traffic.source.city\":\"Salem\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"11.6602\",\"source.longitude\":\"78.1532\"},{\"fortinet.traffic.source.country\":\"United States\",\"fortinet.traffic.source.city\":\"North Charleston\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"32.8608\",\"source.longitude\":\"-79.9746\"},{\"fortinet.traffic.source.country\":\"India\",\"fortinet.traffic.source.city\":\"Faridabad\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"28.4098\",\"source.longitude\":\"77.31\"},{\"fortinet.traffic.source.country\":\"United States\",\"fortinet.traffic.source.city\":\"North Bergen\",\"fortinet.traffic.bytes^avg\":62,\"source.latitude\":\"40.793\",\"source.longitude\":\"-74.0247\"},{\"fortinet.traffic.source.country\":\"China\",\"fortinet.traffic.source.city\":\"Jiaozuo\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"35.2417\",\"source.longitude\":\"113.2339\"},{\"fortinet.traffic.source.country\":\"Singapore\",\"fortinet.traffic.source.city\":\"unknown\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"-2.0001\",\"source.longitude\":\"30.0\"},{\"fortinet.traffic.source.country\":\"United States\",\"fortinet.traffic.source.city\":\"North Kansas City\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"39.1484\",\"source.longitude\":\"-94.5686\"},{\"fortinet.traffic.source.country\":\"Netherlands\",\"fortinet.traffic.source.city\":\"unknown\",\"fortinet.traffic.bytes^avg\":2958,\"source.latitude\":\"-2.0001\",\"source.longitude\":\"30.0\"},{\"fortinet.traffic.source.country\":\"Russian Federation\",\"fortinet.traffic.source.city\":\"Paris\",\"fortinet.traffic.bytes^avg\":16966,\"source.latitude\":\"7.15\",\"source.longitude\":\"1.0833\"},{\"fortinet.traffic.source.country\":\"India\",\"fortinet.traffic.source.city\":\"Mumbai\",\"fortinet.traffic.bytes^avg\":0,\"source.latitude\":\"19.0748\",\"source.longitude\":\"72.8856\"}]}"))
            .put("testLogGroupMapWithDestinationCountry", new JsonObject("{\"result\":[{\"fortinet.traffic.destination.country\":\"Vietnam\",\"fortinet.traffic.bytes^avg\":93340,\"destination.latitude\":\"14.058324\",\"destination.longitude\":\"108.277199\"},{\"fortinet.traffic.destination.country\":\"Ireland\",\"fortinet.traffic.bytes^avg\":46090,\"destination.latitude\":\"53.41291\",\"destination.longitude\":\"-8.24389\"},{\"fortinet.traffic.destination.country\":\"Bahrain\",\"fortinet.traffic.bytes^avg\":9262,\"destination.latitude\":\"25.930414\",\"destination.longitude\":\"50.637772\"},{\"fortinet.traffic.destination.country\":\"Hong Kong\",\"fortinet.traffic.bytes^avg\":4048777,\"destination.latitude\":\"22.396428\",\"destination.longitude\":\"114.109497\"},{\"fortinet.traffic.destination.country\":\"Belgium\",\"fortinet.traffic.bytes^avg\":11006,\"destination.latitude\":\"50.503887\",\"destination.longitude\":\"4.469936\"},{\"fortinet.traffic.destination.country\":\"Australia\",\"fortinet.traffic.bytes^avg\":609163,\"destination.latitude\":\"-25.274398\",\"destination.longitude\":\"133.775136\"},{\"fortinet.traffic.destination.country\":\"Finland\",\"fortinet.traffic.bytes^avg\":1024,\"destination.latitude\":\"61.92411\",\"destination.longitude\":\"25.748151\"},{\"fortinet.traffic.destination.country\":\"Japan\",\"fortinet.traffic.bytes^avg\":1488959,\"destination.latitude\":\"36.204824\",\"destination.longitude\":\"138.252924\"},{\"fortinet.traffic.destination.country\":\"Indonesia\",\"fortinet.traffic.bytes^avg\":16223,\"destination.latitude\":\"-0.789275\",\"destination.longitude\":\"113.921327\"},{\"fortinet.traffic.destination.country\":\"United Kingdom\",\"fortinet.traffic.bytes^avg\":11175,\"destination.latitude\":\"55.378051\",\"destination.longitude\":\"-3.435973\"},{\"fortinet.traffic.destination.country\":\"Netherlands\",\"fortinet.traffic.bytes^avg\":260338,\"destination.latitude\":\"52.132633\",\"destination.longitude\":\"5.291266\"},{\"fortinet.traffic.destination.country\":\"Canada\",\"fortinet.traffic.bytes^avg\":43390,\"destination.latitude\":\"56.130366\",\"destination.longitude\":\"-106.346771\"},{\"fortinet.traffic.destination.country\":\"Bulgaria\",\"fortinet.traffic.bytes^avg\":10801,\"destination.latitude\":\"42.733883\",\"destination.longitude\":\"25.48583\"},{\"fortinet.traffic.destination.country\":\"Israel\",\"fortinet.traffic.bytes^avg\":5306,\"destination.latitude\":\"31.046051\",\"destination.longitude\":\"34.851612\"},{\"fortinet.traffic.destination.country\":\"Italy\",\"fortinet.traffic.bytes^avg\":7691,\"destination.latitude\":\"41.87194\",\"destination.longitude\":\"12.56738\"},{\"fortinet.traffic.destination.country\":\"Thailand\",\"fortinet.traffic.bytes^avg\":76,\"destination.latitude\":\"15.870032\",\"destination.longitude\":\"100.992541\"},{\"fortinet.traffic.destination.country\":\"New Zealand\",\"fortinet.traffic.bytes^avg\":76481,\"destination.latitude\":\"-40.900557\",\"destination.longitude\":\"174.885971\"},{\"fortinet.traffic.destination.country\":\"Switzerland\",\"fortinet.traffic.bytes^avg\":9208,\"destination.latitude\":\"46.818188\",\"destination.longitude\":\"8.227512\"},{\"fortinet.traffic.destination.country\":\"South Africa\",\"fortinet.traffic.bytes^avg\":124245,\"destination.latitude\":\"-30.559482\",\"destination.longitude\":\"22.937506\"},{\"fortinet.traffic.destination.country\":\"Germany\",\"fortinet.traffic.bytes^avg\":4098488,\"destination.latitude\":\"51.165691\",\"destination.longitude\":\"10.451526\"},{\"fortinet.traffic.destination.country\":\"Norway\",\"fortinet.traffic.bytes^avg\":8953,\"destination.latitude\":\"60.472024\",\"destination.longitude\":\"8.468946\"},{\"fortinet.traffic.destination.country\":\"Malaysia\",\"fortinet.traffic.bytes^avg\":8709,\"destination.latitude\":\"4.210484\",\"destination.longitude\":\"101.975766\"},{\"fortinet.traffic.destination.country\":\"Sweden\",\"fortinet.traffic.bytes^avg\":29497,\"destination.latitude\":\"60.128161\",\"destination.longitude\":\"18.643501\"},{\"fortinet.traffic.destination.country\":\"Singapore\",\"fortinet.traffic.bytes^avg\":1007004,\"destination.latitude\":\"1.352083\",\"destination.longitude\":\"103.819836\"},{\"fortinet.traffic.destination.country\":\"Slovenia\",\"fortinet.traffic.bytes^avg\":15448,\"destination.latitude\":\"46.151241\",\"destination.longitude\":\"14.995463\"},{\"fortinet.traffic.destination.country\":\"United States\",\"fortinet.traffic.bytes^avg\":141502,\"destination.latitude\":\"37.09024\",\"destination.longitude\":\"-95.712891\"},{\"fortinet.traffic.destination.country\":\"France\",\"fortinet.traffic.bytes^avg\":38665,\"destination.latitude\":\"46.227638\",\"destination.longitude\":\"2.213749\"},{\"fortinet.traffic.destination.country\":\"China\",\"fortinet.traffic.bytes^avg\":2334,\"destination.latitude\":\"35.86166\",\"destination.longitude\":\"104.195397\"},{\"fortinet.traffic.destination.country\":\"United Arab Emirates\",\"fortinet.traffic.bytes^avg\":7529,\"destination.latitude\":\"23.424076\",\"destination.longitude\":\"53.847818\"},{\"fortinet.traffic.destination.country\":\"India\",\"fortinet.traffic.bytes^avg\":782761,\"destination.latitude\":\"20.593684\",\"destination.longitude\":\"78.96288\"},{\"fortinet.traffic.destination.country\":\"Taiwan\",\"fortinet.traffic.bytes^avg\":2554,\"destination.latitude\":\"23.69781\",\"destination.longitude\":\"120.960515\"},{\"fortinet.traffic.destination.country\":\"Lithuania\",\"fortinet.traffic.bytes^avg\":5808,\"destination.latitude\":\"55.169438\",\"destination.longitude\":\"23.881275\"},{\"fortinet.traffic.destination.country\":\"Spain\",\"fortinet.traffic.bytes^avg\":9240,\"destination.latitude\":\"40.463667\",\"destination.longitude\":\"-3.74922\"},{\"fortinet.traffic.destination.country\":\"Maldives\",\"fortinet.traffic.bytes^avg\":152,\"destination.latitude\":\"3.202778\",\"destination.longitude\":\"73.22068\"},{\"fortinet.traffic.destination.country\":\"Brazil\",\"fortinet.traffic.bytes^avg\":9503,\"destination.latitude\":\"-14.235004\",\"destination.longitude\":\"-51.92528\"}]}"))
            .put("testLogGroupMapWithDestinationCity", new JsonObject("{\"result\":[{\"fortinet.traffic.destination.city\":\"Thrissur\",\"fortinet.traffic.bytes^avg\":217,\"destination.latitude\":\"10.516\",\"destination.longitude\":\"76.2157\"},{\"fortinet.traffic.destination.city\":\"Pak Kret\",\"fortinet.traffic.bytes^avg\":76,\"destination.latitude\":\"13.9181\",\"destination.longitude\":\"100.4974\"},{\"fortinet.traffic.destination.city\":\"Canoga Park\",\"fortinet.traffic.bytes^avg\":9296,\"destination.latitude\":\"34.2166\",\"destination.longitude\":\"-118.6028\"},{\"fortinet.traffic.destination.city\":\"Dyer\",\"fortinet.traffic.bytes^avg\":12278,\"destination.latitude\":\"35.4945\",\"destination.longitude\":\"-94.1315\"},{\"fortinet.traffic.destination.city\":\"Nashville\",\"fortinet.traffic.bytes^avg\":27747,\"destination.latitude\":\"33.9581\",\"destination.longitude\":\"-93.875\"},{\"fortinet.traffic.destination.city\":\"San Jacinto\",\"fortinet.traffic.bytes^avg\":9499,\"destination.latitude\":\"16.0724\",\"destination.longitude\":\"120.4445\"},{\"fortinet.traffic.destination.city\":\"Boydton\",\"fortinet.traffic.bytes^avg\":209010,\"destination.latitude\":\"36.6534\",\"destination.longitude\":\"-78.375\"},{\"fortinet.traffic.destination.city\":\"Jaipur\",\"fortinet.traffic.bytes^avg\":747,\"destination.latitude\":\"26.9525\",\"destination.longitude\":\"75.7105\"},{\"fortinet.traffic.destination.city\":\"Mysore\",\"fortinet.traffic.bytes^avg\":370,\"destination.latitude\":\"12.2989\",\"destination.longitude\":\"76.6394\"},{\"fortinet.traffic.destination.city\":\"Lithia Springs\",\"fortinet.traffic.bytes^avg\":5252,\"destination.latitude\":\"33.7655\",\"destination.longitude\":\"-84.6469\"},{\"fortinet.traffic.destination.city\":\"Helsinki\",\"fortinet.traffic.bytes^avg\":980,\"destination.latitude\":\"60.1797\",\"destination.longitude\":\"24.9344\"},{\"fortinet.traffic.destination.city\":\"San Antonio\",\"fortinet.traffic.bytes^avg\":257497,\"destination.latitude\":\"14.9591\",\"destination.longitude\":\"120.0913\"},{\"fortinet.traffic.destination.city\":\"Osaka\",\"fortinet.traffic.bytes^avg\":281700,\"destination.latitude\":\"34.6933\",\"destination.longitude\":\"135.5067\"},{\"fortinet.traffic.destination.city\":\"Zaragoza\",\"fortinet.traffic.bytes^avg\":9231,\"destination.latitude\":\"15.4522\",\"destination.longitude\":\"120.7966\"},{\"fortinet.traffic.destination.city\":\"Pathanamthitta\",\"fortinet.traffic.bytes^avg\":490,\"destination.latitude\":\"9.2612\",\"destination.longitude\":\"76.7833\"},{\"fortinet.traffic.destination.city\":\"Lehi\",\"fortinet.traffic.bytes^avg\":3894,\"destination.latitude\":\"40.3942\",\"destination.longitude\":\"-111.8483\"},{\"fortinet.traffic.destination.city\":\"Rajkot\",\"fortinet.traffic.bytes^avg\":1929,\"destination.latitude\":\"22.2904\",\"destination.longitude\":\"70.7915\"},{\"fortinet.traffic.destination.city\":\"Canberra\",\"fortinet.traffic.bytes^avg\":8057,\"destination.latitude\":\"-35.3122\",\"destination.longitude\":\"149.1511\"},{\"fortinet.traffic.destination.city\":\"London\",\"fortinet.traffic.bytes^avg\":726024,\"destination.latitude\":\"6.0097\",\"destination.longitude\":\"125.1294\"},{\"fortinet.traffic.destination.city\":\"Secaucus\",\"fortinet.traffic.bytes^avg\":1130357,\"destination.latitude\":\"40.7876\",\"destination.longitude\":\"-74.06\"},{\"fortinet.traffic.destination.city\":\"Berlin\",\"fortinet.traffic.bytes^avg\":6473,\"destination.latitude\":\"54.0375\",\"destination.longitude\":\"10.4557\"},{\"fortinet.traffic.destination.city\":\"Des Moines\",\"fortinet.traffic.bytes^avg\":37399,\"destination.latitude\":\"41.6381\",\"destination.longitude\":\"-93.6203\"},{\"fortinet.traffic.destination.city\":\"Québec\",\"fortinet.traffic.bytes^avg\":8192,\"destination.latitude\":\"46.7949\",\"destination.longitude\":\"-71.247\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.bytes^avg\":1866588,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\"},{\"fortinet.traffic.destination.city\":\"Ajmer\",\"fortinet.traffic.bytes^avg\":490,\"destination.latitude\":\"26.4529\",\"destination.longitude\":\"74.6432\"},{\"fortinet.traffic.destination.city\":\"Oslo\",\"fortinet.traffic.bytes^avg\":8981,\"destination.latitude\":\"59.955\",\"destination.longitude\":\"10.859\"},{\"fortinet.traffic.destination.city\":\"Tiruchirappalli\",\"fortinet.traffic.bytes^avg\":490,\"destination.latitude\":\"10.81\",\"destination.longitude\":\"78.6932\"},{\"fortinet.traffic.destination.city\":\"Dombivali\",\"fortinet.traffic.bytes^avg\":430575,\"destination.latitude\":\"19.216\",\"destination.longitude\":\"73.1034\"},{\"fortinet.traffic.destination.city\":\"Arezzo\",\"fortinet.traffic.bytes^avg\":4158,\"destination.latitude\":\"43.4631\",\"destination.longitude\":\"11.8783\"},{\"fortinet.traffic.destination.city\":\"Prineville\",\"fortinet.traffic.bytes^avg\":12550,\"destination.latitude\":\"44.3041\",\"destination.longitude\":\"-120.8364\"},{\"fortinet.traffic.destination.city\":\"Brussels\",\"fortinet.traffic.bytes^avg\":9322,\"destination.latitude\":\"50.8534\",\"destination.longitude\":\"4.347\"},{\"fortinet.traffic.destination.city\":\"El Cajon\",\"fortinet.traffic.bytes^avg\":87926,\"destination.latitude\":\"32.7916\",\"destination.longitude\":\"-116.971\"},{\"fortinet.traffic.destination.city\":\"Noida\",\"fortinet.traffic.bytes^avg\":720,\"destination.latitude\":\"28.6145\",\"destination.longitude\":\"77.3063\"},{\"fortinet.traffic.destination.city\":\"Seattle\",\"fortinet.traffic.bytes^avg\":30358,\"destination.latitude\":\"47.4902\",\"destination.longitude\":\"-122.3004\"},{\"fortinet.traffic.destination.city\":\"Houston\",\"fortinet.traffic.bytes^avg\":6484,\"destination.latitude\":\"55.8691\",\"destination.longitude\":\"-4.511\"},{\"fortinet.traffic.destination.city\":\"Shillong\",\"fortinet.traffic.bytes^avg\":1963,\"destination.latitude\":\"25.5679\",\"destination.longitude\":\"91.8787\"},{\"fortinet.traffic.destination.city\":\"Ludhiana\",\"fortinet.traffic.bytes^avg\":330,\"destination.latitude\":\"31.0048\",\"destination.longitude\":\"75.9463\"},{\"fortinet.traffic.destination.city\":\"Dallas\",\"fortinet.traffic.bytes^avg\":4506,\"destination.latitude\":\"57.6101\",\"destination.longitude\":\"-3.6176\"},{\"fortinet.traffic.destination.city\":\"Male\",\"fortinet.traffic.bytes^avg\":152,\"destination.latitude\":\"4.1667\",\"destination.longitude\":\"73.5\"},{\"fortinet.traffic.destination.city\":\"Wuhan\",\"fortinet.traffic.bytes^avg\":611809,\"destination.latitude\":\"30.589\",\"destination.longitude\":\"114.2681\"},{\"fortinet.traffic.destination.city\":\"Columbus\",\"fortinet.traffic.bytes^avg\":24357,\"destination.latitude\":\"32.4783\",\"destination.longitude\":\"-84.8985\"},{\"fortinet.traffic.destination.city\":\"Santa Clara\",\"fortinet.traffic.bytes^avg\":425944,\"destination.latitude\":\"15.7063\",\"destination.longitude\":\"120.7158\"},{\"fortinet.traffic.destination.city\":\"Overland Park\",\"fortinet.traffic.bytes^avg\":12190,\"destination.latitude\":\"38.8615\",\"destination.longitude\":\"-94.6277\"},{\"fortinet.traffic.destination.city\":\"Longmont\",\"fortinet.traffic.bytes^avg\":228,\"destination.latitude\":\"40.1452\",\"destination.longitude\":\"-105.1667\"},{\"fortinet.traffic.destination.city\":\"Samalkha\",\"fortinet.traffic.bytes^avg\":185,\"destination.latitude\":\"29.2384\",\"destination.longitude\":\"77.0084\"},{\"fortinet.traffic.destination.city\":\"St Louis\",\"fortinet.traffic.bytes^avg\":7900,\"destination.latitude\":\"38.6287\",\"destination.longitude\":\"-90.1988\"},{\"fortinet.traffic.destination.city\":\"San Diego\",\"fortinet.traffic.bytes^avg\":189887,\"destination.latitude\":\"10.2648\",\"destination.longitude\":\"-67.9517\"},{\"fortinet.traffic.destination.city\":\"Frankfurt am Main\",\"fortinet.traffic.bytes^avg\":2607448,\"destination.latitude\":\"50.1188\",\"destination.longitude\":\"8.6843\"},{\"fortinet.traffic.destination.city\":\"Hong Kong\",\"fortinet.traffic.bytes^avg\":5181774,\"destination.latitude\":\"22.2842\",\"destination.longitude\":\"114.1759\"},{\"fortinet.traffic.destination.city\":\"Haneda\",\"fortinet.traffic.bytes^avg\":5501441,\"destination.latitude\":\"32.2323\",\"destination.longitude\":\"130.7251\"},{\"fortinet.traffic.destination.city\":\"Chennai\",\"fortinet.traffic.bytes^avg\":189905,\"destination.latitude\":\"12.8996\",\"destination.longitude\":\"80.2209\"},{\"fortinet.traffic.destination.city\":\"Tokyo\",\"fortinet.traffic.bytes^avg\":2929964,\"destination.latitude\":\"35.6837\",\"destination.longitude\":\"139.6805\"},{\"fortinet.traffic.destination.city\":\"Central\",\"fortinet.traffic.bytes^avg\":152,\"destination.latitude\":\"-11.1542\",\"destination.longitude\":\"-42.0815\"},{\"fortinet.traffic.destination.city\":\"Nuremberg\",\"fortinet.traffic.bytes^avg\":111053,\"destination.latitude\":\"49.415\",\"destination.longitude\":\"11.034\"},{\"fortinet.traffic.destination.city\":\"Clifton\",\"fortinet.traffic.bytes^avg\":8019,\"destination.latitude\":\"-34.2633\",\"destination.longitude\":\"150.9716\"},{\"fortinet.traffic.destination.city\":\"Jamnagar\",\"fortinet.traffic.bytes^avg\":245780,\"destination.latitude\":\"22.4667\",\"destination.longitude\":\"70.0644\"},{\"fortinet.traffic.destination.city\":\"Chicago\",\"fortinet.traffic.bytes^avg\":49808,\"destination.latitude\":\"41.9025\",\"destination.longitude\":\"-87.6726\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.bytes^avg\":104094,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\"},{\"fortinet.traffic.destination.city\":\"Raigarh\",\"fortinet.traffic.bytes^avg\":21488417,\"destination.latitude\":\"21.896\",\"destination.longitude\":\"83.4001\"},{\"fortinet.traffic.destination.city\":\"Ashburn\",\"fortinet.traffic.bytes^avg\":576858,\"destination.latitude\":\"31.7097\",\"destination.longitude\":\"-83.6625\"},{\"fortinet.traffic.destination.city\":\"Lucknow\",\"fortinet.traffic.bytes^avg\":22372,\"destination.latitude\":\"26.8756\",\"destination.longitude\":\"80.9115\"},{\"fortinet.traffic.destination.city\":\"Pune\",\"fortinet.traffic.bytes^avg\":458685,\"destination.latitude\":\"18.6161\",\"destination.longitude\":\"73.7286\"},{\"fortinet.traffic.destination.city\":\"Gurugram\",\"fortinet.traffic.bytes^avg\":544,\"destination.latitude\":\"28.4597\",\"destination.longitude\":\"77.0282\"},{\"fortinet.traffic.destination.city\":\"North Providence\",\"fortinet.traffic.bytes^avg\":78716,\"destination.latitude\":\"41.8547\",\"destination.longitude\":\"-71.4736\"},{\"fortinet.traffic.destination.city\":\"Redmond\",\"fortinet.traffic.bytes^avg\":3265552,\"destination.latitude\":\"44.2762\",\"destination.longitude\":\"-121.1847\"},{\"fortinet.traffic.destination.city\":\"Mesquite\",\"fortinet.traffic.bytes^avg\":5512,\"destination.latitude\":\"32.8179\",\"destination.longitude\":\"-96.6319\"},{\"fortinet.traffic.destination.city\":\"Kolkata\",\"fortinet.traffic.bytes^avg\":1724,\"destination.latitude\":\"22.518\",\"destination.longitude\":\"88.3832\"},{\"fortinet.traffic.destination.city\":\"El Paso\",\"fortinet.traffic.bytes^avg\":609050,\"destination.latitude\":\"28.6475\",\"destination.longitude\":\"-17.884\"},{\"fortinet.traffic.destination.city\":\"Kansas City\",\"fortinet.traffic.bytes^avg\":79000,\"destination.latitude\":\"39.0828\",\"destination.longitude\":\"-94.777\"},{\"fortinet.traffic.destination.city\":\"Melbourne\",\"fortinet.traffic.bytes^avg\":24838,\"destination.latitude\":\"-37.8159\",\"destination.longitude\":\"144.9669\"},{\"fortinet.traffic.destination.city\":\"Rostock\",\"fortinet.traffic.bytes^avg\":182895,\"destination.latitude\":\"54.1085\",\"destination.longitude\":\"12.0645\"},{\"fortinet.traffic.destination.city\":\"Bhilwara\",\"fortinet.traffic.bytes^avg\":75713,\"destination.latitude\":\"25.3399\",\"destination.longitude\":\"74.645\"},{\"fortinet.traffic.destination.city\":\"Salt Lake City\",\"fortinet.traffic.bytes^avg\":8420,\"destination.latitude\":\"40.7742\",\"destination.longitude\":\"-111.8721\"},{\"fortinet.traffic.destination.city\":\"Homestead\",\"fortinet.traffic.bytes^avg\":6713,\"destination.latitude\":\"25.4931\",\"destination.longitude\":\"-80.4344\"},{\"fortinet.traffic.destination.city\":\"American Fork\",\"fortinet.traffic.bytes^avg\":868,\"destination.latitude\":\"40.3969\",\"destination.longitude\":\"-111.7919\"},{\"fortinet.traffic.destination.city\":\"San Jose\",\"fortinet.traffic.bytes^avg\":91004,\"destination.latitude\":\"15.7494\",\"destination.longitude\":\"121.0832\"},{\"fortinet.traffic.destination.city\":\"Bhubaneswar\",\"fortinet.traffic.bytes^avg\":10939,\"destination.latitude\":\"20.2706\",\"destination.longitude\":\"85.8334\"},{\"fortinet.traffic.destination.city\":\"New Delhi\",\"fortinet.traffic.bytes^avg\":22019,\"destination.latitude\":\"28.6328\",\"destination.longitude\":\"77.2204\"},{\"fortinet.traffic.destination.city\":\"Amsterdam\",\"fortinet.traffic.bytes^avg\":303422,\"destination.latitude\":\"-31.6252\",\"destination.longitude\":\"25.6244\"},{\"fortinet.traffic.destination.city\":\"Slough\",\"fortinet.traffic.bytes^avg\":11573,\"destination.latitude\":\"51.5368\",\"destination.longitude\":\"-0.6718\"},{\"fortinet.traffic.destination.city\":\"Bengaluru\",\"fortinet.traffic.bytes^avg\":1591464,\"destination.latitude\":\"12.9634\",\"destination.longitude\":\"77.5855\"},{\"fortinet.traffic.destination.city\":\"Los Angeles\",\"fortinet.traffic.bytes^avg\":3139,\"destination.latitude\":\"13.3724\",\"destination.longitude\":\"-87.0314\"},{\"fortinet.traffic.destination.city\":\"Stockholm\",\"fortinet.traffic.bytes^avg\":9542,\"destination.latitude\":\"59.3274\",\"destination.longitude\":\"18.0653\"},{\"fortinet.traffic.destination.city\":\"unknown\",\"fortinet.traffic.bytes^avg\":365503,\"destination.latitude\":\"-2.0001\",\"destination.longitude\":\"30.0\"},{\"fortinet.traffic.destination.city\":\"Maharashtra\",\"fortinet.traffic.bytes^avg\":7527,\"destination.latitude\":\"18.9721\",\"destination.longitude\":\"72.8246\"},{\"fortinet.traffic.destination.city\":\"San Francisco\",\"fortinet.traffic.bytes^avg\":28106,\"destination.latitude\":\"15.9667\",\"destination.longitude\":\"120.6833\"},{\"fortinet.traffic.destination.city\":\"Busan\",\"fortinet.traffic.bytes^avg\":10541,\"destination.latitude\":\"35.1001\",\"destination.longitude\":\"129.0433\"},{\"fortinet.traffic.destination.city\":\"Jamestown\",\"fortinet.traffic.bytes^avg\":412,\"destination.latitude\":\"-31.1167\",\"destination.longitude\":\"26.8\"},{\"fortinet.traffic.destination.city\":\"Mesa\",\"fortinet.traffic.bytes^avg\":8347,\"destination.latitude\":\"33.3981\",\"destination.longitude\":\"-111.785\"},{\"fortinet.traffic.destination.city\":\"Bandung\",\"fortinet.traffic.bytes^avg\":1923,\"destination.latitude\":\"-6.9217\",\"destination.longitude\":\"107.6071\"},{\"fortinet.traffic.destination.city\":\"Dublin\",\"fortinet.traffic.bytes^avg\":34183,\"destination.latitude\":\"-34.4558\",\"destination.longitude\":\"138.3547\"},{\"fortinet.traffic.destination.city\":\"Walnut\",\"fortinet.traffic.bytes^avg\":9985,\"destination.latitude\":\"37.5972\",\"destination.longitude\":\"-95.0445\"},{\"fortinet.traffic.destination.city\":\"Zurich\",\"fortinet.traffic.bytes^avg\":9182,\"destination.latitude\":\"47.3682\",\"destination.longitude\":\"8.5671\"},{\"fortinet.traffic.destination.city\":\"Haldwani\",\"fortinet.traffic.bytes^avg\":200,\"destination.latitude\":\"29.2248\",\"destination.longitude\":\"79.5313\"},{\"fortinet.traffic.destination.city\":\"Newark\",\"fortinet.traffic.bytes^avg\":99839,\"destination.latitude\":\"35.7171\",\"destination.longitude\":\"-91.4356\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.bytes^avg\":1501930,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\"},{\"fortinet.traffic.destination.city\":\"Boardman\",\"fortinet.traffic.bytes^avg\":1691044,\"destination.latitude\":\"41.0158\",\"destination.longitude\":\"-80.8041\"},{\"fortinet.traffic.destination.city\":\"Paris\",\"fortinet.traffic.bytes^avg\":24185,\"destination.latitude\":\"7.15\",\"destination.longitude\":\"1.0833\"},{\"fortinet.traffic.destination.city\":\"Delhi\",\"fortinet.traffic.bytes^avg\":7633622,\"destination.latitude\":\"28.6542\",\"destination.longitude\":\"77.2373\"},{\"fortinet.traffic.destination.city\":\"Fremont\",\"fortinet.traffic.bytes^avg\":99339,\"destination.latitude\":\"41.2075\",\"destination.longitude\":\"-92.4359\"},{\"fortinet.traffic.destination.city\":\"Groningen\",\"fortinet.traffic.bytes^avg\":628836,\"destination.latitude\":\"53.2063\",\"destination.longitude\":\"6.569\"},{\"fortinet.traffic.destination.city\":\"The Dalles\",\"fortinet.traffic.bytes^avg\":5952482,\"destination.latitude\":\"45.5999\",\"destination.longitude\":\"-121.1871\"},{\"fortinet.traffic.destination.city\":\"Sydney\",\"fortinet.traffic.bytes^avg\":148542,\"destination.latitude\":\"-33.8715\",\"destination.longitude\":\"151.2006\"},{\"fortinet.traffic.destination.city\":\"Indianapolis\",\"fortinet.traffic.bytes^avg\":407,\"destination.latitude\":\"39.8079\",\"destination.longitude\":\"-86.0949\"},{\"fortinet.traffic.destination.city\":\"Sunnyvale\",\"fortinet.traffic.bytes^avg\":20998,\"destination.latitude\":\"32.7932\",\"destination.longitude\":\"-96.5629\"},{\"fortinet.traffic.destination.city\":\"Kadapa\",\"fortinet.traffic.bytes^avg\":720,\"destination.latitude\":\"14.4804\",\"destination.longitude\":\"78.8317\"},{\"fortinet.traffic.destination.city\":\"São Paulo\",\"fortinet.traffic.bytes^avg\":9547,\"destination.latitude\":\"-23.5335\",\"destination.longitude\":\"-46.6359\"},{\"fortinet.traffic.destination.city\":\"Boston\",\"fortinet.traffic.bytes^avg\":1010668,\"destination.latitude\":\"52.9791\",\"destination.longitude\":\"-0.0269\"},{\"fortinet.traffic.destination.city\":\"Mountain View\",\"fortinet.traffic.bytes^avg\":146803,\"destination.latitude\":\"35.824\",\"destination.longitude\":\"-92.0391\"},{\"fortinet.traffic.destination.city\":\"Saluda\",\"fortinet.traffic.bytes^avg\":238,\"destination.latitude\":\"35.2348\",\"destination.longitude\":\"-82.3351\"},{\"fortinet.traffic.destination.city\":\"Cincinnati\",\"fortinet.traffic.bytes^avg\":62107,\"destination.latitude\":\"39.2413\",\"destination.longitude\":\"-84.5455\"},{\"fortinet.traffic.destination.city\":\"Rohtak\",\"fortinet.traffic.bytes^avg\":490,\"destination.latitude\":\"28.8964\",\"destination.longitude\":\"76.5909\"},{\"fortinet.traffic.destination.city\":\"Montreal\",\"fortinet.traffic.bytes^avg\":9589,\"destination.latitude\":\"43.951\",\"destination.longitude\":\"0.1951\"},{\"fortinet.traffic.destination.city\":\"Coimbatore\",\"fortinet.traffic.bytes^avg\":385579,\"destination.latitude\":\"11.0142\",\"destination.longitude\":\"76.9941\"},{\"fortinet.traffic.destination.city\":\"Tappahannock\",\"fortinet.traffic.bytes^avg\":111438,\"destination.latitude\":\"37.9273\",\"destination.longitude\":\"-76.8545\"},{\"fortinet.traffic.destination.city\":\"Maiden\",\"fortinet.traffic.bytes^avg\":11346,\"destination.latitude\":\"35.5759\",\"destination.longitude\":\"-81.1745\"},{\"fortinet.traffic.destination.city\":\"Patna\",\"fortinet.traffic.bytes^avg\":413,\"destination.latitude\":\"25.5908\",\"destination.longitude\":\"85.1348\"},{\"fortinet.traffic.destination.city\":\"Hoover\",\"fortinet.traffic.bytes^avg\":95696,\"destination.latitude\":\"33.3555\",\"destination.longitude\":\"-86.8274\"},{\"fortinet.traffic.destination.city\":\"Reno\",\"fortinet.traffic.bytes^avg\":37009,\"destination.latitude\":\"32.9658\",\"destination.longitude\":\"-97.6866\"},{\"fortinet.traffic.destination.city\":\"Sacramento\",\"fortinet.traffic.bytes^avg\":212,\"destination.latitude\":\"-19.8804\",\"destination.longitude\":\"-47.226\"},{\"fortinet.traffic.destination.city\":\"Kuala Lumpur\",\"fortinet.traffic.bytes^avg\":8709,\"destination.latitude\":\"3.1413\",\"destination.longitude\":\"101.685\"},{\"fortinet.traffic.destination.city\":\"Menifee\",\"fortinet.traffic.bytes^avg\":3410,\"destination.latitude\":\"35.1327\",\"destination.longitude\":\"-92.5494\"},{\"fortinet.traffic.destination.city\":\"Fort Lauderdale\",\"fortinet.traffic.bytes^avg\":505,\"destination.latitude\":\"26.1864\",\"destination.longitude\":\"-80.1285\"},{\"fortinet.traffic.destination.city\":\"Seoul\",\"fortinet.traffic.bytes^avg\":441467,\"destination.latitude\":\"37.5576\",\"destination.longitude\":\"126.9937\"},{\"fortinet.traffic.destination.city\":\"Clearfield\",\"fortinet.traffic.bytes^avg\":3944,\"destination.latitude\":\"38.1295\",\"destination.longitude\":\"-83.4466\"},{\"fortinet.traffic.destination.city\":\"Jakarta\",\"fortinet.traffic.bytes^avg\":15691,\"destination.latitude\":\"-6.2114\",\"destination.longitude\":\"106.8446\"},{\"fortinet.traffic.destination.city\":\"Milan\",\"fortinet.traffic.bytes^avg\":9243,\"destination.latitude\":\"45.4722\",\"destination.longitude\":\"9.1922\"},{\"fortinet.traffic.destination.city\":\"Mount Juliet\",\"fortinet.traffic.bytes^avg\":5726,\"destination.latitude\":\"36.1868\",\"destination.longitude\":\"-86.5067\"},{\"fortinet.traffic.destination.city\":\"Cape Town\",\"fortinet.traffic.bytes^avg\":3277,\"destination.latitude\":\"-34.0486\",\"destination.longitude\":\"18.4811\"},{\"fortinet.traffic.destination.city\":\"Knoxville\",\"fortinet.traffic.bytes^avg\":7236,\"destination.latitude\":\"35.3707\",\"destination.longitude\":\"-93.3697\"},{\"fortinet.traffic.destination.city\":\"Dhanbad\",\"fortinet.traffic.bytes^avg\":260,\"destination.latitude\":\"23.8006\",\"destination.longitude\":\"86.4283\"},{\"fortinet.traffic.destination.city\":\"Nagpur\",\"fortinet.traffic.bytes^avg\":50895,\"destination.latitude\":\"21.1161\",\"destination.longitude\":\"79.0706\"},{\"fortinet.traffic.destination.city\":\"Council Bluffs\",\"fortinet.traffic.bytes^avg\":3645,\"destination.latitude\":\"41.2615\",\"destination.longitude\":\"-95.8304\"},{\"fortinet.traffic.destination.city\":\"Boca Raton\",\"fortinet.traffic.bytes^avg\":469,\"destination.latitude\":\"26.3796\",\"destination.longitude\":\"-80.1029\"},{\"fortinet.traffic.destination.city\":\"Thane\",\"fortinet.traffic.bytes^avg\":20559108,\"destination.latitude\":\"19.1963\",\"destination.longitude\":\"72.9675\"},{\"fortinet.traffic.destination.city\":\"Bhopal\",\"fortinet.traffic.bytes^avg\":275975,\"destination.latitude\":\"23.2487\",\"destination.longitude\":\"77.4066\"},{\"fortinet.traffic.destination.city\":\"Hanoi\",\"fortinet.traffic.bytes^avg\":97547,\"destination.latitude\":\"21.0292\",\"destination.longitude\":\"105.8526\"},{\"fortinet.traffic.destination.city\":\"Portland\",\"fortinet.traffic.bytes^avg\":242615,\"destination.latitude\":\"-38.354\",\"destination.longitude\":\"141.5992\"},{\"fortinet.traffic.destination.city\":\"Dubai\",\"fortinet.traffic.bytes^avg\":9245,\"destination.latitude\":\"25.0731\",\"destination.longitude\":\"55.298\"},{\"fortinet.traffic.destination.city\":\"Madrid\",\"fortinet.traffic.bytes^avg\":380,\"destination.latitude\":\"40.4163\",\"destination.longitude\":\"-3.6934\"},{\"fortinet.traffic.destination.city\":\"Ambala\",\"fortinet.traffic.bytes^avg\":7161,\"destination.latitude\":\"30.3557\",\"destination.longitude\":\"76.8019\"},{\"fortinet.traffic.destination.city\":\"Washington\",\"fortinet.traffic.bytes^avg\":53531,\"destination.latitude\":\"54.9\",\"destination.longitude\":\"-1.5167\"},{\"fortinet.traffic.destination.city\":\"Marseille\",\"fortinet.traffic.bytes^avg\":21917,\"destination.latitude\":\"43.2951\",\"destination.longitude\":\"5.3861\"},{\"fortinet.traffic.destination.city\":\"Cheyenne\",\"fortinet.traffic.bytes^avg\":24490,\"destination.latitude\":\"35.608\",\"destination.longitude\":\"-99.6787\"},{\"fortinet.traffic.destination.city\":\"Indore\",\"fortinet.traffic.bytes^avg\":260,\"destination.latitude\":\"22.717\",\"destination.longitude\":\"75.8337\"},{\"fortinet.traffic.destination.city\":\"Chandigarh\",\"fortinet.traffic.bytes^avg\":260,\"destination.latitude\":\"30.7339\",\"destination.longitude\":\"76.7889\"},{\"fortinet.traffic.destination.city\":\"Karur\",\"fortinet.traffic.bytes^avg\":260,\"destination.latitude\":\"10.9536\",\"destination.longitude\":\"78.0812\"},{\"fortinet.traffic.destination.city\":\"South Jordan\",\"fortinet.traffic.bytes^avg\":937,\"destination.latitude\":\"40.5581\",\"destination.longitude\":\"-111.9222\"},{\"fortinet.traffic.destination.city\":\"Tseung Kwan O\",\"fortinet.traffic.bytes^avg\":12159,\"destination.latitude\":\"22.2908\",\"destination.longitude\":\"114.1501\"},{\"fortinet.traffic.destination.city\":\"North Bergen\",\"fortinet.traffic.bytes^avg\":13432,\"destination.latitude\":\"40.793\",\"destination.longitude\":\"-74.0247\"},{\"fortinet.traffic.destination.city\":\"Phoenix\",\"fortinet.traffic.bytes^avg\":9822,\"destination.latitude\":\"-29.7109\",\"destination.longitude\":\"31.0176\"},{\"fortinet.traffic.destination.city\":\"Lappeenranta\",\"fortinet.traffic.bytes^avg\":8379,\"destination.latitude\":\"61.0636\",\"destination.longitude\":\"28.189\"},{\"fortinet.traffic.destination.city\":\"Las Vegas\",\"fortinet.traffic.bytes^avg\":8334,\"destination.latitude\":\"9.5426\",\"destination.longitude\":\"-68.6361\"},{\"fortinet.traffic.destination.city\":\"Fortaleza\",\"fortinet.traffic.bytes^avg\":8586,\"destination.latitude\":\"-3.7139\",\"destination.longitude\":\"-38.5409\"},{\"fortinet.traffic.destination.city\":\"Incheon\",\"fortinet.traffic.bytes^avg\":9611,\"destination.latitude\":\"37.4585\",\"destination.longitude\":\"126.7015\"},{\"fortinet.traffic.destination.city\":\"Moscow\",\"fortinet.traffic.bytes^avg\":1728,\"destination.latitude\":\"55.7483\",\"destination.longitude\":\"37.6171\"},{\"fortinet.traffic.destination.city\":\"Piscataway\",\"fortinet.traffic.bytes^avg\":6725,\"destination.latitude\":\"40.5511\",\"destination.longitude\":\"-74.4606\"},{\"fortinet.traffic.destination.city\":\"Guangzhou\",\"fortinet.traffic.bytes^avg\":3790,\"destination.latitude\":\"23.1181\",\"destination.longitude\":\"113.2539\"},{\"fortinet.traffic.destination.city\":\"Colbert\",\"fortinet.traffic.bytes^avg\":58059,\"destination.latitude\":\"34.035\",\"destination.longitude\":\"-83.2191\"},{\"fortinet.traffic.destination.city\":\"Johannesburg\",\"fortinet.traffic.bytes^avg\":372,\"destination.latitude\":\"-26.3811\",\"destination.longitude\":\"27.8376\"},{\"fortinet.traffic.destination.city\":\"Hyderabad\",\"fortinet.traffic.bytes^avg\":714253,\"destination.latitude\":\"25.3507\",\"destination.longitude\":\"68.3534\"},{\"fortinet.traffic.destination.city\":\"Kochi\",\"fortinet.traffic.bytes^avg\":260,\"destination.latitude\":\"9.9185\",\"destination.longitude\":\"76.2558\"},{\"fortinet.traffic.destination.city\":\"North Charleston\",\"fortinet.traffic.bytes^avg\":50073,\"destination.latitude\":\"32.8608\",\"destination.longitude\":\"-79.9746\"}]}"))
            .put("testLogGroupMapWithSourceDestinationCity", new JsonObject("{\"result\":[{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Mumbai\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"19.0748\",\"source.longitude\":\"72.8856\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Jiaozuo\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"35.2417\",\"source.longitude\":\"113.2339\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Fremont\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"41.2075\",\"source.longitude\":\"-92.4359\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Durgapur\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"24.4459\",\"source.longitude\":\"88.766\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Zurich\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"47.3682\",\"source.longitude\":\"8.5671\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Wuxi\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"31.5618\",\"source.longitude\":\"120.2864\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"North Charleston\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"32.8608\",\"source.longitude\":\"-79.9746\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Ningbo\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"29.8797\",\"source.longitude\":\"121.5513\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Cairo\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"30.0588\",\"source.longitude\":\"31.2268\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Vizianagaram\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"18.1194\",\"source.longitude\":\"83.4116\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Maersta\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"59.6131\",\"source.longitude\":\"17.8495\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Woodland Hills\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"34.1765\",\"source.longitude\":\"-118.614\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Jeju City\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"33.5109\",\"source.longitude\":\"126.5264\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Chengdu\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"30.6498\",\"source.longitude\":\"104.0555\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Nellikkuppam\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"11.7755\",\"source.longitude\":\"79.6702\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Da Nang\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"16.0685\",\"source.longitude\":\"108.2215\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Linyi\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"35.0624\",\"source.longitude\":\"118.3364\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Hyderabad\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"25.3507\",\"source.longitude\":\"68.3534\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Kuala Lumpur\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"3.1413\",\"source.longitude\":\"101.685\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Ashburn\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"31.7097\",\"source.longitude\":\"-83.6625\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Santiago\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"17.2956\",\"source.longitude\":\"120.4412\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Amsterdam\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"-31.6252\",\"source.longitude\":\"25.6244\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Beijing\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"39.9143\",\"source.longitude\":\"116.3861\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Kottayam\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"9.5813\",\"source.longitude\":\"76.5263\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Bangkok\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"13.7512\",\"source.longitude\":\"100.5172\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Bengaluru\",\"fortinet.traffic.bytes^avg\":347,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"12.9634\",\"source.longitude\":\"77.5855\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Veraval\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"20.9094\",\"source.longitude\":\"70.364\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Thanjavur\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"10.7831\",\"source.longitude\":\"79.1359\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"unknown\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"-2.0001\",\"source.longitude\":\"30.0\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Linfen\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"36.0964\",\"source.longitude\":\"111.5218\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"unknown\",\"fortinet.traffic.bytes^avg\":2378,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"-2.0001\",\"source.longitude\":\"30.0\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Faridabad\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"28.4098\",\"source.longitude\":\"77.31\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"London\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"6.0097\",\"source.longitude\":\"125.1294\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Changzhou\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"31.7685\",\"source.longitude\":\"119.9527\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Ahmedabad\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"23.0276\",\"source.longitude\":\"72.5871\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Bengaluru\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"12.9634\",\"source.longitude\":\"77.5855\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Hangzhou\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"30.2994\",\"source.longitude\":\"120.1612\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Nagpur\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"21.1161\",\"source.longitude\":\"79.0706\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Narathiwat\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"5.9329\",\"source.longitude\":\"101.899\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Las Vegas\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"9.5426\",\"source.longitude\":\"-68.6361\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Stains\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"48.9475\",\"source.longitude\":\"2.3858\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"San Mateo\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"17.1411\",\"source.longitude\":\"121.7434\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Buk-gu\",\"fortinet.traffic.bytes^avg\":39,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"35.9034\",\"source.longitude\":\"128.5923\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Ghaziabad\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"28.665\",\"source.longitude\":\"77.4477\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Eygelshoven\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"50.8897\",\"source.longitude\":\"6.0563\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"The Dalles\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"45.5999\",\"source.longitude\":\"-121.1871\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"New Taipei\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"24.9466\",\"source.longitude\":\"121.586\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Amsterdam\",\"fortinet.traffic.bytes^avg\":1,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"-31.6252\",\"source.longitude\":\"25.6244\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Markapur\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"15.7377\",\"source.longitude\":\"79.254\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Hanoi\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"21.0292\",\"source.longitude\":\"105.8526\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Milan\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"45.4722\",\"source.longitude\":\"9.1922\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Clifton\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"-34.2633\",\"source.longitude\":\"150.9716\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Kollam\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"8.8814\",\"source.longitude\":\"76.585\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Bengaluru\",\"fortinet.traffic.bytes^avg\":173,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"12.9634\",\"source.longitude\":\"77.5855\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Santiago\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"17.2956\",\"source.longitude\":\"120.4412\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Pune\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"18.6161\",\"source.longitude\":\"73.7286\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Beauharnois\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"45.3161\",\"source.longitude\":\"-73.8736\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Buk-gu\",\"fortinet.traffic.bytes^avg\":117,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"35.9034\",\"source.longitude\":\"128.5923\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Istanbul\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"40.9993\",\"source.longitude\":\"28.9361\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Frankfurt am Main\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"50.1188\",\"source.longitude\":\"8.6843\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Slough\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"51.5368\",\"source.longitude\":\"-0.6718\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Chennai\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"12.8996\",\"source.longitude\":\"80.2209\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Amsterdam\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"-31.6252\",\"source.longitude\":\"25.6244\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Hyderabad\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"25.3507\",\"source.longitude\":\"68.3534\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Lagos\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"37.1052\",\"source.longitude\":\"-8.6713\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Ashburn\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"31.7097\",\"source.longitude\":\"-83.6625\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"North Charleston\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"32.8608\",\"source.longitude\":\"-79.9746\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Ningbo\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"29.8797\",\"source.longitude\":\"121.5513\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Hangzhou\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"30.2994\",\"source.longitude\":\"120.1612\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Gurugram\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"28.4597\",\"source.longitude\":\"77.0282\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Beijing\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"39.9143\",\"source.longitude\":\"116.3861\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Seattle\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"47.4902\",\"source.longitude\":\"-122.3004\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Etah\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"27.5588\",\"source.longitude\":\"78.6569\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Lahore\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"31.5826\",\"source.longitude\":\"74.3276\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Los Angeles\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"13.3724\",\"source.longitude\":\"-87.0314\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Beijing\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"39.9143\",\"source.longitude\":\"116.3861\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Cazadero\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"38.5923\",\"source.longitude\":\"-123.1946\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Kochi\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"9.9185\",\"source.longitude\":\"76.2558\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Chennai\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"12.8996\",\"source.longitude\":\"80.2209\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Changsha\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"22.3735\",\"source.longitude\":\"112.6855\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Mannargudi\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"10.6681\",\"source.longitude\":\"79.4504\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Staten Island\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"40.6063\",\"source.longitude\":\"-74.1774\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Thrissur\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"10.516\",\"source.longitude\":\"76.2157\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Hyderabad\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"25.3507\",\"source.longitude\":\"68.3534\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Salem\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"11.6602\",\"source.longitude\":\"78.1532\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Xi'an\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"34.2635\",\"source.longitude\":\"108.9246\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Xi'an\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"34.2635\",\"source.longitude\":\"108.9246\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"North Bergen\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"40.793\",\"source.longitude\":\"-74.0247\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Ningbo\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"29.8797\",\"source.longitude\":\"121.5513\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Kollam\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"8.8814\",\"source.longitude\":\"76.585\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Visakhapatnam\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"17.6774\",\"source.longitude\":\"83.2036\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Santa Clara\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"15.7063\",\"source.longitude\":\"120.7158\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Delhi\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"28.6542\",\"source.longitude\":\"77.2373\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Thrissur\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"10.516\",\"source.longitude\":\"76.2157\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Raipur\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"23.0403\",\"source.longitude\":\"90.7642\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Eygelshoven\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"50.8897\",\"source.longitude\":\"6.0563\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Seattle\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"47.4902\",\"source.longitude\":\"-122.3004\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Eygelshoven\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"50.8897\",\"source.longitude\":\"6.0563\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"New Delhi\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"28.6328\",\"source.longitude\":\"77.2204\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"London\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"6.0097\",\"source.longitude\":\"125.1294\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Changzhou\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"31.7685\",\"source.longitude\":\"119.9527\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Pune\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"18.6161\",\"source.longitude\":\"73.7286\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Seattle\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"47.4902\",\"source.longitude\":\"-122.3004\"},{\"fortinet.traffic.destination.city\":\"unknown\",\"fortinet.traffic.source.city\":\"unknown\",\"fortinet.traffic.bytes^avg\":72,\"destination.latitude\":\"-2.0001\",\"destination.longitude\":\"30.0\",\"source.latitude\":\"-2.0001\",\"source.longitude\":\"30.0\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Barakaldo\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"43.2982\",\"source.longitude\":\"-3.0016\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Balch Springs\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"32.7255\",\"source.longitude\":\"-96.6233\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Los Angeles\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"13.3724\",\"source.longitude\":\"-87.0314\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Paris\",\"fortinet.traffic.bytes^avg\":16966,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"7.15\",\"source.longitude\":\"1.0833\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Cedar Knolls\",\"fortinet.traffic.bytes^avg\":4203,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"40.8229\",\"source.longitude\":\"-74.4592\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Blagoevgrad\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"42.02\",\"source.longitude\":\"23.0961\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Linfen\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"36.0964\",\"source.longitude\":\"111.5218\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Bhopal\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"23.2487\",\"source.longitude\":\"77.4066\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Jaipur\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"26.9525\",\"source.longitude\":\"75.7105\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Yinchuan\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"38.4736\",\"source.longitude\":\"106.2662\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Cazadero\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"38.5923\",\"source.longitude\":\"-123.1946\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Los Angeles\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"13.3724\",\"source.longitude\":\"-87.0314\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Jaboatao dos Guararapes\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"-8.1136\",\"source.longitude\":\"-35.0145\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"San Francisco\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"15.9667\",\"source.longitude\":\"120.6833\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Toronto\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"22.7844\",\"source.longitude\":\"-82.5011\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"San Francisco\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"15.9667\",\"source.longitude\":\"120.6833\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"San Francisco\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"15.9667\",\"source.longitude\":\"120.6833\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Santa Clara\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"15.7063\",\"source.longitude\":\"120.7158\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Mumbai\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"19.0748\",\"source.longitude\":\"72.8856\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"unknown\",\"fortinet.traffic.bytes^avg\":1933,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"-2.0001\",\"source.longitude\":\"30.0\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Dhekiajuli\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"26.7021\",\"source.longitude\":\"92.4813\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Haldwani\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"29.2248\",\"source.longitude\":\"79.5313\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"North Charleston\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"32.8608\",\"source.longitude\":\"-79.9746\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Mosier\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"45.6514\",\"source.longitude\":\"-121.3833\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"North Kansas City\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"39.1484\",\"source.longitude\":\"-94.5686\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"London\",\"fortinet.traffic.bytes^avg\":2496,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"6.0097\",\"source.longitude\":\"125.1294\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Santa Clara\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"15.7063\",\"source.longitude\":\"120.7158\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Ahmedabad\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"23.0276\",\"source.longitude\":\"72.5871\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Dublin\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"-34.4558\",\"source.longitude\":\"138.3547\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Delhi\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"28.6542\",\"source.longitude\":\"77.2373\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Alblasserdam\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"51.8572\",\"source.longitude\":\"4.6766\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Staten Island\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"40.6063\",\"source.longitude\":\"-74.1774\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Kochi\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"9.9185\",\"source.longitude\":\"76.2558\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"North Bergen\",\"fortinet.traffic.bytes^avg\":4905,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"40.793\",\"source.longitude\":\"-74.0247\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Cazadero\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"38.5923\",\"source.longitude\":\"-123.1946\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Lanzhou\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"36.0647\",\"source.longitude\":\"103.8397\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Delhi\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"28.6542\",\"source.longitude\":\"77.2373\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Puducherry\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"11.8785\",\"source.longitude\":\"79.8191\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Idukki\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"9.8466\",\"source.longitude\":\"76.9689\"}]}"))
            .put("testLogGroupMapWithSourceDestination", new JsonObject("{\"result\":[{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Mumbai\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"19.0748\",\"source.longitude\":\"72.8856\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Jiaozuo\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"35.2417\",\"source.longitude\":\"113.2339\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Fremont\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"41.2075\",\"source.longitude\":\"-92.4359\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Durgapur\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"24.4459\",\"source.longitude\":\"88.766\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Zurich\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"47.3682\",\"source.longitude\":\"8.5671\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Wuxi\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"31.5618\",\"source.longitude\":\"120.2864\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"North Charleston\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"32.8608\",\"source.longitude\":\"-79.9746\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Ningbo\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"29.8797\",\"source.longitude\":\"121.5513\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Cairo\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"30.0588\",\"source.longitude\":\"31.2268\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Vizianagaram\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"18.1194\",\"source.longitude\":\"83.4116\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Maersta\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"59.6131\",\"source.longitude\":\"17.8495\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Woodland Hills\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"34.1765\",\"source.longitude\":\"-118.614\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Jeju City\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"33.5109\",\"source.longitude\":\"126.5264\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Chengdu\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"30.6498\",\"source.longitude\":\"104.0555\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Nellikkuppam\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"11.7755\",\"source.longitude\":\"79.6702\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Da Nang\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"16.0685\",\"source.longitude\":\"108.2215\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Linyi\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"35.0624\",\"source.longitude\":\"118.3364\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Hyderabad\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"25.3507\",\"source.longitude\":\"68.3534\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Kuala Lumpur\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"3.1413\",\"source.longitude\":\"101.685\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Ashburn\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"31.7097\",\"source.longitude\":\"-83.6625\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Santiago\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"17.2956\",\"source.longitude\":\"120.4412\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Amsterdam\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"-31.6252\",\"source.longitude\":\"25.6244\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Beijing\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"39.9143\",\"source.longitude\":\"116.3861\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Kottayam\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"9.5813\",\"source.longitude\":\"76.5263\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Bangkok\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"13.7512\",\"source.longitude\":\"100.5172\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Bengaluru\",\"fortinet.traffic.bytes^avg\":347,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"12.9634\",\"source.longitude\":\"77.5855\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Veraval\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"20.9094\",\"source.longitude\":\"70.364\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Thanjavur\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"10.7831\",\"source.longitude\":\"79.1359\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"unknown\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"-2.0001\",\"source.longitude\":\"30.0\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Linfen\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"36.0964\",\"source.longitude\":\"111.5218\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"unknown\",\"fortinet.traffic.bytes^avg\":2378,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"-2.0001\",\"source.longitude\":\"30.0\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Faridabad\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"28.4098\",\"source.longitude\":\"77.31\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"London\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"6.0097\",\"source.longitude\":\"125.1294\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Changzhou\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"31.7685\",\"source.longitude\":\"119.9527\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Ahmedabad\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"23.0276\",\"source.longitude\":\"72.5871\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Bengaluru\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"12.9634\",\"source.longitude\":\"77.5855\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Hangzhou\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"30.2994\",\"source.longitude\":\"120.1612\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Nagpur\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"21.1161\",\"source.longitude\":\"79.0706\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Narathiwat\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"5.9329\",\"source.longitude\":\"101.899\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Las Vegas\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"9.5426\",\"source.longitude\":\"-68.6361\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Stains\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"48.9475\",\"source.longitude\":\"2.3858\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"San Mateo\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"17.1411\",\"source.longitude\":\"121.7434\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Buk-gu\",\"fortinet.traffic.bytes^avg\":39,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"35.9034\",\"source.longitude\":\"128.5923\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Ghaziabad\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"28.665\",\"source.longitude\":\"77.4477\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Eygelshoven\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"50.8897\",\"source.longitude\":\"6.0563\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"The Dalles\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"45.5999\",\"source.longitude\":\"-121.1871\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"New Taipei\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"24.9466\",\"source.longitude\":\"121.586\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Amsterdam\",\"fortinet.traffic.bytes^avg\":1,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"-31.6252\",\"source.longitude\":\"25.6244\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Markapur\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"15.7377\",\"source.longitude\":\"79.254\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Hanoi\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"21.0292\",\"source.longitude\":\"105.8526\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Milan\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"45.4722\",\"source.longitude\":\"9.1922\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Clifton\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"-34.2633\",\"source.longitude\":\"150.9716\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Kollam\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"8.8814\",\"source.longitude\":\"76.585\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Bengaluru\",\"fortinet.traffic.bytes^avg\":173,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"12.9634\",\"source.longitude\":\"77.5855\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Santiago\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"17.2956\",\"source.longitude\":\"120.4412\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Pune\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"18.6161\",\"source.longitude\":\"73.7286\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Beauharnois\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"45.3161\",\"source.longitude\":\"-73.8736\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Buk-gu\",\"fortinet.traffic.bytes^avg\":117,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"35.9034\",\"source.longitude\":\"128.5923\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Istanbul\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"40.9993\",\"source.longitude\":\"28.9361\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Frankfurt am Main\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"50.1188\",\"source.longitude\":\"8.6843\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Slough\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"51.5368\",\"source.longitude\":\"-0.6718\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Chennai\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"12.8996\",\"source.longitude\":\"80.2209\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Amsterdam\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"-31.6252\",\"source.longitude\":\"25.6244\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Hyderabad\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"25.3507\",\"source.longitude\":\"68.3534\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Lagos\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"37.1052\",\"source.longitude\":\"-8.6713\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Ashburn\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"31.7097\",\"source.longitude\":\"-83.6625\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"North Charleston\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"32.8608\",\"source.longitude\":\"-79.9746\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Ningbo\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"29.8797\",\"source.longitude\":\"121.5513\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Hangzhou\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"30.2994\",\"source.longitude\":\"120.1612\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Gurugram\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"28.4597\",\"source.longitude\":\"77.0282\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Beijing\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"39.9143\",\"source.longitude\":\"116.3861\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Seattle\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"47.4902\",\"source.longitude\":\"-122.3004\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Etah\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"27.5588\",\"source.longitude\":\"78.6569\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Lahore\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"31.5826\",\"source.longitude\":\"74.3276\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Los Angeles\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"13.3724\",\"source.longitude\":\"-87.0314\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Beijing\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"39.9143\",\"source.longitude\":\"116.3861\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Cazadero\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"38.5923\",\"source.longitude\":\"-123.1946\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Kochi\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"9.9185\",\"source.longitude\":\"76.2558\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Chennai\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"12.8996\",\"source.longitude\":\"80.2209\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Changsha\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"22.3735\",\"source.longitude\":\"112.6855\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Mannargudi\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"10.6681\",\"source.longitude\":\"79.4504\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Staten Island\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"40.6063\",\"source.longitude\":\"-74.1774\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Thrissur\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"10.516\",\"source.longitude\":\"76.2157\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Hyderabad\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"25.3507\",\"source.longitude\":\"68.3534\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Salem\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"11.6602\",\"source.longitude\":\"78.1532\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Xi'an\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"34.2635\",\"source.longitude\":\"108.9246\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Xi'an\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"34.2635\",\"source.longitude\":\"108.9246\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"North Bergen\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"40.793\",\"source.longitude\":\"-74.0247\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Ningbo\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"29.8797\",\"source.longitude\":\"121.5513\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Kollam\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"8.8814\",\"source.longitude\":\"76.585\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Visakhapatnam\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"17.6774\",\"source.longitude\":\"83.2036\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Santa Clara\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"15.7063\",\"source.longitude\":\"120.7158\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Delhi\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"28.6542\",\"source.longitude\":\"77.2373\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Thrissur\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"10.516\",\"source.longitude\":\"76.2157\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Raipur\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"23.0403\",\"source.longitude\":\"90.7642\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Eygelshoven\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"50.8897\",\"source.longitude\":\"6.0563\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Seattle\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"47.4902\",\"source.longitude\":\"-122.3004\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Eygelshoven\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"50.8897\",\"source.longitude\":\"6.0563\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"New Delhi\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"28.6328\",\"source.longitude\":\"77.2204\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"London\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"6.0097\",\"source.longitude\":\"125.1294\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Changzhou\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"31.7685\",\"source.longitude\":\"119.9527\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Pune\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"18.6161\",\"source.longitude\":\"73.7286\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Seattle\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"47.4902\",\"source.longitude\":\"-122.3004\"},{\"fortinet.traffic.destination.city\":\"unknown\",\"fortinet.traffic.source.city\":\"unknown\",\"fortinet.traffic.bytes^avg\":72,\"destination.latitude\":\"-2.0001\",\"destination.longitude\":\"30.0\",\"source.latitude\":\"-2.0001\",\"source.longitude\":\"30.0\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Barakaldo\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"43.2982\",\"source.longitude\":\"-3.0016\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Balch Springs\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"32.7255\",\"source.longitude\":\"-96.6233\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Los Angeles\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"13.3724\",\"source.longitude\":\"-87.0314\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Paris\",\"fortinet.traffic.bytes^avg\":16966,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"7.15\",\"source.longitude\":\"1.0833\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Cedar Knolls\",\"fortinet.traffic.bytes^avg\":4203,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"40.8229\",\"source.longitude\":\"-74.4592\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Blagoevgrad\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"42.02\",\"source.longitude\":\"23.0961\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Linfen\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"36.0964\",\"source.longitude\":\"111.5218\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Bhopal\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"23.2487\",\"source.longitude\":\"77.4066\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Jaipur\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"26.9525\",\"source.longitude\":\"75.7105\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Yinchuan\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"38.4736\",\"source.longitude\":\"106.2662\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Cazadero\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"38.5923\",\"source.longitude\":\"-123.1946\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Los Angeles\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"13.3724\",\"source.longitude\":\"-87.0314\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Jaboatao dos Guararapes\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"-8.1136\",\"source.longitude\":\"-35.0145\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"San Francisco\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"15.9667\",\"source.longitude\":\"120.6833\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Toronto\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"22.7844\",\"source.longitude\":\"-82.5011\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"San Francisco\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"15.9667\",\"source.longitude\":\"120.6833\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"San Francisco\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"15.9667\",\"source.longitude\":\"120.6833\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Santa Clara\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"15.7063\",\"source.longitude\":\"120.7158\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Mumbai\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"19.0748\",\"source.longitude\":\"72.8856\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"unknown\",\"fortinet.traffic.bytes^avg\":1933,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"-2.0001\",\"source.longitude\":\"30.0\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Dhekiajuli\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"26.7021\",\"source.longitude\":\"92.4813\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Haldwani\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"29.2248\",\"source.longitude\":\"79.5313\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"North Charleston\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"32.8608\",\"source.longitude\":\"-79.9746\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Mosier\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"45.6514\",\"source.longitude\":\"-121.3833\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"North Kansas City\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"39.1484\",\"source.longitude\":\"-94.5686\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"London\",\"fortinet.traffic.bytes^avg\":2496,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"6.0097\",\"source.longitude\":\"125.1294\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Santa Clara\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"15.7063\",\"source.longitude\":\"120.7158\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Ahmedabad\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"23.0276\",\"source.longitude\":\"72.5871\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Dublin\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"-34.4558\",\"source.longitude\":\"138.3547\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Delhi\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"28.6542\",\"source.longitude\":\"77.2373\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Alblasserdam\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"51.8572\",\"source.longitude\":\"4.6766\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Staten Island\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"40.6063\",\"source.longitude\":\"-74.1774\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Kochi\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"9.9185\",\"source.longitude\":\"76.2558\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"North Bergen\",\"fortinet.traffic.bytes^avg\":4905,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"40.793\",\"source.longitude\":\"-74.0247\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Cazadero\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"38.5923\",\"source.longitude\":\"-123.1946\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Lanzhou\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"36.0647\",\"source.longitude\":\"103.8397\"},{\"fortinet.traffic.destination.city\":\"Mumbai\",\"fortinet.traffic.source.city\":\"Delhi\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"19.0748\",\"destination.longitude\":\"72.8856\",\"source.latitude\":\"28.6542\",\"source.longitude\":\"77.2373\"},{\"fortinet.traffic.destination.city\":\"Ahmedabad\",\"fortinet.traffic.source.city\":\"Puducherry\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"23.0276\",\"destination.longitude\":\"72.5871\",\"source.latitude\":\"11.8785\",\"source.longitude\":\"79.8191\"},{\"fortinet.traffic.destination.city\":\"Limbdi\",\"fortinet.traffic.source.city\":\"Idukki\",\"fortinet.traffic.bytes^avg\":0,\"destination.latitude\":\"22.5677\",\"destination.longitude\":\"71.8106\",\"source.latitude\":\"9.8466\",\"source.longitude\":\"76.9689\"}]}"))
            .put("testAuditExport", new JsonObject("{\"result\":[{\"Timestamp\":1698387797000,\"event.id^value\":\"120550997-0-7-\",\"audit.module^value\":\"Monitor\",\"audit.operation^value\":\"Update\",\"audit.user^value\":\"admin\",\"audit.message^value\":\"cisco_core.motadata.local Monitor modified successfully\\n{\\n  \\\"old.prop.value\\\" : {\\n    \\\"Group(s)\\\" : \\\"Network\\\"\\n  },\\n  \\\"new.prop.value\\\" : {\\n    \\\"Group(s)\\\" : \\\"Network,Switch,Cisco Systems\\\"\\n  }\\n}\",\"audit.status^value\":\"succeed\",\"audit.remote.ip^value\":\"127.0.0.1\"}]}"))
            .put("testMapWithSourceCountryAndCity", new JsonObject("{\"result\":[{\"source.city\":\"Nashville\",\"source.country\":\"United States\",\"volume.bytes^avg\":1406000,\"source.latitude\":\"33.9581\",\"source.longitude\":\"-93.875\"},{\"source.city\":\"Boston\",\"source.country\":\"United States\",\"volume.bytes^avg\":3036000,\"source.latitude\":\"52.9791\",\"source.longitude\":\"-0.0269\"},{\"source.city\":\"Rajkot\",\"source.country\":\"India\",\"volume.bytes^avg\":388000,\"source.latitude\":\"22.2904\",\"source.longitude\":\"70.7915\"},{\"source.city\":\"Washington\",\"source.country\":\"United States\",\"volume.bytes^avg\":484200,\"source.latitude\":\"54.9\",\"source.longitude\":\"-1.5167\"},{\"source.city\":\"Amsterdam\",\"source.country\":\"Netherlands\",\"volume.bytes^avg\":2092000,\"source.latitude\":\"-31.6252\",\"source.longitude\":\"25.6244\"},{\"source.city\":\"San Jose\",\"source.country\":\"United States\",\"volume.bytes^avg\":689243,\"source.latitude\":\"15.7494\",\"source.longitude\":\"121.0832\"},{\"source.city\":\"Columbus\",\"source.country\":\"United States\",\"volume.bytes^avg\":3036000,\"source.latitude\":\"32.4783\",\"source.longitude\":\"-84.8985\"},{\"source.city\":\"Marseille\",\"source.country\":\"France\",\"volume.bytes^avg\":1592000,\"source.latitude\":\"43.2951\",\"source.longitude\":\"5.3861\"},{\"source.city\":\"Delhi\",\"source.country\":\"India\",\"volume.bytes^avg\":3029032,\"source.latitude\":\"28.6542\",\"source.longitude\":\"77.2373\"},{\"source.city\":\"Osaka\",\"source.country\":\"Japan\",\"volume.bytes^avg\":986920,\"source.latitude\":\"34.6933\",\"source.longitude\":\"135.5067\"},{\"source.city\":\"Dallas\",\"source.country\":\"United States\",\"volume.bytes^avg\":1606000,\"source.latitude\":\"57.6101\",\"source.longitude\":\"-3.6176\"},{\"source.city\":\"Boardman\",\"source.country\":\"United States\",\"volume.bytes^avg\":1391692,\"source.latitude\":\"41.0158\",\"source.longitude\":\"-80.8041\"},{\"source.city\":\"Lucknow\",\"source.country\":\"India\",\"volume.bytes^avg\":116000,\"source.latitude\":\"26.8756\",\"source.longitude\":\"80.9115\"},{\"source.city\":\"Limbdi\",\"source.country\":\"India\",\"volume.bytes^avg\":2100000,\"source.latitude\":\"22.5677\",\"source.longitude\":\"71.8106\"},{\"source.city\":\"Colbert\",\"source.country\":\"United States\",\"volume.bytes^avg\":1284109,\"source.latitude\":\"34.035\",\"source.longitude\":\"-83.2191\"},{\"source.city\":\"Laucha\",\"source.country\":\"Germany\",\"volume.bytes^avg\":116000,\"source.latitude\":\"51.226\",\"source.longitude\":\"11.6762\"},{\"source.city\":\"Kansas City\",\"source.country\":\"United States\",\"volume.bytes^avg\":623480,\"source.latitude\":\"39.0828\",\"source.longitude\":\"-94.777\"},{\"source.city\":\"Des Moines\",\"source.country\":\"United States\",\"volume.bytes^avg\":558000,\"source.latitude\":\"41.6381\",\"source.longitude\":\"-93.6203\"},{\"source.city\":\"Raigarh\",\"source.country\":\"India\",\"volume.bytes^avg\":2633333,\"source.latitude\":\"21.896\",\"source.longitude\":\"83.4001\"},{\"source.city\":\"Detroit\",\"source.country\":\"United States\",\"volume.bytes^avg\":2948000,\"source.latitude\":\"34.0258\",\"source.longitude\":\"-88.1687\"},{\"source.city\":\"Madurai\",\"source.country\":\"India\",\"volume.bytes^avg\":1921947,\"source.latitude\":\"9.9327\",\"source.longitude\":\"78.1141\"},{\"source.city\":\"Dublin\",\"source.country\":\"Ireland\",\"volume.bytes^avg\":673500,\"source.latitude\":\"-34.4558\",\"source.longitude\":\"138.3547\"},{\"source.city\":\"Frankfurt am Main\",\"source.country\":\"Germany\",\"volume.bytes^avg\":922000,\"source.latitude\":\"50.1188\",\"source.longitude\":\"8.6843\"},{\"source.city\":\"Dombivali\",\"source.country\":\"India\",\"volume.bytes^avg\":2054666,\"source.latitude\":\"19.216\",\"source.longitude\":\"73.1034\"},{\"source.city\":\"Jaipur\",\"source.country\":\"India\",\"volume.bytes^avg\":1219100,\"source.latitude\":\"26.9525\",\"source.longitude\":\"75.7105\"},{\"source.city\":\"Los Angeles\",\"source.country\":\"United States\",\"volume.bytes^avg\":459500,\"source.latitude\":\"13.3724\",\"source.longitude\":\"-87.0314\"},{\"source.city\":\"Tappahannock\",\"source.country\":\"United States\",\"volume.bytes^avg\":298645,\"source.latitude\":\"37.9273\",\"source.longitude\":\"-76.8545\"},{\"source.city\":\"Pune\",\"source.country\":\"India\",\"volume.bytes^avg\":1105051,\"source.latitude\":\"18.6161\",\"source.longitude\":\"73.7286\"},{\"source.city\":\"Sydney\",\"source.country\":\"Australia\",\"volume.bytes^avg\":153000,\"source.latitude\":\"-33.8715\",\"source.longitude\":\"151.2006\"},{\"source.city\":\"Redmond\",\"source.country\":\"United States\",\"volume.bytes^avg\":2629090,\"source.latitude\":\"44.2762\",\"source.longitude\":\"-121.1847\"},{\"source.city\":\"Bhopal\",\"source.country\":\"India\",\"volume.bytes^avg\":2317358,\"source.latitude\":\"23.2487\",\"source.longitude\":\"77.4066\"},{\"source.city\":\"Bengaluru\",\"source.country\":\"India\",\"volume.bytes^avg\":1642466,\"source.latitude\":\"12.9634\",\"source.longitude\":\"77.5855\"},{\"source.city\":\"Ahmedabad\",\"source.country\":\"India\",\"volume.bytes^avg\":2470428,\"source.latitude\":\"23.0276\",\"source.longitude\":\"72.5871\"},{\"source.city\":\"Hyderabad\",\"source.country\":\"India\",\"volume.bytes^avg\":2925478,\"source.latitude\":\"25.3507\",\"source.longitude\":\"68.3534\"},{\"source.city\":\"Mountain View\",\"source.country\":\"United States\",\"volume.bytes^avg\":882000,\"source.latitude\":\"35.824\",\"source.longitude\":\"-92.0391\"},{\"source.city\":\"The Dalles\",\"source.country\":\"United States\",\"volume.bytes^avg\":2997394,\"source.latitude\":\"45.5999\",\"source.longitude\":\"-121.1871\"},{\"source.city\":\"Hong Kong\",\"source.country\":\"Hong Kong\",\"volume.bytes^avg\":1663360,\"source.latitude\":\"22.2842\",\"source.longitude\":\"114.1759\"},{\"source.city\":\"Tokyo\",\"source.country\":\"Japan\",\"volume.bytes^avg\":1070030,\"source.latitude\":\"35.6837\",\"source.longitude\":\"139.6805\"},{\"source.city\":\"Mumbai\",\"source.country\":\"India\",\"volume.bytes^avg\":1777303,\"source.latitude\":\"19.0748\",\"source.longitude\":\"72.8856\"},{\"source.city\":\"Saluda\",\"source.country\":\"United States\",\"volume.bytes^avg\":271000,\"source.latitude\":\"35.2348\",\"source.longitude\":\"-82.3351\"},{\"source.city\":\"Paris\",\"source.country\":\"France\",\"volume.bytes^avg\":1572000,\"source.latitude\":\"7.15\",\"source.longitude\":\"1.0833\"},{\"source.city\":\"Maiden\",\"source.country\":\"United States\",\"volume.bytes^avg\":3036000,\"source.latitude\":\"35.5759\",\"source.longitude\":\"-81.1745\"},{\"source.city\":\"El Paso\",\"source.country\":\"United States\",\"volume.bytes^avg\":116000,\"source.latitude\":\"28.6475\",\"source.longitude\":\"-17.884\"},{\"source.city\":\"New York\",\"source.country\":\"United States\",\"volume.bytes^avg\":3036000,\"source.latitude\":\"53.0834\",\"source.longitude\":\"-0.4292\"},{\"source.city\":\"Chennai\",\"source.country\":\"India\",\"volume.bytes^avg\":2399714,\"source.latitude\":\"12.8996\",\"source.longitude\":\"80.2209\"},{\"source.city\":\"Ashburn\",\"source.country\":\"United States\",\"volume.bytes^avg\":443133,\"source.latitude\":\"31.7097\",\"source.longitude\":\"-83.6625\"},{\"source.city\":\"London\",\"source.country\":\"United Kingdom\",\"volume.bytes^avg\":2060000,\"source.latitude\":\"6.0097\",\"source.longitude\":\"125.1294\"},{\"source.city\":\"North Charleston\",\"source.country\":\"United States\",\"volume.bytes^avg\":116000,\"source.latitude\":\"32.8608\",\"source.longitude\":\"-79.9746\"},{\"source.city\":\"Slough\",\"source.country\":\"United Kingdom\",\"volume.bytes^avg\":3012000,\"source.latitude\":\"51.5368\",\"source.longitude\":\"-0.6718\"},{\"source.city\":\"Seoul\",\"source.country\":\"South Korea\",\"volume.bytes^avg\":1266285,\"source.latitude\":\"37.5576\",\"source.longitude\":\"126.9937\"},{\"source.city\":\"Secaucus\",\"source.country\":\"United States\",\"volume.bytes^avg\":230000,\"source.latitude\":\"40.7876\",\"source.longitude\":\"-74.06\"},{\"source.city\":\"Council Bluffs\",\"source.country\":\"United States\",\"volume.bytes^avg\":140000,\"source.latitude\":\"41.2615\",\"source.longitude\":\"-95.8304\"},{\"source.city\":\"Newark\",\"source.country\":\"United States\",\"volume.bytes^avg\":504500,\"source.latitude\":\"35.7171\",\"source.longitude\":\"-91.4356\"},{\"source.city\":\"San Antonio\",\"source.country\":\"United States\",\"volume.bytes^avg\":116000,\"source.latitude\":\"14.9591\",\"source.longitude\":\"120.0913\"},{\"source.city\":\"Tseung Kwan O\",\"source.country\":\"Hong Kong\",\"volume.bytes^avg\":3036000,\"source.latitude\":\"22.2908\",\"source.longitude\":\"114.1501\"},{\"source.city\":\"Boydton\",\"source.country\":\"United States\",\"volume.bytes^avg\":424307,\"source.latitude\":\"36.6534\",\"source.longitude\":\"-78.375\"}]}"))
            .put("testMetricPolicyGroupByMonitor", new JsonObject("{\"result\":[{\"CLEAR\":17}]}"))
            .put("testMetricPolicyGroupByMultipleApps", new JsonObject("{\"result\":[{\"monitor\":27,\"severity\":\"CRITICAL\",\"metric.type\":\"Oracle Database\"},{\"monitor\":27,\"severity\":\"CRITICAL\",\"metric.type\":\"PostgreSQL\"},{\"monitor\":27,\"severity\":\"CRITICAL\",\"metric.type\":\"MySQL\"}]}"))
            .put("grid.inventory", new JsonObject("{\"result\":[{\"object.type\":\"Load Balancer\",\"object.vendor\":\"Linux\",\"object.ip\":\"************\",\"monitor\":\"mail.test8.local\",\"ping.latency.ms.last\":1,\"ping.latency.ms.last.formatted\":\"1 ms\",\"ping.packet.lost.percent.last\":0,\"ping.packet.lost.percent.last.formatted\":\"0%\"}]}"))
            .put("testExportLogCSV", new JsonObject("{\"result\":[{\"Timestamp\":1722426967000,\"event.id^value\":\"144590167-0-0-499999-event.history\",\"message^value\":\"<85> mindarray-dev1 compiz: pam_unix(unity:auth): authentication failure\",\"event^value\":\"\"}]}"))
            .put("testCompositeQueryWithAvailabilityJoining", new JsonObject("{\"result\":[{\"monitor\":\"1\",\"interface\":\"G 1/0\",\"interface~uptime.percent^avg\":75},{\"monitor\":\"2\",\"interface\":\"G 1/1\",\"interface~uptime.percent^avg\":25}]}"))
            .put("testTagFilterMonitor", new JsonObject("{\"result\":[{\"monitor\":\"HB1-1311.hb1.com\",\"object.ip\":\"************\",\"object.type\":\"Router\",\"object.vendor\":\"Cisco Systems\",\"system.cpu.percent.avg\":16,\"system.cpu.percent.avg.formatted\":\"16%\"}]}"))
            .put("testInstanceGridResult", new JsonObject("{\"result\":[{\"esxi.vm\":\"NEW_ISO_DIT_Sanity_172.16.15.233\",\"esxi.vm.disk.used.percent.avg\":80.88,\"esxi.vm.disk.used.percent.avg.formatted\":\"80.88%\",\"monitor\":\"esxi32.motadata.local\",\"object.ip\":\"************\",\"object.type\":\"VMware ESXi\",\"object.vendor\":\"undefined\"}]}"))
            .put("testCompositeQueryWithMetricJoining", new JsonObject("{\"result\":[{\"monitor\":\"1\",\"interface\":\"G 1/0\",\"interface~ip.address^last\":\"***********\",\"interface~traffic.utilization.percent^avg\":75,\"interface~traffic.utilization.percent^min\":25,\"interface~traffic.utilization.percent^max\":85},{\"monitor\":\"2\",\"interface\":\"G 1/1\",\"interface~ip.address^last\":\"***********\",\"interface~traffic.utilization.percent^avg\":75,\"interface~traffic.utilization.percent^min\":25,\"interface~traffic.utilization.percent^max\":85}]}"))
            .put("testCustomReportInventoryType", new JsonObject("{\"result\":[{\"monitor\":32,\"system.os.name^last\":\"Linux\",\"system.os.version^last\":\"4.4.0-142-generic\",\"system.cpu.type^last\":\"x86_64\",\"system.vendor^last\":\"GenuineIntel\",\"system.model^last\":\"Intel(R) Xeon(R) CPU E5-2658 v2 @ 2.40GHz\",\"system.cpu.cores^last\":1,\"system.memory.installed.bytes^last\":1040441344,\"system.disk.capacity.bytes^last\":80644706304}]}"))
            .put("testConfigActionHistory", new JsonObject("{\"result\":[{\"Timestamp\":\"1726233825000\",\"event.id^value\":\"148397025-0-1-\",\"config.event^value\":\"AQAAAAYAYmFja3VwAgAAAAcAc3VjY2VlZAMAAAAKAHN1Y2Nlc3NmdWwEAAAApABjaXNjbzg0MT5lbg0KUGFzc3dvcmQ6IA0KY2lzY284NDEjDQpjaXNjbzg0MSMNCmNpc2NvODQxIwp8LS0tLS0tIEV4ZWN1dGluZyBjb25maWcgdGVtcGxhdGUgOiBDaXNjbyAtLS0tLS0tfAp0ZXJtaW5hbCBsZW5ndGggMA0KY2lzY284NDEjdGVybWluYWwgbGVuZ3RoIDANCmNpc2NvODQxIwUAAAAFAGFkbWluBgAAAAwAMTAuMjAuNDAuMjE0BwAAAAAA\"}]}"))
            .put("testTagColumnInGridMonitor", new JsonObject("{\"result\":[{\"monitor\":\"10\",\"system.cpu.percent^avg\":3.85}]}"))
            .put("testTagColumnInGridInstance", new JsonObject("{\"result\":[{\"monitor\":\"10\",\"interface~status^last\":\"Up\",\"interface\":\"Te1/1/3-38\"}]}"))
            .put("testCapacityPlanReport", new JsonObject("{\"result\":[{\"Timestamp\":1722501000000,\"1^system.cpu.percent^avg\":-3.4028234663852886e+38,\"2^system.cpu.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722501300000,\"1^system.cpu.percent^avg\":-3.4028234663852886e+38,\"2^system.cpu.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722501600000,\"1^system.cpu.percent^avg\":-3.4028234663852886e+38,\"2^system.cpu.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722501900000,\"1^system.cpu.percent^avg\":-3.4028234663852886e+38,\"2^system.cpu.percent^avg\":1},{\"Timestamp\":1722502200000,\"1^system.cpu.percent^avg\":65.49,\"2^system.cpu.percent^avg\":0},{\"Timestamp\":1722502500000,\"1^system.cpu.percent^avg\":6.71,\"2^system.cpu.percent^avg\":0},{\"Timestamp\":1722502800000,\"1^system.cpu.percent^avg\":-3.4028234663852886e+38,\"2^system.cpu.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722503100000,\"1^system.cpu.percent^avg\":-3.4028234663852886e+38,\"2^system.cpu.percent^avg\":-3.4028234663852886e+38}]}"))
            .put("testCapacityPlanReport1", new JsonObject("{\"result\":[{\"Timestamp\":1722500700000,\"1^system.disk.used.percent^avg\":-3.4028234663852886e+38,\"2^system.disk.used.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722501000000,\"1^system.disk.used.percent^avg\":-3.4028234663852886e+38,\"2^system.disk.used.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722501300000,\"1^system.disk.used.percent^avg\":-3.4028234663852886e+38,\"2^system.disk.used.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722501600000,\"1^system.disk.used.percent^avg\":-3.4028234663852886e+38,\"2^system.disk.used.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722501900000,\"1^system.disk.used.percent^avg\":-3.4028234663852886e+38,\"2^system.disk.used.percent^avg\":3},{\"Timestamp\":1722502200000,\"1^system.disk.used.percent^avg\":54.95,\"2^system.disk.used.percent^avg\":3},{\"Timestamp\":1722502500000,\"1^system.disk.used.percent^avg\":54.95,\"2^system.disk.used.percent^avg\":3},{\"Timestamp\":1722502800000,\"1^system.disk.used.percent^avg\":-3.4028234663852886e+38,\"2^system.disk.used.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722503100000,\"1^system.disk.used.percent^avg\":-3.4028234663852886e+38,\"2^system.disk.used.percent^avg\":-3.4028234663852886e+38}]}"))
            .put("testCapacityPlanReport2", new JsonObject("{\"result\":[{\"Timestamp\":1722501000000,\"2^system.memory.used.percent^avg\":-3.4028234663852886e+38,\"1^system.memory.used.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722501300000,\"2^system.memory.used.percent^avg\":-3.4028234663852886e+38,\"1^system.memory.used.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722501600000,\"2^system.memory.used.percent^avg\":-3.4028234663852886e+38,\"1^system.memory.used.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722501900000,\"2^system.memory.used.percent^avg\":67.96,\"1^system.memory.used.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722502200000,\"2^system.memory.used.percent^avg\":67.82,\"1^system.memory.used.percent^avg\":64.9},{\"Timestamp\":1722502500000,\"2^system.memory.used.percent^avg\":67.81,\"1^system.memory.used.percent^avg\":62.61},{\"Timestamp\":1722502800000,\"2^system.memory.used.percent^avg\":-3.4028234663852886e+38,\"1^system.memory.used.percent^avg\":-3.4028234663852886e+38},{\"Timestamp\":1722503100000,\"2^system.memory.used.percent^avg\":-3.4028234663852886e+38,\"1^system.memory.used.percent^avg\":-3.4028234663852886e+38}]}"))
            .put("testTopNTagWithSparklineINT64", new JsonObject("{\"result\":[{\"monitor\":\"4\",\"system.cpu.percent^avg\":110864869692,\"system.cpu.percent^sparkline\":{\"1723539600000\":110803927617,\"1723539900000\":-9223372036854775808,\"1723540200000\":-9223372036854775808,\"1723540500000\":-9223372036854775808,\"1723540800000\":-9223372036854775808,\"1723541100000\":110822494080,\"1723541400000\":-9223372036854775808,\"1723541700000\":-9223372036854775808,\"1723542000000\":-9223372036854775808,\"1723542300000\":-9223372036854775808,\"1723542600000\":-9223372036854775808,\"1723542900000\":110845666715,\"1723543200000\":-9223372036854775808,\"1723543500000\":110854627106,\"1723543800000\":-9223372036854775808,\"1723544100000\":110861490315,\"1723544400000\":-9223372036854775808,\"1723544700000\":110868611952,\"1723545000000\":-9223372036854775808,\"1723545300000\":110875649171,\"1723545600000\":-9223372036854775808,\"1723545900000\":-9223372036854775808,\"1723546200000\":-9223372036854775808,\"1723546500000\":-9223372036854775808,\"1723546800000\":-9223372036854775808,\"1723547100000\":110897950158,\"1723547400000\":-9223372036854775808,\"1723547700000\":110905397146,\"1723548000000\":-9223372036854775808,\"1723548300000\":110912882664,\"1723548600000\":-9223372036854775808,\"1723548900000\":-9223372036854775808,\"1723549200000\":-9223372036854775808}}]}"))
            .put("testTopNTagWithSparklineFLOAT32", new JsonObject("{\"result\":[{\"monitor\":\"4\",\"system.cpu.percent^avg\":110864869692,\"system.cpu.percent^sparkline\":{\"1723420800000\":-3.4028234663852886E38,\"1723421100000\":-3.4028234663852886E38,\"1723421400000\":-3.4028234663852886E38,\"1723421700000\":-3.4028234663852886E38,\"1723422000000\":-3.4028234663852886E38,\"1723422300000\":-3.4028234663852886E38,\"1723422600000\":-3.4028234663852886E38,\"1723422900000\":-3.4028234663852886E38,\"1723423200000\":-3.4028234663852886E38,\"1723423500000\":-3.4028234663852886E38,\"1723423800000\":-3.4028234663852886E38,\"1723424100000\":-3.4028234663852886E38,\"1723424400000\":-3.4028234663852886E38,\"1723424700000\":-3.4028234663852886E38,\"1723425000000\":-3.4028234663852886E38,\"1723425300000\":-3.4028234663852886E38,\"1723425600000\":-3.4028234663852886E38,\"1723425900000\":-3.4028234663852886E38,\"1723426200000\":-3.4028234663852886E38,\"1723426500000\":-3.4028234663852886E38,\"1723426800000\":-3.4028234663852886E38,\"1723427100000\":-3.4028234663852886E38,\"1723427400000\":-3.4028234663852886E38,\"1723427700000\":-3.4028234663852886E38,\"1723428000000\":-3.4028234663852886E38}}]}"))
            .put("testTopNTagWithSparklineINT32", new JsonObject("{\"result\":[{\"monitor\":\"4\",\"system.cpu.percent^avg\":110864869692,\"system.cpu.percent^sparkline\":{\"1723420800000\":-2147483648,\"1723421100000\":-2147483648,\"1723421400000\":-2147483648,\"1723421700000\":-2147483648,\"1723422000000\":-2147483648,\"1723422300000\":-2147483648,\"1723422600000\":-2147483648,\"1723422900000\":-2147483648,\"1723423200000\":-2147483648,\"1723423500000\":-2147483648}}]}"))
            .put("testShadowCounterQuery", new JsonObject("{\"result\":[{\"monitor\":\"1\",\"interface~traffic.bytes.per.sec\":3299376,\"interface~traffic.bits.per.sec\":2428341120}]}"))
            .put("testAvailabilityInstanceFilterGroupTags", new JsonObject("{\"result\":[{\"interface.uptime.percent\":100,\"tag\":\"instanceKey:value\"}]}"))
            .put("testAvailabilityMonitorFilterGroupTags", new JsonObject("{\"result\":[{\"monitor.uptime.percent\":100,\"tag\":\"Development:8.0\"}]}"))
            .put("testStatusFlapReport", new JsonObject("{\"result\":[{\"Timestamp\":1722501000000,\"duration\":10,\"instance\":\"Slot0/1-1\",\"status.flap.history\":\"UP\",\"object.id\":\"14\"}]}"))
            .put("testMapLogSophosGeoMapping", new JsonObject("{\"result\":[{\"event.source.type\":\"Firewall\",\"sophos.firewall.source.city\":\"Hangzhou\",\"sophos.firewall.source.country\":\"China\",\"sophos.firewall.source.ip^count\":4,\"sophos.firewall.destination.city\":\"Hangzhou\",\"sophos.firewall.destination.country\":\"China\",\"sophos.firewall.destination.ip^count\":4}]}"))
            .put("testHCIInventory", new JsonObject("{\"result\":[{\"monitor\":\"1\",\"nutanix.cpu.percent^last\":10.0,\"nutanix.memory.used.percent^last\":60.0,\"nutanix.cluster^last\":\"Motadata-Cluster\",\"total.vms^last\":3}]}"))
            .put("testMetricExplorerRaw", new JsonObject("{\"result\":[{\"Timestamp\":1746681741000,\"object.id\":\"1\",\"system.cpu.percent^value\":0.0},{\"Timestamp\":1746681142000,\"object.id\":\"1\",\"system.cpu.percent^value\":0.0},{\"Timestamp\":1746680841000,\"object.id\":\"1\",\"system.cpu.percent^value\":0.0},{\"Timestamp\":1746680541000,\"object.id\":\"1\",\"system.cpu.percent^value\":0.0},{\"Timestamp\":1746680241000,\"object.id\":\"1\",\"system.cpu.percent^value\":0.0},{\"Timestamp\":1746679941000,\"object.id\":\"1\",\"system.cpu.percent^value\":0.0},{\"Timestamp\":1746679641000,\"object.id\":\"1\",\"system.cpu.percent^value\":0.0}]}"))
            .put("testLogStreamWidget", new JsonObject("{\"result\":[{\"Timestamp\":1740464268000,\"event.id\":\"162627468-0-0-\",\"severity\":\"MAJOR\",\"event.field\":\"message\",\"event.source\":\"\",\"policy.id\":67931083160}]}"));

    private static final JsonObject RESPONSES = new JsonObject();
    private static final JsonObject AUDIT_USER_CONTEXT = new JsonObject()
            .put(USER_NAME, "audit.user")
            .put(USER_FIRST_NAME, "audit1")
            .put(USER_LAST_NAME, "admin5")
            .put(USER_EMAIL, "<EMAIL>")
            .put(USER_MOBILE, 1234567890)
            .put(USER_PASSWORD, "MindUser@123")
            .put(USER_STATUS, "yes")
            .put(USER_ROLE, DEFAULT_ID)
            .put(USER_GROUPS, new JsonArray().add(10000000000013L))
            .put(USER_TYPE, USER_TYPE_SYSTEM);
    private static final AtomicBoolean HAS_MORE_EVENTS = new AtomicBoolean(true);
    private static final boolean trapExplorer = false;
    private static final AtomicInteger COUNTER = new AtomicInteger(0);
    private static final JsonArray QUERY_IDS = new JsonArray();
    private static MessageConsumer<JsonObject> messageConsumer;
    private static MessageConsumer<JsonObject> consumer;
    private static JsonObject object = new JsonObject();
    private static boolean buildEntityKeys = false;
    private static boolean eventQuery = false;
    private static boolean applicationStatus = false;
    private static boolean drillDown = false;
    private static boolean horizontalTopN = false;
    private static boolean eventHistory = false;
    private static boolean eventCachePolicy = false;
    private static boolean eventApplicationPolicy = false;
    private static boolean abortQuery = false;
    private static boolean mapWidget = false;
    private static boolean auditExport = false;
    private static boolean gridCpuResult = false;
    private static boolean configActionHistory = false;
    private static boolean capacityPlanning = false;
    private static boolean gridVMInstanceResult = false;
    private static boolean logExport = false;
    private static boolean customInventoryType = false;
    private static boolean instanceAvailabilityFilterGroup = false;
    private static boolean monitorAvailabilityFilterGroup = false;
    private static boolean mapLogWidget = false;
    private static boolean objectTag = false;
    private static boolean instanceTag = false;
    private static boolean sparklineResultINT64 = false;
    private static boolean sparklineResultINT32 = false;
    private static boolean sparklineResultFLOAT32 = false;
    private static boolean shadowCounterQuery = false;
    private static boolean hciInventory = false;
    private static boolean streamLogWidget = false;
    private static boolean metricExplorerRaw = false;
    private static int dataSourceQueries = 0;
    private static int appStatusQueries = 0;
    private static int dataSourceQueriesSize = 0;
    private static int appStatusQueriesSize = 0;
    private static boolean statusflapReport = false;
    private static String testCase = EMPTY_VALUE;
    private static long queryId = -1L;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext) throws InterruptedException
    {

        LOGGER.info("Discovery Config Store items : " + DiscoveryConfigStore.getStore().getItems().encode());

        LOGGER.info(VALUE_SEPARATOR_WITH_ESCAPE);

        LOGGER.info("Object Config Store items : " + ObjectConfigStore.getStore().getItems().encode());

        LOGGER.info(VALUE_SEPARATOR_WITH_ESCAPE);

        LOGGER.info("Metric Config Store items : " + MetricConfigStore.getStore().getItems().encode());

        LOGGER.info(VALUE_SEPARATOR_WITH_ESCAPE);

        LOGGER.info("Tag Config Store items : " + TagConfigStore.getStore().getItems().encode());

        LOGGER.info(VALUE_SEPARATOR_WITH_ESCAPE);

        var registration = new JsonObject()
                .put(EVENT_TYPE, EVENT_REGISTRATION)
                .put(REMOTE_EVENT_PROCESSOR_HOST, "test-case-db")
                .put(REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, InstallationMode.PRIMARY.name())
                .put(REMOTE_EVENT_PROCESSOR_TYPE, BootstrapType.DATASTORE.name())
                .put(REMOTE_EVENT_PROCESSOR_IP, "*************")
                .put(REMOTE_EVENT_PROCESSOR_UUID, "primary_db_uuid")
                .put(REMOTE_EVENT_PROCESSOR_VERSION, "8.0.4");

        TestUtil.vertx().eventBus().send(EVENT_REGISTRATION, registration);

        TestUtil.vertx().setPeriodic(2 * 1000, timer ->
        {
            var object = RemoteEventProcessorCacheStore.getStore().getItem(RemoteEventProcessorConfigStore.getStore().getItemIdByUUID("primary_db_uuid", BootstrapType.DATASTORE.name(), InstallationMode.PRIMARY.name()));

            RemoteEventProcessorCacheStore.getStore().updateItem(RemoteEventProcessorConfigStore.getStore().getItemIdByUUID("primary_db_uuid", BootstrapType.DATASTORE.name(), InstallationMode.PRIMARY.name()), object.put(STATUS, STATUS_UP).put(HEARTBEAT_STATE, STATE_RUNNING));
        });

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, "************").put(EventBusConstants.EVENT, EVENT_LOG).put(PLUGIN_ID, 500005).put(CHANGE_NOTIFICATION_TYPE, ADD_EVENT_SOURCE));

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, "10.20.40.234").put(EventBusConstants.EVENT, EVENT_LOG).put(PLUGIN_ID, 500005).put(CHANGE_NOTIFICATION_TYPE, ADD_EVENT_SOURCE));

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, "10.20.40.87").put(EventBusConstants.EVENT, EVENT_LOG).put(PLUGIN_ID, 500005).put(CHANGE_NOTIFICATION_TYPE, ADD_EVENT_SOURCE));

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, "***********").put(EventBusConstants.EVENT, EVENT_TRAP).put(PLUGIN_ID, 500001).put(CHANGE_NOTIFICATION_TYPE, ADD_EVENT_SOURCE));

        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, "10.20.40.8").put(EventBusConstants.EVENT, EVENT_TRAP).put(PLUGIN_ID, 500001).put(CHANGE_NOTIFICATION_TYPE, ADD_EVENT_SOURCE));

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE,
                new JsonObject()
                        .put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_EVENT_COLUMN.name())
                        .put(DatastoreConstants.MAPPER, DatastoreConstants.DataCategory.STRING.getName() + GlobalConstants.COLUMN_SEPARATOR + 500019 + GlobalConstants.COLUMN_SEPARATOR + "config.event" + GlobalConstants.COLUMN_SEPARATOR + NO + COLUMN_SEPARATOR + VisualizationConstants.VisualizationDataSource.STATIC_METRIC.getName()));

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE,
                new JsonObject()
                        .put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_EVENT_COLUMN.name())
                        .put(DatastoreConstants.MAPPER, DatastoreConstants.DataCategory.STRING.getName() + COLUMN_SEPARATOR + DatastoreConstants.PluginId.GENERIC_LOG_EVENT.getName() + COLUMN_SEPARATOR + MESSAGE + COLUMN_SEPARATOR + NO + COLUMN_SEPARATOR + VisualizationConstants.VisualizationDataSource.EVENT_HISTORY.getName()));

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE,
                new JsonObject()
                        .put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_EVENT_COLUMN.name())
                        .put(DatastoreConstants.MAPPER, DatastoreConstants.DataCategory.STRING.getName() + COLUMN_SEPARATOR + DatastoreConstants.PluginId.GENERIC_LOG_EVENT.getName() + COLUMN_SEPARATOR + SEVERITY + COLUMN_SEPARATOR + NO + COLUMN_SEPARATOR + VisualizationConstants.VisualizationDataSource.POLICY.getName()));

        var trap = new JsonObject("{\"trap.oid\":\".*******.*******.5.5\",\"trap.version\":\"v2c\",\"event.source\":\"127.0.0.1\",\"trap.severity\":\"unknown\",\"trap.type\":\"authenticationFailure\",\"trap.message\":\"An authenticationFailure trap signifies that the SNMP entity has received a protocol message that is not properly authenticated. While all implementations of SNMP entities MAY be capable of generating this trap, the snmpEnableAuthenTraps object indicates whether this trap will be generated.\",\"trap.vendor\":\"NXNETWORK\"}");

        trap.getMap().forEach((key, value) ->
                Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE,
                        new JsonObject()
                                .put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_EVENT_COLUMN.name())
                                .put(DatastoreConstants.MAPPER, 0 + GlobalConstants.COLUMN_SEPARATOR + DatastoreConstants.PluginId.TRAP_EVENT.getName() + GlobalConstants.COLUMN_SEPARATOR + key + GlobalConstants.COLUMN_SEPARATOR + NO)));


        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        LOGGER.info("RemoteEventProcessor:- " + RemoteEventProcessorConfigStore.getStore().getItems().encode());

        subscribe(MotadataConfigUtil.getDatastoreReaderPort());

        MotadataConfigUtil.loadConfigs(new JsonObject().put("env.type", "test"));

        var deployedVerticles = new AtomicInteger();

        var promise = Promise.<Void>promise();

        var visualizationManagers = new JsonArray().add(VisualizationEventManager.class.getSimpleName()).add(VisualizationMetricManager.class.getSimpleName()).add(VisualizationManager.class.getSimpleName()).add(VisualizationEventHistoryManager.class.getSimpleName()).add(VisualizationAvailabilityManager.class.getSimpleName());

        for (var manager : visualizationManagers)
        {
            Bootstrap.undeployVerticle(CommonUtil.getString(manager)).onComplete(undeploy ->
            {
                deployedVerticles.getAndIncrement();

                if (deployedVerticles.get() == 5)
                {
                    promise.complete();
                }
            });
        }

        promise.future().onComplete(asyncResult ->
                Bootstrap.startEngine(new LocalEventRouter(VISUALIZATION_TEST, ID,
                                1, "com.mindarray.visualization.VisualizationManager", true), VisualizationManager.class.getSimpleName(), null)
                        .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_VISUALIZATION_METRIC, ID,
                                1, "com.mindarray.visualization.VisualizationMetricManager", true), VisualizationMetricManager.class.getSimpleName(), null))
                        .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_VISUALIZATION_EVENT, ID,
                                1, "com.mindarray.visualization.VisualizationEventManager", true), VisualizationEventManager.class.getSimpleName(), null))
                        .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_VISUALIZATION_AVAILABILITY, ID,
                                1, "com.mindarray.visualization.VisualizationAvailabilityManager", true), VisualizationAvailabilityManager.class.getSimpleName(), null))
                        .compose(future -> Bootstrap.startEngine(new LocalEventRouter(EventBusConstants.EVENT_VISUALIZATION_EVENT_HISTORY, ID,
                                1, "com.mindarray.visualization.VisualizationEventHistoryManager", true), VisualizationEventHistoryManager.class.getSimpleName(), null))
                        .onComplete(result ->
                        {

                            for (var index = 0; index < 10; index++)
                            {
                                Bootstrap.vertx().eventBus().send(EVENT_FLOW, new JsonObject("{\"tag\": 1, \"class\": \"unknown\", \"peer_ip_src\": \"*******\", \"peer_ip_dst\": \"\", \"iface_in\": 0, \"iface_out\": 0, \"ip_src\": \"*******\", \"ip_dst\": \"*******\", \"port_src\": 40, \"port_dst\": 80, \"tcp_flags\": \"0\", \"ip_proto\": \"6\", \"tos\": 0, \"sampling_rate\": 10, \"timestamp_start\": \"2022-09-07 14:56:11.000000\", \"timestamp_end\": \"2022-09-07 14:56:11.000000\", \"export_proto_seqno\": 1, \"export_proto_version\": 5, \"flows\": 1, \"packets\": 902, \"bytes\": 393, \"flow_type\": \"NetFlow\"}"));

                                Bootstrap.vertx().eventBus().send(EVENT_FLOW, new JsonObject("{\"tag\": 2, \"class\": \"unknown\", \"peer_ip_src\": \"*******\", \"peer_ip_dst\": \"\", \"iface_in\": 0, \"iface_out\": 0, \"ip_src\": \"*******\", \"ip_dst\": \"*******\", \"port_src\": 40, \"port_dst\": 80, \"tcp_flags\": \"0\", \"ip_proto\": \"6\", \"tos\": 7, \"sampling_rate\": 10, \"timestamp_start\": \"2022-09-07 14:56:11.000000\", \"timestamp_end\": \"2022-09-07 14:56:11.000000\", \"export_proto_seqno\": 1, \"export_proto_version\": 5, \"flows\": 1, \"packets\": 902, \"bytes\": 393, \"flow_type\": \"NetFlow\"}"));
                            }

                            for (var index = 0; index < 10; index++)
                            {
                                Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, "10.20.40." + index).put(EventBusConstants.EVENT, EVENT_LOG).put(EVENT_CATEGORY, "Linux Syslog").put(PLUGIN_ID, 600004).put(CHANGE_NOTIFICATION_TYPE, ADD_EVENT_SOURCE));
                            }

                            Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, "***********").put(EventBusConstants.EVENT, EVENT_LOG).put(EVENT_CATEGORY, "Linux Syslog").put(PLUGIN_ID, 600004).put(SOURCE_GROUPS, new JsonArray().add(10000000000017L)).put(CHANGE_NOTIFICATION_TYPE, ADD_EVENT_SOURCE));

                            Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_SOURCE, "10.20.41.35").put(EventBusConstants.EVENT, EVENT_LOG).put(EVENT_CATEGORY, "Sophos Firewall").put(PLUGIN_ID, 600013).put(CHANGE_NOTIFICATION_TYPE, ADD_EVENT_SOURCE));

                            TestAPIUtil.put("/api/v1/settings/log-parsers/10000000000004/assign", new JsonObject("{\"params\":[\"10.21.41.100\",\"10.20.41.146\",\"101.21.40.147\"]}"), testContext.succeeding(response -> testContext.verify(() -> LOGGER.trace("Log Parser assigned succesfully"))));

                            TestAPIUtil.put("/api/v1/settings/log-parsers/10000000000005/assign", new JsonObject("{\"params\":[\"123.124.125.126\"]}"), testContext.succeeding(response -> testContext.verify(() -> LOGGER.trace("Log Parser assigned succesfully"))));


                            var objects = ObjectConfigStore.getStore().getItemsByValues(OBJECT_TYPE, new JsonArray().add(NMSConstants.Type.LINUX.getName()).add(NMSConstants.Type.WINDOWS.getName()));

                            var counter = 0;

                            for (var object : objects)
                            {
                                if (counter % 2 == 0)
                                {
                                    ObjectStatusCacheStore.getStore().updateItem(JsonObject.mapFrom(object).getLong(ID), GlobalConstants.StatusType.DOWN.name(), DateTimeUtil.currentSeconds());
                                }
                                else
                                {

                                    ObjectStatusCacheStore.getStore().updateItem(JsonObject.mapFrom(object).getLong(ID), GlobalConstants.StatusType.UP.name(), DateTimeUtil.currentSeconds());
                                }

                                counter++;
                            }

                            var networkObjects = ObjectConfigStore.getStore().getItemsByValues(OBJECT_TYPE, new JsonArray().add(NMSConstants.Type.SWITCH.getName()).add(NMSConstants.Type.FIREWALL.getName()));

                            for (var networkObject : networkObjects)
                            {
                                if (counter % 2 == 0)
                                {
                                    for (var index = 0; index < 5; index++)
                                    {
                                        ObjectStatusCacheStore.getStore().updateItem(JsonObject.mapFrom(networkObject).getLong(ID), NMSConstants.INTERFACE + INSTANCE_SEPARATOR + "Gi1/0/18-" + index + INSTANCE_SEPARATOR + 201, GlobalConstants.StatusType.DOWN.name(), DateTimeUtil.currentSeconds());
                                    }
                                }
                                else
                                {

                                    ObjectStatusCacheStore.getStore().updateItem(JsonObject.mapFrom(networkObject).getLong(ID), NMSConstants.NETWORK_SERVICE + INSTANCE_SEPARATOR + "SSH(14)" + INSTANCE_SEPARATOR + 200, GlobalConstants.StatusType.UP.name(), DateTimeUtil.currentSeconds());
                                }

                                counter++;
                            }

                            var wirelessObjects = ObjectConfigStore.getStore().getItemsByValues(OBJECT_TYPE, new JsonArray().add(NMSConstants.Type.CISCO_WIRELESS.getName()).add(NMSConstants.Type.ARUBA_WIRELESS.getName()));

                            for (var wirelessObject : wirelessObjects)
                            {
                                if (counter % 2 == 0)
                                {
                                    for (var index = 0; index < 5; index++)
                                    {
                                        ObjectStatusCacheStore.getStore().updateItem(JsonObject.mapFrom(wirelessObject).getLong(ID), NMSConstants.CISCO_WIRELESS_ACCESS_POINT + INSTANCE_SEPARATOR + "Gi1/0/18-" + index + INSTANCE_SEPARATOR + 201, GlobalConstants.StatusType.UP.name(), DateTimeUtil.currentSeconds());
                                    }
                                }

                                counter++;
                            }

                            var visualizationObjects = ObjectConfigStore.getStore().getItemsByValues(OBJECT_TYPE, new JsonArray().add(NMSConstants.Type.CITRIX_XEN.getName()).add(NMSConstants.Type.HYPER_V.getName()));

                            for (var visualizationObject : visualizationObjects)
                            {
                                if (counter % 2 == 0)
                                {
                                    for (var index = 0; index < 5; index++)
                                    {
                                        ObjectStatusCacheStore.getStore().updateItem(JsonObject.mapFrom(visualizationObject).getLong(ID), NMSConstants.CITRIX_XEN_VM + INSTANCE_SEPARATOR + "Gi1/0/18-" + index + INSTANCE_SEPARATOR + 201, GlobalConstants.StatusType.UP.name(), DateTimeUtil.currentSeconds());
                                    }
                                }

                                counter++;
                            }

                            object = ObjectConfigStore.getStore().getItemsByValue(OBJECT_TYPE, "Windows").getJsonObject(0);

                            var context = new JsonObject().put(USER_PREFERENCES, new JsonObject("{ \"ui.theme\": \"auto\", \"ui.timeZone\": \"Asia/Calcutta\", \"ui.date.time.format\": \"ddd, MMM DD, yyyy hh:mm:ss A\", \"ui.columns\": {}, \"ui.page.size\": 50, \"setup.completed\": \"yes\", \"setup.guide.completed\": [], \"setup.sections.completed\": [], \"setup.steps.completed\": [] }"));

                            packDBResponse();

                            TestAPIUtil.post(USER_API_ENDPOINT, AUDIT_USER_CONTEXT,
                                    testContext.succeeding(userResponse ->

                                            testContext.verify(() ->
                                                    TestAPIUtil.put(USER_API_ENDPOINT + DEFAULT_ID, context,
                                                            testContext.succeeding(response ->
                                                                    testContext.verify(() ->
                                                                            UserConfigStore.getStore().updateItem(DEFAULT_ID).onComplete(handler -> testContext.completeNow())))))));
                        }));
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        HAS_MORE_EVENTS.set(false);

        SUBSCRIBER.close();

        testContext.completeNow();
    }


    private static void subscribe(int port)
    {
        SUBSCRIBER.setHWM(MotadataConfigUtil.getEventBacklogSize());

        SUBSCRIBER.setRcvHWM(MotadataConfigUtil.getEventBacklogSize());

        SUBSCRIBER.connect("tcp://*:" + port);

        SUBSCRIBER.subscribe("primary_db_uuid" + DOT_SEPARATOR + DATASTORE_QUERY_TOPIC);

        new Thread(() ->
        {
            while (HAS_MORE_EVENTS.get())
            {
                try
                {
                    var topicBytes = SUBSCRIBER.recv();

                    LOGGER.debug("query context received on topic::" + new String(topicBytes));

                    var bufferBytes = SUBSCRIBER.recv();

                    if (bufferBytes.length > 0)
                    {
                        var buffer = Buffer.buffer(bufferBytes);

                        LOGGER.info("event: " + new String(bufferBytes));

                        if (!(buffer.length() == 1 && buffer.getBytes()[0] == 18))
                        {
                            var event = new JsonObject(new String(buffer.getBytes(1, buffer.length())));

                            if (event.containsKey(VisualizationConstants.QUERY_ID) && ((capacityPlanning && QUERY_IDS.contains(event.getLong(VisualizationConstants.QUERY_ID))) || queryId == event.getLong(VisualizationConstants.QUERY_ID)))
                            {
                                var queryId = event.getLong(VisualizationConstants.QUERY_ID);

                                LOGGER.debug("query context received for query Id::" + queryId + " with context::" + event);

                                if (queryId == 1234567 && abortQuery)
                                {
                                    QUERY_CONTEXT.get(queryId).completeNow();
                                }
                                else
                                {
                                    var valid = true;

                                    var visualizationDataSource = event.getJsonObject(VisualizationConstants.VISUALIZATION_DATA_SOURCES);

                                    assertNotNull(visualizationDataSource.getValue(VisualizationConstants.DATA_POINTS));

                                    assertNotNull(visualizationDataSource.getValue(ENTITIES));

                                    assertNotNull(visualizationDataSource.getValue(VisualizationConstants.PLUGINS));

                                    if (visualizationDataSource.containsKey(VisualizationConstants.VISUALIZATION_RESULT_BY) && !visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).isEmpty())
                                    {
                                        if (visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).getString(0).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.GROUP.getName()) || visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).getString(0).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.TAG.getName()))
                                        {
                                            assertNotNull(visualizationDataSource.getValue(visualizationDataSource.getJsonArray(VisualizationConstants.VISUALIZATION_RESULT_BY).getString(0)));
                                        }
                                    }

                                    var completeQuery = true;

                                    if (buildEntityKeys)
                                    {
                                        for (var datapoint : visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS))
                                        {
                                            var datapointContext = JsonObject.mapFrom(datapoint);

                                            var counter = datapointContext.getString(VisualizationConstants.DATA_POINT);

                                            if (!datapointContext.containsKey(VisualizationConstants.ENTITY_KEYS))
                                            {
                                                valid = false;

                                                break;
                                            }
                                            else
                                            {
                                                for (var entry : datapointContext.getJsonObject(VisualizationConstants.ENTITY_KEYS).getMap().entrySet())
                                                {
                                                    if (((counter.contains(INSTANCE_SEPARATOR) && ((!counter.split(INSTANCE_SEPARATOR)[0].equalsIgnoreCase(NMSConstants.NETWORK_SERVICE) && !counter.split(INSTANCE_SEPARATOR)[0].equalsIgnoreCase(NMSConstants.INTERFACE) && !counter.split(INSTANCE_SEPARATOR)[0].equalsIgnoreCase(NMSConstants.SYSTEM_PROCESS) && !counter.split(INSTANCE_SEPARATOR)[0].equalsIgnoreCase(NMSConstants.ESXI_VM) && !counter.split(INSTANCE_SEPARATOR)[0].equalsIgnoreCase(NMSConstants.SYSTEM_SERVICE) && !counter.split(INSTANCE_SEPARATOR)[0].equalsIgnoreCase(NMSConstants.CITRIX_XEN_VM) && !counter.split(INSTANCE_SEPARATOR)[0].equalsIgnoreCase(NMSConstants.HYPERV_VM)) && !entry.getKey().matches("\\d+\\^.*\\^.*")) || !entry.getKey().matches("\\d+\\^.*")) || (!counter.contains(INSTANCE_SEPARATOR) && !entry.getKey().matches("\\d+\\^.*"))))
                                                    {
                                                        valid = false;

                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else
                                    {
                                        if (!(visualizationDataSource.containsKey("test.plugins") && visualizationDataSource.getString("test.plugins").equalsIgnoreCase("no")))
                                        {
                                            for (var datapoint : visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS))
                                            {
                                                var datapointContext = JsonObject.mapFrom(datapoint);

                                                var plugins = datapointContext.getJsonArray(VisualizationConstants.PLUGINS);

                                                if (eventQuery)
                                                {
                                                    assertEquals(1, plugins.size());
                                                }

                                                var plugin = new ArrayList<String>();

                                                for (var index = 0; index < plugins.size(); index++)
                                                {
                                                    var pluginId = plugins.getString(index);

                                                    if (plugin.contains(pluginId))
                                                    {
                                                        valid = false;
                                                    }
                                                    else
                                                    {
                                                        plugin.add(pluginId);
                                                    }
                                                }

                                                if (!datapointContext.containsKey(ENTITIES))
                                                {
                                                    valid = false;

                                                    break;
                                                }
                                            }
                                        }

                                        var visualizationType = visualizationDataSource.getString(VisualizationConstants.TYPE);

                                        if (shadowCounterQuery)
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testShadowCounterQuery"), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                            shadowCounterQuery = false;
                                        }

                                        if (statusflapReport)
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testStatusFlapReport").put("object.id", new JsonArray().add(ObjectConfigStore.getStore().getObjectIdById(ObjectConfigStore.getStore().getItemByIP("***********")))), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                            statusflapReport = false;
                                        }

                                        if (metricExplorerRaw)
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testMetricExplorerRaw"), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                            metricExplorerRaw = false;
                                        }


                                        if (instanceAvailabilityFilterGroup)
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testAvailabilityInstanceFilterGroupTags"), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                            instanceAvailabilityFilterGroup = false;
                                        }

                                        if (monitorAvailabilityFilterGroup)
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testAvailabilityMonitorFilterGroupTags"), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                            monitorAvailabilityFilterGroup = false;
                                        }


                                        if (hciInventory)
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testHCIInventory").put("monitor", new JsonArray().add(ObjectConfigStore.getStore().getObjectIdById(ObjectConfigStore.getStore().getItemByIP("*************")))), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                            hciInventory = false;
                                        }

                                        if (sparklineResultINT64)
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testTopNTagWithSparklineINT64").put("monitor", new JsonArray().add(ObjectConfigStore.getStore().getObjectIdById(ObjectConfigStore.getStore().getItemByIP("***********")))), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                            sparklineResultINT64 = false;
                                        }

                                        if (sparklineResultINT32)
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testTopNTagWithSparklineINT32").put("monitor", new JsonArray().add(ObjectConfigStore.getStore().getObjectIdById(ObjectConfigStore.getStore().getItemByIP("***********")))), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                            sparklineResultINT32 = false;
                                        }

                                        if (sparklineResultFLOAT32)
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testTopNTagWithSparklineFLOAT32").put("monitor", new JsonArray().add(ObjectConfigStore.getStore().getObjectIdById(ObjectConfigStore.getStore().getItemByIP("***********")))), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                            sparklineResultFLOAT32 = false;
                                        }

                                        if (streamLogWidget)
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testLogStreamWidget").put(PolicyEngineConstants.POLICY_ID, new JsonArray().add(EventPolicyConfigStore.getStore().getItemByValue(PolicyEngineConstants.POLICY_NAME, "Test policy Log").getLong(ID))), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                            streamLogWidget = false;
                                        }

                                        if (configActionHistory)
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testConfigActionHistory"), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                            configActionHistory = false;
                                        }

                                        if (gridCpuResult)
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testTagFilterMonitor"), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                            gridCpuResult = false;
                                        }

                                        if (instanceTag)
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testTagColumnInGridInstance").put("monitor", new JsonArray().add(ObjectConfigStore.getStore().getObjectIdById(ObjectConfigStore.getStore().getItemByIP("**********")))), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                            customInventoryType = false;

                                            instanceTag = false;
                                        }

                                        if (objectTag)
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testTagColumnInGridMonitor").put("monitor", new JsonArray().add(ObjectConfigStore.getStore().getObjectIdById(ObjectConfigStore.getStore().getItemByIP("***********")))), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                            customInventoryType = false;

                                            objectTag = false;
                                        }

                                        if (customInventoryType)
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testCustomReportInventoryType").put("monitor", new JsonArray().add(ObjectConfigStore.getStore().getObjectIdById(ObjectConfigStore.getStore().getItemByIP("*************")))), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                            customInventoryType = false;
                                        }

                                        if (applicationStatus)
                                        {
                                            if (visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS).getJsonObject(0).getString(VisualizationConstants.DATA_POINT).contains("system.process"))
                                            {
                                                Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonArray("application.status").getJsonObject(0), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));
                                            }
                                            else
                                            {
                                                Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonArray("application.status").getJsonObject(1), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));
                                            }

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                            appStatusQueries++;

                                            completeQuery = false;
                                        }

                                        if (gridVMInstanceResult)
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testInstanceGridResult"), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                            gridVMInstanceResult = false;
                                        }

                                        if (mapLogWidget)
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testMapLogSophosGeoMapping"), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                            mapLogWidget = false;
                                        }


                                        if (mapWidget)
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject(event.getString(VisualizationConstants.VISUALIZATION_NAME)), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                            mapWidget = false;
                                        }

                                        if (horizontalTopN)
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("horizontal.TopN"), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                            horizontalTopN = false;
                                        }


                                        if (eventHistory)
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("event.history"), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                            eventHistory = false;
                                        }

                                        if (auditExport)
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testAuditExport"), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);

                                            auditExport = false;
                                        }

                                        if (logExport)
                                        {
                                            var testBuffer = Buffer.buffer();

                                            var tags = new JsonObject().put("linux.syslog.event.type", "log").put("linux.syslog.priority", 85).put("linux.syslog.logsource", "mindarray-pc190").put("linux.syslog.program", "polkitd").put("linux.syslog.facility", "security/authorization messages").put("linux.syslog.severity", "Notice").put("event.category", "Linux Syslog").put("event.source.type", "Linux").put("event.severity", "Notice");

                                            for (var tag : tags.getMap().entrySet())
                                            {
                                                testBuffer.appendIntLE(EventOrdinalCacheStore.getStore().getOrdinal(DatastoreConstants.PluginId.GENERIC_LOG_EVENT.getName() + SEPARATOR + tag.getKey()));

                                                var bytes = CommonUtil.getString(tag.getValue()).getBytes(StandardCharsets.UTF_8);

                                                testBuffer.appendShortLE(CommonUtil.getShort(bytes.length));

                                                testBuffer.appendBytes(bytes);
                                            }

                                            var log = new JsonObject().put(EVENT, testBuffer.getBytes());

                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testExportLogCSV").put("event^value", new JsonArray().add(CommonUtil.getString(log.getValue(EVENT)))), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(5, TimeUnit.SECONDS);

                                            logExport = false;
                                        }

                                        if (drillDown)
                                        {
                                            LOGGER.debug("Drill down request : " + event);

                                            drillDown = false;

                                            assertFalse(event.getJsonObject("visualization.data.sources").getJsonObject(FILTERS).getJsonObject(VisualizationConstants.DRILL_DOWN_FILTER).isEmpty());

                                            assertEquals(1, event.getJsonObject("visualization.data.sources").getJsonObject(FILTERS).getJsonObject(VisualizationConstants.DRILL_DOWN_FILTER).getJsonArray("groups").size());

                                            assertEquals(EVENT_SOURCE, event.getJsonObject("visualization.data.sources").getJsonObject(FILTERS).getJsonObject(VisualizationConstants.DRILL_DOWN_FILTER).getJsonArray("groups").getJsonObject(0).getJsonArray("conditions").getJsonObject(0).getString(OPERAND));

                                            completeQuery = true;
                                        }

//                                    if ( visualizationType.equalsIgnoreCase(String.valueOf(DatastoreConstants.DatastoreType.TRAP.ordinal())) || visualizationType.equalsIgnoreCase(String.valueOf(DatastoreConstants.DatastoreType.TRAP_FLAP_HISTORY.ordinal()))   visualizationType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.TRAP.getName()) || visualizationType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.TRAP_FLAP.getName()) || visualizationType.equalsIgnoreCase(VisualizationConstants.VisualizationDataSource.TRAP_ACKNOWLEDGEMENT.getName()))

                                        if (visualizationType.equalsIgnoreCase(String.valueOf(DatastoreConstants.DatastoreType.TRAP.ordinal())) || visualizationType.equalsIgnoreCase(String.valueOf(DatastoreConstants.DatastoreType.TRAP_FLAP_HISTORY.ordinal())) || visualizationType.equalsIgnoreCase(String.valueOf(DatastoreConstants.DatastoreType.TRAP_ACKNOWLEDGEMENT.ordinal())))
                                        {
                                            dataSourceQueries++;

                                            completeQuery = false;

                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject(trapExplorer ? "testTrapExplorer" : visualizationType.equalsIgnoreCase("10") ? VisualizationConstants.VisualizationDataSource.TRAP_FLAP.getName() : DatastoreConstants.DatastoreType.values()[Integer.parseInt(visualizationType)].toString().toLowerCase()), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(2, TimeUnit.SECONDS);
                                        }

                                        // Condition to publish composite join query result
                                        if (visualizationDataSource.containsKey(VISUALIZATION_DATA_RESULT_KEY))
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject(visualizationDataSource.getString(VISUALIZATION_DATA_RESULT_KEY)), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            completeQuery = false;

                                            eventCachePolicy = false;

                                            eventApplicationPolicy = false;
                                        }

                                        if (eventCachePolicy)
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testMetricPolicyGroupByMonitor"), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GAUGE.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(5, TimeUnit.SECONDS);

                                            eventCachePolicy = false;

                                            completeQuery = true;

                                        }

                                        if (eventApplicationPolicy)
                                        {
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testMetricPolicyGroupByMultipleApps"), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GAUGE.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            QUERY_CONTEXT.get(queryId).awaitCompletion(5, TimeUnit.SECONDS);

                                            eventApplicationPolicy = false;
                                        }

                                        if (capacityPlanning)
                                        {
                                            // testCapacityPlanReport
                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testCapacityPlanReport"), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testCapacityPlanReport1"), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(VisualizationConstants.packGridResult(RESPONSES.getJsonObject("testCapacityPlanReport2"), VisualizationConstants.VisualizationCategoryOrdinal.VISUALIZATION_GRID.ordinal(), queryId, event.getLong(VisualizationConstants.SUB_QUERY_ID)).getBytes()));

                                            COUNTER.incrementAndGet();

                                            if (COUNTER.get() >= 3)
                                            {
                                                capacityPlanning = false;

                                                QUERY_IDS.clear();
                                            }
                                        }
                                    }

                                    if (dataSourceQueriesSize > 0 && dataSourceQueries == dataSourceQueriesSize)
                                    {
                                        completeQuery = true;
                                    }

                                    if (appStatusQueriesSize > 0 && appStatusQueries == appStatusQueriesSize)
                                    {
                                        completeQuery = true;

                                        applicationStatus = false;
                                    }

                                    if (completeQuery)
                                    {
                                        if (valid && QUERY_CONTEXT.containsKey(queryId))
                                        {
                                            QUERY_CONTEXT.get(queryId).completeNow();

                                            QUERY_CONTEXT.remove(queryId);
                                        }
                                        else
                                        {

                                            if (QUERY_CONTEXT.containsKey(queryId))
                                            {
                                                QUERY_CONTEXT.get(queryId).failNow("Invalid Context received::" + event);

                                                QUERY_CONTEXT.remove(queryId);

                                            }
                                            else
                                            {
                                                LOGGER.warn("Query Id Widget Context not received");
                                            }

                                            LOGGER.warn("Failed context::" + event);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.trace(testCase + " failed with queryId " + queryId);

                    LOGGER.error(exception);
                }
            }
        }, "DB Subscriber").start();
    }

    private static void packDBResponse()
    {
        RESPONSES_BY_TYPE.getMap().forEach((key, value) ->
        {
            var mergedResult = new JsonObject();

            try
            {
                if (key.equalsIgnoreCase("grid.inventory"))
                {
                    var gridResult = JsonObject.mapFrom(value);

                    gridResult.getJsonArray("result").getJsonObject(0).put("monitor", object.getInteger(OBJECT_ID));

                    RESPONSES.put(key, new JsonArray().add(gridResult));
                }

                else if (key.equalsIgnoreCase("application.status"))
                {
                    var appStatusResult = JsonObject.mapFrom(value);

                    var result = new JsonObject();

                    appStatusResult.getJsonArray("result1").getJsonObject(0).put("monitor", object.getInteger(OBJECT_ID)).forEach(appStatResult -> ((JsonArray) result.getMap().computeIfAbsent(appStatResult.getKey(), item -> new JsonArray())).add(appStatResult.getValue()));

                    appStatusResult.getJsonArray("result3").getJsonObject(0).put("monitor", object.getInteger(OBJECT_ID)).forEach(appStatResult -> ((JsonArray) mergedResult.getMap().computeIfAbsent(appStatResult.getKey(), item -> new JsonArray())).add(appStatResult.getValue()));

                    RESPONSES.put(key, new JsonArray().add(result).add(mergedResult));
                }
                else
                {
                    var rows = JsonObject.mapFrom(value).getJsonArray(RESULT);

                    if (key.equalsIgnoreCase("horizontal.TopN"))
                    {
                        for (var i = 0; i < rows.size(); i++)
                        {
                            rows.getJsonObject(i).put("monitor", object.getInteger(OBJECT_ID));
                        }
                    }

                    if (key.contains("Map"))
                    {
                        for (var i = 0; i < rows.size(); i++)
                        {
                            rows.getJsonObject(i).remove("source.latitude");

                            rows.getJsonObject(i).remove("source.longitude");

                            rows.getJsonObject(i).remove("destination.latitude");

                            rows.getJsonObject(i).remove("destination.longitude");
                        }
                    }

                    for (var i = 0; i < rows.size(); i++)
                    {
                        rows.getJsonObject(i).forEach(result -> ((JsonArray) mergedResult.getMap().computeIfAbsent(result.getKey(), item -> new JsonArray())).add(result.getValue()));
                    }

                    RESPONSES.put(key, mergedResult);
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        try
        {
            testContext.awaitCompletion(1, TimeUnit.SECONDS);

            LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        }
        catch (Exception ignored)
        {
        }

        queryId = -1;

        testCase = EMPTY_VALUE;

        buildEntityKeys = false;

        eventQuery = false;

        abortQuery = false;

        dataSourceQueries = 0;

        dataSourceQueriesSize = 0;

        appStatusQueries = 0;

        appStatusQueriesSize = 0;

        mapWidget = false;

        drillDown = false;

        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testHistogramGroupByMonitor(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testHistogramGroupByGroup(VertxTestContext testContext, TestInfo testInfo)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE,
                new JsonObject()
                        .put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_METRIC_COLUMN.name())
                        .put(DatastoreConstants.MAPPER, 1 + GlobalConstants.COLUMN_SEPARATOR + 100 + GlobalConstants.COLUMN_SEPARATOR + "availability~uptime.percent" + GlobalConstants.COLUMN_SEPARATOR + GlobalConstants.YES));

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE,
                new JsonObject()
                        .put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_METRIC_COLUMN.name())
                        .put(DatastoreConstants.MAPPER, 1 + GlobalConstants.COLUMN_SEPARATOR + 101 + GlobalConstants.COLUMN_SEPARATOR + "availability~uptime.percent" + GlobalConstants.COLUMN_SEPARATOR + GlobalConstants.YES));

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testHistogramGroupByTag(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testHistogram(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testGridGroupByMonitor(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testGridGroupByInstance(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testAvailabilityGaugeMultipleDataPoints(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testAvailabilityGaugeMultipleInstanceDataPoints(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testTopNMultipleInstanceDataPoints(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testTopNMultipleDataPoints(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testTopN(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testGridWithFilter(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testAvailabilityGrid(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testAvailabilityGridFilterByFilter(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE,
                new JsonObject()
                        .put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_METRIC_COLUMN.name())
                        .put(DatastoreConstants.MAPPER, DatastoreConstants.DataCategory.NUMERIC.getName() + GlobalConstants.COLUMN_SEPARATOR + 201 + GlobalConstants.COLUMN_SEPARATOR + "interface~uptime.percent" + GlobalConstants.COLUMN_SEPARATOR + NO + COLUMN_SEPARATOR + NMSConstants.INTERFACE));

        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testHistogramFilterByGroup(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testHistogramFilterByMonitor(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        var objects = ObjectConfigStore.getStore().getItemsByValues(OBJECT_TYPE, new JsonArray().add(NMSConstants.Type.WINDOWS.getName()));

        var object = objects.getJsonObject(0);

        if (object == null)
        {
            object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "************");
        }

        var visualizationDataSources = new JsonArray();

        try
        {
            var dataSources = new JsonArray().addAll((JsonArray) widgetContext.remove(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

            for (var index = 0; index < dataSources.size(); index++)
            {

                var visualizationDataSource = dataSources.getJsonObject(index);

                if (VisualizationConstants.VisualizationDataSource.valueOfName(visualizationDataSource.getString(VisualizationConstants.TYPE)) == VisualizationConstants.VisualizationDataSource.PERFORMANCE_METRIC)
                {
                    var visualizationDataDataPoints = new JsonArray();

                    var dataPoints = visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS);

                    for (var i = 0; i < dataPoints.size(); i++)
                    {

                        var visualizationDataDataPoint = dataPoints.getJsonObject(i);

                        visualizationDataDataPoint.put(ENTITIES, new JsonArray().add(object.getLong(ID)));

                        visualizationDataDataPoints.add(visualizationDataDataPoint);
                    }

                    visualizationDataSource.put(VisualizationConstants.DATA_POINTS, visualizationDataDataPoints);

                    visualizationDataSources.add(visualizationDataSource);
                }
            }

            widgetContext.put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, visualizationDataSources).put(APIConstants.SESSION_ID, TestUtil.getSessionId());

            buildEntityKeys = false;

            setQueryId(widgetContext.getLong(ID), testContext, true);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        send(widgetContext, testInfo.getTestMethod().get().getName());

        testContext.awaitCompletion(2, TimeUnit.SECONDS);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testGaugeFilterByTag(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        var visualizationDataSources = new JsonArray();

        var items = ObjectConfigStore.getStore().getItemsByValues(OBJECT_TYPE, new JsonArray().add(NMSConstants.Type.WINDOWS.getName()));

        var item = items.getJsonObject(0);

        if (item == null)
        {
            item = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "************");
        }

        item.put(OBJECT_USER_TAGS, new JsonArray().add("windows-tag"));

        var promise = Promise.<Void>promise();

        var entity = item;

        Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_OBJECT, new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                item, GlobalConstants.DEFAULT_USER, GlobalConstants.SYSTEM_REMOTE_ADDRESS, handler -> ObjectConfigStore.getStore().updateItem(entity.getLong(ID)).onComplete(result -> promise.complete()));

        try
        {
            var dataSources = new JsonArray().addAll((JsonArray) widgetContext.remove(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

            var object = item;

            promise.future().onComplete(result ->
            {
                for (var index = 0; index < dataSources.size(); index++)
                {
                    var visualizationDataSource = dataSources.getJsonObject(index);

                    if (VisualizationConstants.VisualizationDataSource.valueOfName(visualizationDataSource.getString(VisualizationConstants.TYPE)) == VisualizationConstants.VisualizationDataSource.PERFORMANCE_METRIC)
                    {

                        var dataPoints = new JsonArray();

                        for (var i = 0; i < visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS).size(); i++)
                        {
                            dataPoints.add(visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS).getJsonObject(i).put(ENTITIES, object.getJsonArray(OBJECT_USER_TAGS)));
                        }

                        visualizationDataSource.put(VisualizationConstants.DATA_POINTS, dataPoints);

                        visualizationDataSources.add(visualizationDataSource);
                    }
                }

                widgetContext.put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, visualizationDataSources).put(APIConstants.SESSION_ID, TestUtil.getSessionId());

                buildEntityKeys = false;

                setQueryId(widgetContext.getLong(ID), testContext, true);

                send(widgetContext, testInfo.getTestMethod().get().getName());
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testHistogramMultipleDataSources(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testHistogramInvalidFilter(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testGridFilterByCategory(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testAvailabilityInvalidFilter(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testChartYesterday(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testGridLast5Minutes(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testGridLast1Hour(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testGridLast6Hours(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testGaugeLast12Hours(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testTopNLast24Hours(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    void testTopNLast48Hours(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testChartHistogramLast30Days(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testHistogramLastWeek(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    void testHistogramLast90Days(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(32)
    void testHistogramLastYear(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(33)
    void testHistogramCustomTimeline(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(35)
    void testApplicationAvailability(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        widgetContext.put(ENTITY_TYPE, VisualizationConstants.VisualizationGrouping.MONITOR.getName());

        Assertions.assertNotNull(object);

        widgetContext.put(ENTITIES, new JsonArray().add(object.getLong(ID)));

        widgetContext.put(NMSConstants.APP_PROCESS, new JsonObject().put("Windows RDP", new JsonObject().put(OBJECT_TYPE, NMSConstants.SYSTEM_SERVICE).put(AIOpsObject.OBJECT_NAME, "TermService")));

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(36)
    void testApplicationAvailabilityTimeSeries(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        widgetContext.put(ENTITY_TYPE, VisualizationConstants.VisualizationGrouping.MONITOR.getName());

        Assertions.assertNotNull(object);

        widgetContext.put(ENTITIES, new JsonArray().add(object.getLong(ID)));

        widgetContext.put(NMSConstants.APP_PROCESS, new JsonObject().put("Windows RDP", new JsonObject().put(OBJECT_TYPE, NMSConstants.SYSTEM_SERVICE).put(AIOpsObject.OBJECT_NAME, "TermService")));

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(37)
    void testGridAggregationFunctionMaxLimit(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(38)
    void testGridGroupByMonitorRuckusInterface(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(39)
    void testEventHistogramGroupBySourceIP(VertxTestContext testContext, TestInfo testInfo)
    {
        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(40)
    void testEventHistogramGroupByGroup(VertxTestContext testContext, TestInfo testInfo)
    {
        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(41)
    void testEventHistogram(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(42)
    void testEventGridGroupBySourceIP(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(43)
    void testEventGridGroupByMultipleGroupBy(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(44)
    void testEventTopNMultipleDataPoints(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(45)
    void testEventTopNMultipleDataPointsByMultipleGroupBy(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(46)
    void testEventGridWithFilter(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(47)
    void testEventGauge(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(48)
    void testEventLogGridWithFilter(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(49)
    void testStatusFlap(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(50)
    void testEventTrapMultipleSources1(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        dataSourceQueriesSize = 2;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(51)
    void testEventFlowInterfaceDrillDown(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_IP, "**********");

        widgetContext.put(ENTITIES, new JsonArray().add(object.getLong(ID)));

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(52)
    void testAudit(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(53)
    void testAudit1(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(54)
    void testMetricPolicyHistogram(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(55)
    void testMetricPolicyHistogramWithFilterGroup(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(56)
    void testMetricPolicyHistogramWithGroupByMonitor(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(58)
    void testVisualizationCorrelatedMetric(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(59)
    void testMetricPolicyGauge(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(60)
    void testMetricPolicyGaugeWithFilterPolicyTag(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(61)
    void testMetricPolicyTopNWithGroupSeverity(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(62)
    void testMetricPolicyHeatmap(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    assertCacheTestResult(context, testInfo.getTestMethod().get().getName());

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });


        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(63)
    void testMetricPolicyHeatmapFilterByTag(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    assertCacheTestResult(context, testInfo.getTestMethod().get().getName());

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(64)
    void testMetricPolicyStreamWithMultipleGroupBy(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(65)
    void testMetricPolicyStreamWithMultipleDataSources(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(66)
    void testAbort(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        abortQuery = true;

        send(widgetContext, testInfo.getTestMethod().get().getName());

        queryId = 1234567L;

        Bootstrap.vertx().eventBus().send(EVENT_VISUALIZATION_QUERY_ABORT, widgetContext);

        QUERY_CONTEXT.put(1234567L, testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(57)
    void testMetricPolicyGridWithFilterPolicyGroupBySeverity(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        var visualizationDataSources = new JsonArray();

        try
        {
            var dataSources = new JsonArray().addAll((JsonArray) widgetContext.remove(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

            for (var index = 0; index < dataSources.size(); index++)
            {
                var visualizationDataSource = dataSources.getJsonObject(index);

                var visualizationDataDataPoints = new JsonArray();

                var dataPoints = visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS);

                for (var i = 0; i < dataPoints.size(); i++)
                {
                    visualizationDataDataPoints.add(dataPoints.getJsonObject(i).put(ENTITIES, new JsonArray().add(object.getLong(ID))));
                }

                visualizationDataSource.put(VisualizationConstants.DATA_POINTS, visualizationDataDataPoints);

                visualizationDataSources.add(visualizationDataSource);
            }

            widgetContext.put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, visualizationDataSources).put(APIConstants.SESSION_ID, TestUtil.getSessionId());

            eventQuery = true;

            setQueryId(widgetContext.getLong(ID), testContext, false);

            send(widgetContext, testInfo.getTestMethod().get().getName());
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(67)
    void testMetricPolicyStreamFlap(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        var visualizationDataSources = new JsonArray();

        try
        {
            var dataSources = new JsonArray().addAll((JsonArray) widgetContext.remove(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

            for (var index = 0; index < dataSources.size(); index++)
            {
                var visualizationDataSource = dataSources.getJsonObject(index);

                var visualizationDataDataPoints = new JsonArray();

                var dataPoints = visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS);

                for (var i = 0; i < dataPoints.size(); i++)
                {
                    visualizationDataDataPoints.add(dataPoints.getJsonObject(i).put(ENTITIES, new JsonArray().add(object.getLong(ID))));
                }

                visualizationDataSource.put(VisualizationConstants.DATA_POINTS, visualizationDataDataPoints);

                visualizationDataSources.add(visualizationDataSource);
            }

            widgetContext.put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, visualizationDataSources).put(APIConstants.SESSION_ID, TestUtil.getSessionId());

            eventQuery = true;

            setQueryId(widgetContext.getLong(ID), testContext, false);

            send(widgetContext, testInfo.getTestMethod().get().getName());
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(68)
    void testEventTrapMultipleSources2(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventQuery = true;

        dataSourceQueriesSize = 2;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());

        testContext.awaitCompletion(4, TimeUnit.SECONDS);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(69)
    void testCorrelationWorkLog(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.ENTITY_ID, object.getLong(ID));

        widgetContext.put(PolicyEngineConstants.POLICY_ID, object.getLong(ID));

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());

        testContext.awaitCompletion(2, TimeUnit.SECONDS);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(70)
    void testEventHistoryWithINCondition(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(71)
    void testEventHistoryWithEqualsCondition(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(72)
    void testEventHistoryWithContainsCondition(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(73)
    void testEventHistoryWithStartsWithCondition(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(74)
    void testEventHistoryWithEndsWithCondition(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(75)
    void testEventHistorySourceTypeWithINCondition(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(76)
    void testEventHistorySourceTypeWithEqualsCondition(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(77)
    void testEventHistorySourceTypeWithStartsWithCondition(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(78)
    void testEventHistorySourceTypeWithEndsWithCondition(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(79)
    void testPolicyResult(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(80)
    void testPolicyTriggerAction(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(81)
    void testPolicyLog(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(82)
    void testPolicyLog1(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(83)
    void testPolicyLogTriggerTick(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(84)
    void testCorrelatedMetric1(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());

        testContext.awaitCompletion(2, TimeUnit.SECONDS);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(85)
    void testHorizontalTopN(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        horizontalTopN = true;

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(86)
    void testEventHistory(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        eventHistory = true;

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(87)
    void testMapWithSourceCountry(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {

        var columns = new JsonArray().add("fortinet.traffic.bytes").add("fortinet.traffic.source.country").add("fortinet.traffic.source.city").add("fortinet.traffic.destination.city").add("fortinet.traffic.destination.country");

        for (var column : columns)
        {
            Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE,
                    new JsonObject()
                            .put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_EVENT_COLUMN.name())
                            .put(DatastoreConstants.MAPPER, (column.equals("fortinet.traffic.bytes") ? DatastoreConstants.DataCategory.NUMERIC.getName() : DatastoreConstants.DataCategory.STRING.getName()) + GlobalConstants.COLUMN_SEPARATOR + 600005 + GlobalConstants.COLUMN_SEPARATOR + column + GlobalConstants.COLUMN_SEPARATOR + NO + COLUMN_SEPARATOR + "fortinet.traffic"));
        }

        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        columns = new JsonArray().add("source.country").add("source.city").add("destination.country").add("destination.city").add("volume.bytes");

        for (var column : columns)
        {
            Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE,
                    new JsonObject()
                            .put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_EVENT_COLUMN.name())
                            .put(DatastoreConstants.MAPPER, (column.equals("volume.bytes") ? DatastoreConstants.DataCategory.NUMERIC.getName() : DatastoreConstants.DataCategory.STRING.getName()) + GlobalConstants.COLUMN_SEPARATOR + DatastoreConstants.PluginId.FLOW_EVENT.getName() + GlobalConstants.COLUMN_SEPARATOR + column + GlobalConstants.COLUMN_SEPARATOR + NO + COLUMN_SEPARATOR + VisualizationConstants.VisualizationDataSource.FLOW.getName()));
        }

        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        mapWidget = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(88)
    void testMapWithSourceCountryAndCity(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        mapWidget = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(89)
    void testMapWithDestinationCountryAndCity(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        mapWidget = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(90)
    void testMapWithSourceCountryAndDestinationCountry(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        mapWidget = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(91)
    void testMapWithSourceAndDestination(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        mapWidget = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(92)
    void testMapWithDestinationCountry(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        mapWidget = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(93)
    void testMapWithDestinationCity(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        mapWidget = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(94)
    void testMapWithSourceCity(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        mapWidget = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(95)
    void testAuditExport(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        auditExport = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(97)
    void testChartLastWeek(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(98)
    void testChartLastMonth(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(99)
    void testChartLastQuarter(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(100)
    void testChartLastYear(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(101)
    void testChartThisWeek(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(102)
    void testChartThisMonth(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(103)
    void testChartThisQuarter(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(104)
    void testChartThisYear(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(105)
    void testPolicyTriggerTicks(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(106)
    void testAvailabilityHeatmapVM(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    assertCacheTestResult(context, testInfo.getTestMethod().get().getName());

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());

        testContext.awaitCompletion(1, TimeUnit.SECONDS);

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(107)
    void testAvailabilityHeatmapAccessPoint(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    assertCacheTestResult(context, testInfo.getTestMethod().get().getName());

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());

        testContext.awaitCompletion(10, TimeUnit.SECONDS);

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(108)
    void testAvailabilityHeatmapApps(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    assertCacheTestResult(context, testInfo.getTestMethod().get().getName());

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());

        testContext.awaitCompletion(10, TimeUnit.SECONDS);

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(109)
    void testConfigDeviceOverviewGauge(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase("Device Overview"))
                {
                    LOGGER.info("testDeviceOverviewGauge: Result: " + context);

                    assertCacheTestResult(context, "Device Overview");

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, "Device Overview");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(110)
    void testConfigDeviceOverviewSummary(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase("Device Summary"))
                {
                    assertCacheTestResult(context, "Device Summary");

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, "Device Summary");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(112)
    void testConfigBackupSummaryGauge(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase("Backup Summary"))
                {
                    assertCacheTestResult(context, "Backup Summary");

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, "Backup Summary");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(113)
    void testConfigBackupSummary(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase("Failed Backup Summary"))
                {
                    assertCacheTestResult(context, "Failed Backup Summary");

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, "Failed Backup Summary");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(114)
    void testConfigBaselineRunningSummaryGauge(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase("Baseline-Running Conflict Overview"))
                {
                    assertCacheTestResult(context, "Baseline-Running Conflict Overview");

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, "Baseline-Running Conflict Overview");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(115)
    void testConfigBaselineRunningSummary(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase("Baseline-Running Conflict Summary"))
                {
                    assertCacheTestResult(context, "Baseline-Running Conflict Summary");

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, "Baseline-Running Conflict Summary");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(116)
    void testConfigStartupRunningSummaryGauge(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase("Startup-Running Conflict Overview"))
                {
                    assertCacheTestResult(context, "Startup-Running Conflict Overview");

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, "Startup-Running Conflict Overview");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(117)
    void testConfigStartupRunningSummary(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase("Startup-Running Conflict Summary"))
                {
                    assertCacheTestResult(context, "Startup-Running Conflict Summary");

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, "Startup-Running Conflict Summary");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(118)
    void testEventLogDataSecurityWithRestrictedUser(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        drillDown = true;

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(119)
    void testEventLogDataSecurityWithAdminUser(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(120)
    void testTagByKeyValue(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());

        testContext.awaitCompletion(10, TimeUnit.SECONDS);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(121)
    void testTagByFilterForMonitor(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary("result")), null, false, null, false, true);

                LOGGER.info(testInfo.getTestMethod().get().getName() + ": result: " + result);

                assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("monitor"));

                testContext.completeNow();
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        gridCpuResult = true;

        send(widgetContext, testInfo.getTestMethod().get().getName());

        testContext.awaitCompletion(10, TimeUnit.SECONDS);
    }

    // BUG: MOTADATA-553
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(122)
    void testAvailabilityGaugeUnknownStatus(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary("result")), null, false, null, false, true);

                LOGGER.info(testInfo.getTestMethod().get().getName() + ": result: " + result);

                assertEquals(0, result.getJsonArray(RESULT).getJsonObject(0).getInteger("up"));

                testContext.completeNow();
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        var id = ObjectConfigStore.getStore().getItemByIP("***********");

        Assertions.assertNotNull(id);

        if (ObjectStatusCacheStore.getStore().getItem(id) != null)
        {
            ObjectStatusCacheStore.getStore().deleteItem(id);
        }

        widgetContext.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonArray(VisualizationConstants.DATA_POINTS).getJsonObject(0).getJsonArray(ENTITIES).add(id);

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(123)
    void testAvailabilityHeatmapInterfaceWithFilter(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    assertCacheTestResult(context, testInfo.getTestMethod().get().getName());

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());

        testContext.awaitCompletion(1, TimeUnit.SECONDS);

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(124)
    void testAvailabilityHeatmapVMsWithTag(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    assertCacheTestResult(context, testInfo.getTestMethod().get().getName());

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());

        testContext.awaitCompletion(1, TimeUnit.SECONDS);

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(125)
    void testTrapExplorer(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {

            LOGGER.info(message.body().encodePrettily());

            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    var result = VisualizationConstants.unpack(Buffer.buffer(message.body().getJsonObject(EVENT_CONTEXT).getBinary(RESULT)), null, false, null, false, true).getJsonArray(RESULT);

                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": result: " + result);

                    assertEquals(9, result.size());

                    for (var index = 0; index < result.size(); index++)
                    {
                        assertTrue(result.getJsonObject(index).containsKey(SNMPTrapProcessor.SNMP_TRAP_ACK_STATUS));

                        if (result.getJsonObject(index).getString(SNMPTrapProcessor.SNMP_TRAP_OID).equalsIgnoreCase(".*******.4.1.644.2.4.0.1040"))
                        {
                            assertTrue(result.getJsonObject(index).getString(SNMPTrapProcessor.SNMP_TRAP_ACK_STATUS).equalsIgnoreCase(YES));
                        }
                        else if (result.getJsonObject(index).getString(SNMPTrapProcessor.SNMP_TRAP_OID).equalsIgnoreCase(".*******.*******.5.150") || result.getJsonObject(index).getString(SNMPTrapProcessor.SNMP_TRAP_OID).equalsIgnoreCase(".*******.*******.5.250"))
                        {
                            assertTrue(result.getJsonObject(index).getString(SNMPTrapProcessor.SNMP_TRAP_ACK_STATUS).equalsIgnoreCase(EMPTY_VALUE));
                        }
                        else
                        {
                            assertTrue(result.getJsonObject(index).getString(SNMPTrapProcessor.SNMP_TRAP_ACK_STATUS).equalsIgnoreCase(NO));
                        }
                    }

                    testContext.completeNow();
                }

            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(126)
    void testEventHistoryFlow(VertxTestContext testContext, TestInfo testInfo)
    {

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        eventHistory = true;

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(127)
    void testEventHistoryWithINConditionAndExcludeFilter(VertxTestContext testContext, TestInfo testInfo)
    {
        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        eventHistory = true;

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(128)
    void testAvailabilityHeatmapAccessPointWithTagInstances(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    assertCacheTestResult(context, testInfo.getTestMethod().get().getName());

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());

        testContext.awaitCompletion(1, TimeUnit.SECONDS);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(129)
    void testAvailabilityHeatmapInstanceResultByMonitor(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    assertCacheTestResult(context, testInfo.getTestMethod().get().getName());

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());

        testContext.awaitCompletion(10, TimeUnit.SECONDS);

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(130)
    void testVisualizeReportWithNoApplication(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    assertCacheTestResult(context, testInfo.getTestMethod().get().getName());

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());

        testContext.awaitCompletion(1, TimeUnit.SECONDS);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(131)
    void testMetricEntityKeyInstances(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary("result")), null, false, null, false, true);

                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": result: " + result);

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("monitor"));

                    testContext.completeNow();
                }
            }
        });

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE,
                new JsonObject()
                        .put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_METRIC_COLUMN.name())
                        .put(DatastoreConstants.MAPPER, 1 + GlobalConstants.COLUMN_SEPARATOR + 52 + GlobalConstants.COLUMN_SEPARATOR + "esxi.vm~disk.used.percent" + GlobalConstants.COLUMN_SEPARATOR + GlobalConstants.YES));

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        gridVMInstanceResult = true;

        send(widgetContext, testInfo.getTestMethod().get().getName());

        testContext.awaitCompletion(1, TimeUnit.SECONDS);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(132)
    void testMetricPolicyGroupByMonitor(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary("result")), null, false, null, false, true);

                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": result: " + result);

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("monitor"));

                    testContext.completeNow();
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventCachePolicy = true;

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(133)
    void testMetricPolicyGroupByApplication(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventCachePolicy = true;

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(134)
    void testMetricPolicyGroupByMultipleApps(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        eventApplicationPolicy = true;

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(135)
    void testAvailabilityStatus(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    assertCacheTestResult(context, testInfo.getTestMethod().get().getName());

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());

        testContext.awaitCompletion(1, TimeUnit.SECONDS);

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(136)
    void testAvailabilityStatusWithBothServiceAndProcess(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    assertCacheTestResult(context, testInfo.getTestMethod().get().getName());

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());

        testContext.awaitCompletion(1, TimeUnit.SECONDS);

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(137)
    void testNetworkGridInDashboard(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    assertCacheTestResult(context, testInfo.getTestMethod().get().getName());

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());

        testContext.awaitCompletion(1, TimeUnit.SECONDS);

    }

    @RepeatedIfExceptionsTest(suspend = 5000L)
    @Order(138)
    void testCompositeQueryWithAvailabilityAndMetricJoining(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase("Branch Link Utilization"))
                {
                    try
                    {
                        assertCacheTestResult(context, "Branch Link Utilization");

                        testContext.completeNow();
                    }
                    catch (AssertionFailedError exception)
                    {
                        LOGGER.error(exception);

                        testContext.failNow(exception.getMessage());
                    }
                    finally
                    {
                        messageConsumer.unregister(result ->
                        {
                        });
                    }
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, "Branch Link Utilization");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(139)
    void testCustomReportInventoryType(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase("Server Inventory Summary"))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals("Server Inventory Summary", context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertEquals(13, result.getJsonArray("result").size());

                    messageConsumer.unregister(test -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        customInventoryType = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, "Server Inventory Summary");

        testContext.awaitCompletion(1, TimeUnit.SECONDS);

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(140)
    void testConfigActionHistory(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    assertCacheTestResult(context, testInfo.getTestMethod().get().getName());

                    messageConsumer.unregister(result -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, true);

        configActionHistory = true;

        send(widgetContext, testInfo.getTestMethod().get().getName());

        testContext.awaitCompletion(1, TimeUnit.SECONDS);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(141)
    void testTagColumnInGridMonitor(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertTrue(VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true).getJsonArray(RESULT).getJsonObject(0).containsKey("location"));

                    messageConsumer.unregister(tag -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        widgetContext.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonArray(VisualizationConstants.DATA_POINTS).getJsonObject(0).getJsonArray(ENTITIES).add(ObjectConfigStore.getStore().getItemByIP("***********"));

        setQueryId(widgetContext.getLong(ID), testContext, false);

        objectTag = true;

        send(widgetContext, testInfo.getTestMethod().get().getName());

        testContext.awaitCompletion(1, TimeUnit.SECONDS);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(142)
    void testTagColumnInGridInstance(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("instancekey"));

                    assertEquals("value2", result.getJsonArray(RESULT).getJsonObject(0).getString("instancekey"));

                    messageConsumer.unregister(tag -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        widgetContext.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonArray(VisualizationConstants.DATA_POINTS).getJsonObject(0).getJsonArray(ENTITIES).add(ObjectConfigStore.getStore().getItemByIP("**********"));

        setQueryId(widgetContext.getLong(ID), testContext, false);

        instanceTag = true;

        send(widgetContext, testInfo.getTestMethod().get().getName());

        testContext.awaitCompletion(1, TimeUnit.SECONDS);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(143)
    void testExportLogCSV(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_CSV_EXPORT_READY))
            {
                messageConsumer.unregister(result -> testContext.completeNow());
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        logExport = true;

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(144)
    void testExportLogReport(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_CSV_EXPORT_READY))
            {
                messageConsumer.unregister(result -> testContext.completeNow());
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        logExport = true;

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(145)
    void testInstanceTagByMonitor(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("instancekey"));

                    assertEquals("instancekey:value2,instancekey:value3", result.getJsonArray(RESULT).getJsonObject(0).getString("instancekey"));

                    messageConsumer.unregister(tag -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        instanceTag = true;

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(146)
    @Timeout(360 * 1000)
    @Disabled
    void testCapacityPlanReport(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null && message.body().getJsonObject(EVENT_CONTEXT).getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase("Idle Servers"))
            {
                LOGGER.info(testInfo.getTestMethod().get().getName() + " - message: " + message.body());

                var context = message.body().getJsonObject(EVENT_CONTEXT);

                Assertions.assertNotNull(VisualizationConstants.VISUALIZATION_NAME);

                Assertions.assertNotNull(VisualizationConstants.VISUALIZATION_DATA_SOURCES);

                assertEquals("Idle Servers", context.getString(VisualizationConstants.VISUALIZATION_NAME));

                Assertions.assertNotNull(VisualizationConstants.VISUALIZATION_TYPE);

                Assertions.assertNotNull(VisualizationConstants.VISUALIZATION_CATEGORY);

                var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true).getJsonArray(RESULT).getJsonObject(0);

                LOGGER.info(testInfo.getTestMethod().get().getName() + " - result: " + result);

                assertEquals(2, result.getInteger("monitor"));

                assertEquals("100% of time CPU Utilization < 30%", result.getString("cpu.utilization"));

                assertEquals("0% of time Memory Utilization < 30%", result.getString("memory.utilization"));

                assertEquals("0% of time Disk Utilization < 30%", result.getString("disk.utilization"));

                testContext.completeNow();

            }
        });

        testCase = testInfo.getTestMethod().get().getName();

        Bootstrap.vertx().eventBus().send(UI_ACTION_VISUALIZATION_RENDER, new JsonObject().put(ReportConstants.REPORT_ID, 1000000001031014L)
                .put(ID, -1)
                .put(SESSION_ID, TestUtil.getSessionId())
                .put(USER_NAME, "admin"));

        capacityPlanning = true;

        try
        {
            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EventBusConstants.EVENT_VISUALIZATION_TEST, message ->
            {
                try
                {
                    message.body().getMap().forEach((key, value) ->
                    {
                        try
                        {
                            if (value instanceof Long && -1 == CommonUtil.getLong(key))
                            {
                                LOGGER.debug("query id received for widget::" + key + " with query Id::" + CommonUtil.getLong(value));

                                QUERY_IDS.add(CommonUtil.getLong(value));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            LOGGER.debug("Inside SetQueryId : " + message.body());
                        }
                    });
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister();

                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(147)
    void testTopNTagWithSparklineINT64(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase("testTopNTagWithSparklineINT64"))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true).getJsonArray(RESULT).getJsonObject(0);

                    assertTrue(result.containsKey("location"));

                    assertTrue(result.containsKey("system.cpu.percent^sparkline"));

                    messageConsumer.unregister(tag -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams("testTopNTagWithSparkline");

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        var dataPoints = widgetContext.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonArray(VisualizationConstants.DATA_POINTS);

        for (var i = 0; i < dataPoints.size(); i++)
        {
            dataPoints.getJsonObject(i).getJsonArray(ENTITIES).add(ObjectConfigStore.getStore().getItemByIP("***********"));
        }

        setQueryId(widgetContext.getLong(ID), testContext, false);

        sparklineResultINT64 = true;

        send(widgetContext, "testTopNTagWithSparklineINT64");

        testContext.awaitCompletion(1, TimeUnit.SECONDS);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(148)
    void testTopNTagWithSparklineINT32(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase("testTopNTagWithSparklineINT32"))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true).getJsonArray(RESULT).getJsonObject(0);

                    assertTrue(result.containsKey("location"));

                    assertTrue(result.containsKey("system.cpu.percent^sparkline"));

                    messageConsumer.unregister(tag -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams("testTopNTagWithSparkline");

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        var dataPoints = widgetContext.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonArray(VisualizationConstants.DATA_POINTS);

        for (var i = 0; i < dataPoints.size(); i++)
        {
            dataPoints.getJsonObject(i).getJsonArray(ENTITIES).add(ObjectConfigStore.getStore().getItemByIP("***********"));
        }

        setQueryId(widgetContext.getLong(ID), testContext, false);

        sparklineResultINT32 = true;

        send(widgetContext, "testTopNTagWithSparklineINT32");

        testContext.awaitCompletion(1, TimeUnit.SECONDS);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(149)
    void testTopNTagWithSparklineFLOAT32(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase("testTopNTagWithSparklineFLOAT32"))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true).getJsonArray(RESULT).getJsonObject(0);

                    assertTrue(result.containsKey("location"));

                    assertTrue(result.containsKey("system.cpu.percent^sparkline"));

                    messageConsumer.unregister(tag -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams("testTopNTagWithSparkline");

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        var dataPoints = widgetContext.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonArray(VisualizationConstants.DATA_POINTS);

        for (var i = 0; i < dataPoints.size(); i++)
        {
            dataPoints.getJsonObject(i).getJsonArray(ENTITIES).add(ObjectConfigStore.getStore().getItemByIP("***********"));
        }

        setQueryId(widgetContext.getLong(ID), testContext, false);

        sparklineResultFLOAT32 = true;

        send(widgetContext, "testTopNTagWithSparklineFLOAT32");

        testContext.awaitCompletion(1, TimeUnit.SECONDS);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(150)
    void testDecodeHistogramGroupByMonitorResponse(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(2, TimeUnit.SECONDS);

        var widgetContext = TestConstants.prepareParams("testHistogramGroupByMonitor");

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                .put(VisualizationConstants.VISUALIZATION_DECODE_RESPONSE, YES)
                .put(EVENT_TYPE, EVENT_VISUALIZATION_QUERY_RESPONSE + ".test");

        decodeResponse(widgetContext.getLong(ID), testContext, JsonArray.of("monitor", METRIC, AGGREGATOR, VALUE, VisualizationConstants.TIMESTAMP));

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(151)
    void testDecodeHistogramGroupByTagResponse(VertxTestContext testContext, TestInfo testInfo)
    {
        var widgetContext = TestConstants.prepareParams("testHistogramGroupByTag");

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId())
                .put(VisualizationConstants.VISUALIZATION_DECODE_RESPONSE, YES)
                .put(EVENT_TYPE, EVENT_VISUALIZATION_QUERY_RESPONSE + ".test");

        decodeResponse(widgetContext.getLong(ID), testContext, JsonArray.of("tag", METRIC, AGGREGATOR, VALUE, TIMESTAMP));

        buildEntityKeys = false;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(152)
    void testAvailabilityMonitorByGaugeDrillDown(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    assertCacheTestResult(context, testInfo.getTestMethod().get().getName());

                    messageConsumer.unregister(test -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        var dataPoints = widgetContext.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonArray(VisualizationConstants.DATA_POINTS);

        for (var i = 0; i < dataPoints.size(); i++)
        {
            dataPoints.getJsonObject(i).getJsonArray(ENTITIES).add(ObjectConfigStore.getStore().getItemByIP("***********"));
        }

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(153)
    void testAvailabilityInstanceByGaugeDrillDown(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    assertCacheTestResult(context, testInfo.getTestMethod().get().getName());

                    messageConsumer.unregister(test -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        var dataPoints = widgetContext.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonArray(VisualizationConstants.DATA_POINTS);

        for (var i = 0; i < dataPoints.size(); i++)
        {
            dataPoints.getJsonObject(i).getJsonArray(ENTITIES).add(ObjectConfigStore.getStore().getItemByIP("**********"));
        }

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(154)
    void testShadowCounterQuery(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertEquals(1, result.getInteger(STATUS));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(APIConstants.Entity.OBJECT.getName().toLowerCase()));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("interface~traffic.bytes.per.sec"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("interface~traffic.bits.per.sec"));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        var dataPoints = widgetContext.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonArray(VisualizationConstants.DATA_POINTS);

        for (var i = 0; i < dataPoints.size(); i++)
        {
            dataPoints.getJsonObject(i).getJsonArray(ENTITIES).add(ObjectConfigStore.getStore().getItemByIP("**********"));
        }

        shadowCounterQuery = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(155)
    void testHCIInventory(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase("HCI Inventory Summary"))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals("HCI Inventory Summary", context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertEquals(1, result.getInteger(STATUS));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(APIConstants.Entity.OBJECT.getName().toLowerCase()));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("nutanix.cpu.percent^last"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("nutanix.memory.used.percent^last"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("total.vms^last"));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        hciInventory = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, "HCI Inventory Summary");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(156)
    void testAvailabilityInstanceFilterGroupTags(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertEquals(1, result.getInteger(STATUS));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("interface.uptime.percent"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("tag"));

                    assertEquals("instanceKey:value", result.getJsonArray(RESULT).getJsonObject(0).getString("tag"));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        instanceAvailabilityFilterGroup = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(157)
    void testAvailabilityInstanceFilterGroupTags2(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertEquals(1, result.getInteger(STATUS));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("interface.uptime.percent"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("tag"));

                    assertEquals("instanceKey:value", result.getJsonArray(RESULT).getJsonObject(0).getString("tag"));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        instanceAvailabilityFilterGroup = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(158)
    void testAvailabilityHeatmapInstanceFilterGroupTags(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertEquals(1, result.getInteger(STATUS));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("instance"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("status"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("tag"));

                    assertEquals("instancekey:value2", result.getJsonArray(RESULT).getJsonObject(0).getString("tag"));

                    assertEquals("Te1/1/3-38", result.getJsonArray(RESULT).getJsonObject(0).getString("instance"));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(159)
    void testAvailabilityHeatmapInstanceFilterGroupTags2(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertEquals(1, result.getInteger(STATUS));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("instance"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("status"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("tag"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("monitor"));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(160)
    void testAvailabilityMonitorFilterGroupTags(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertEquals(1, result.getInteger(STATUS));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("monitor.uptime.percent"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("tag"));

                    assertEquals("Development:8.0", result.getJsonArray(RESULT).getJsonObject(0).getString("tag"));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        monitorAvailabilityFilterGroup = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(161)
    void testAvailabilityMonitorFilterGroupTags2(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertEquals(1, result.getInteger(STATUS));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("monitor.uptime.percent"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("tag"));

                    assertEquals("Development:8.0", result.getJsonArray(RESULT).getJsonObject(0).getString("tag"));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        monitorAvailabilityFilterGroup = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(162)
    void testAvailabilityHeatmapInstanceFilterTags(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertEquals(1, result.getInteger(STATUS));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("instance"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("status"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("tag"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("monitor"));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        monitorAvailabilityFilterGroup = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(163)
    void testAvailabilityHeatmapInstanceFilterTags2(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertEquals(1, result.getInteger(STATUS));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("instance"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("status"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("tag"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("monitor"));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        monitorAvailabilityFilterGroup = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(164)
    void testAvailabilityHeatmapMonitorFilterTags(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertEquals(1, result.getInteger(STATUS));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("status"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("tag"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("monitor"));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        monitorAvailabilityFilterGroup = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(165)
    void testAvailabilityHeatmapMonitorFilterTags2(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertEquals(1, result.getInteger(STATUS));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("status"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("tag"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("monitor"));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        monitorAvailabilityFilterGroup = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(166)
    void testAvailabilityMonitorStatusFlapReport(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertEquals(1, result.getInteger(STATUS));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(DURATION));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("status.flap.history"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("monitor"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(VisualizationConstants.END_TIME));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(VisualizationConstants.START_TIME));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        var dataPoints = widgetContext.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonArray(VisualizationConstants.DATA_POINTS);

        for (var i = 0; i < dataPoints.size(); i++)
        {
            dataPoints.getJsonObject(i).getJsonArray(ENTITIES).add(ObjectConfigStore.getStore().getItemByIP("***********"));
        }

        statusflapReport = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(167)
    void testAvailabilityInstanceStatusFlapReport(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertEquals(1, result.getInteger(STATUS));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(DURATION));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("status.flap.history"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("monitor"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(VisualizationConstants.END_TIME));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(VisualizationConstants.START_TIME));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(INSTANCE));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        var dataPoints = widgetContext.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonArray(VisualizationConstants.DATA_POINTS);

        for (var i = 0; i < dataPoints.size(); i++)
        {
            dataPoints.getJsonObject(i).getJsonArray(ENTITIES).add(ObjectConfigStore.getStore().getItemByIP("***********"));
        }

        statusflapReport = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(168)
    void testAvailabilityInstanceFilterTags(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertEquals(1, result.getInteger(STATUS));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("interface.uptime.percent"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("tag"));

                    assertEquals("instancekey:value2", result.getJsonArray(RESULT).getJsonObject(0).getString("tag"));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        instanceAvailabilityFilterGroup = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(169)
    void testAvailabilityInstanceFilterTags2(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertEquals(1, result.getInteger(STATUS));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("interface.uptime.percent"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("tag"));

                    assertEquals("Development:8.0", result.getJsonArray(RESULT).getJsonObject(0).getString("tag"));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        instanceAvailabilityFilterGroup = true;

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(170)
    void testMapLogSophosGeoMapping(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertEquals(1, result.getInteger(STATUS));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("sophos.firewall.source.city"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("sophos.firewall.source.country"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("event.source.type"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("sophos.firewall.source.ip^count"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("sophos.firewall.destination.city"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("sophos.firewall.destination.country"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("sophos.firewall.destination.ip^count"));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE,
                new JsonObject()
                        .put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_EVENT_COLUMN.name())
                        .put(DatastoreConstants.MAPPER, DatastoreConstants.DataCategory.STRING.getName() + GlobalConstants.COLUMN_SEPARATOR + 600013 + GlobalConstants.COLUMN_SEPARATOR + "sophos.firewall.source.ip" + GlobalConstants.COLUMN_SEPARATOR + NO + COLUMN_SEPARATOR + "Sophos Firewall"));


        mapLogWidget = true;

        testContext.awaitCompletion(3, TimeUnit.SECONDS);

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(171)
    void testComplianceOverallRuleAssessmentResult(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertNotNull(result.getJsonArray(RESULT));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("current.scan.status"));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(172)
    void testComplianceOverallFailureBySeverity(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertNotNull(result.getJsonArray(RESULT));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("max"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("compliance.rule.id"));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(173)
    void testCompliancePolicyStats(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertNotNull(result.getJsonArray(RESULT));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("compliance.policy.id"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("compliance.percentage"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("vulnerable"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("poor"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("moderate"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("secure"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("last.scan.timestamp"));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(174)
    void testComplianceEntityStats(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertNotNull(result.getJsonArray(RESULT));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("compliance.policy.id"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("object.id"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("compliance.percentage"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("severity"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("last.scan.status"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("last.scan.timestamp"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("scanned.rule"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("message"));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("scanned.rule"));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        var policyId = CompliancePolicyConfigStore.getStore().getItems().getJsonObject(0).getLong(ID);

        widgetContext.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonObject(FILTERS).getJsonObject(DATA_FILTER).getJsonArray("groups").getJsonObject(0).getJsonArray(CONDITIONS).getJsonObject(0).put(VALUE, policyId);

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(175)
    void testComplianceTrail(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(ComplianceConstants.LAST_SCAN_STATUS));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(ComplianceConstants.CURRENT_SCAN_STATUS));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(ComplianceConstants.COMPLIANCE_RULE_ID));

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(ComplianceRule.COMPLIANCE_RULE_SEVERITY));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        var items = CompliancePolicyConfigStore.getStore().getItems();

        var policyId = 0L;

        for (var i = 0; i < items.size(); i++)
        {

            var item = items.getJsonObject(i);

            if (item.getString(CompliancePolicy.COMPLIANCE_POLICY_NAME).equalsIgnoreCase("112 Policy"))
            {
                policyId = item.getLong(ID);

                break;
            }
        }

        var conditions = widgetContext.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonObject(FILTERS).getJsonObject(DATA_FILTER).getJsonArray("groups").getJsonObject(0).getJsonArray(CONDITIONS);

        conditions.getJsonObject(0).put(VALUE, policyId);

        conditions.getJsonObject(1).put(VALUE, ObjectConfigStore.getStore().getItemByIP("***********"));

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(177)
    void testRuleAssessmentResultPassedDrilldownLevelFirst(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertNotNull(result.getJsonArray(RESULT));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("count"));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(178)
    void testRuleAssessmentResultPassedDrilldownLevelSecond(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertNotNull(result.getJsonArray(RESULT));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(OBJECT_ID));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        var ruleId = ComplianceRuleConfigStore.getStore().getItemByValue(ComplianceRule.COMPLIANCE_RULE_NAME, "Test Version 15.2(4)M7").getLong(ID);

        widgetContext.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonObject(FILTERS).getJsonObject(DATA_FILTER).getJsonArray("groups").getJsonObject(0).getJsonArray(CONDITIONS).getJsonObject(0).put(VALUE, ruleId);

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(179)
    void testRuleAssessmentResultFailedDrilldownLevelSecond(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertNotNull(result.getJsonArray(RESULT));

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(OBJECT_ID));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        var ruleId = ComplianceRuleConfigStore.getStore().getItemByValue(ComplianceRule.COMPLIANCE_RULE_NAME, "Rule Name 3").getLong(ID);

        widgetContext.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonObject(FILTERS).getJsonObject(DATA_FILTER).getJsonArray("groups").getJsonObject(0).getJsonArray(CONDITIONS).getJsonObject(2).put(VALUE, ruleId);

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(176)
    void testLogStreamWidget(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                if (context.getString(VisualizationConstants.VISUALIZATION_NAME).equalsIgnoreCase(testInfo.getTestMethod().get().getName()))
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": Result: " + context);

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

                    Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

                    assertEquals(testInfo.getTestMethod().get().getName(), context.getString(VisualizationConstants.VISUALIZATION_NAME));

                    var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

                    assertFalse(result.getJsonArray(RESULT).isEmpty());

                    assertEquals("Test policy Log", result.getJsonArray(RESULT).getJsonObject(0).getString(PolicyEngineConstants.POLICY_NAME));

                    assertEquals("MAJOR", result.getJsonArray(RESULT).getJsonObject(0).getString(SEVERITY));

                    messageConsumer.unregister(asyncResult -> testContext.completeNow());
                }
            }
        });

        var context = new JsonObject("{ \"policy.name\": \"Test policy Log\", \"policy.type\": \"Log\", \"policy.tags\": [ \"Default\" ], \"policy.scheduled\": \"no\", \"policy.context\": { \"entity.type\": \"event.source.type\", \"entities\": [ \"Linux\" ], \"data.point\": \"message\", \"aggregator\": \"count\", \"operator\": \">=\", \"value\": 1, \"trigger.mode\": \"individual\", \"evaluation.window\": 5, \"evaluation.window.unit\": \"minute\", \"evaluation.frequency\": 10, \"evaluation.frequency.unit\": \"second\", \"policy.severity\": \"WARNING\", \"policy.result.by\": [], \"policy.trigger.occurrences\": 1, \"policy.auto.clear.timer.seconds\": 0, \"filters\": { \"data.filter\": {} } }, \"policy.email.notification.recipients\": [], \"policy.renotify\": \"yes\", \"policy.monitor.polling.failed.notification.timer.seconds\": 0, \"policy.monitor.polling.failed.notification.status\": \"no\", \"policy.renotification.timer.seconds\": 0, \"policy.actions\": {}, \"policy.suppress.action\": \"no\", \"policy.archived\": \"no\", \"policy.creation.time\": 1685353790, \"policy.state\": \"yes\", \"_type\": \"1\" }");

        TestAPIUtil.post(EVENT_POLICY_API_ENDPOINT, context, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    EventPolicyCacheStore.getStore().updateTriggerTicks(response.bodyAsJsonObject().getLong(ID), DateTimeUtil.currentMilliSeconds());

                    var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

                    widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

                    setQueryId(widgetContext.getLong(ID), testContext, false);

                    streamLogWidget = true;

                    send(widgetContext, testInfo.getTestMethod().get().getName());
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(177)
    void testNetRouteMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        var item = NetRouteConfigStore.getStore().getItemByValue(NETROUTE_DESTINATION, "8.8.8.8");

        Assertions.assertNotNull(item);

        var visualizationDataSources = new JsonArray();

        try
        {
            var dataSources = new JsonArray().addAll((JsonArray) widgetContext.remove(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

            for (var index = 0; index < dataSources.size(); index++)
            {
                var visualizationDataSource = dataSources.getJsonObject(index);

                if (VisualizationConstants.VisualizationDataSource.valueOfName(visualizationDataSource.getString(VisualizationConstants.TYPE)) == VisualizationConstants.VisualizationDataSource.NETROUTE_METRIC)
                {
                    var visualizationDataDataPoints = new JsonArray();

                    var dataPoints = visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS);

                    for (var i = 0; i < dataPoints.size(); i++)
                    {
                        var visualizationDataDataPoint = dataPoints.getJsonObject(i);

                        visualizationDataDataPoint.put(ENTITIES, new JsonArray().add(item.getLong(ID)));

                        visualizationDataDataPoints.add(visualizationDataDataPoint);
                    }

                    visualizationDataSource.put(VisualizationConstants.DATA_POINTS, visualizationDataDataPoints);

                    visualizationDataSources.add(visualizationDataSource);
                }
            }

            widgetContext.put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, visualizationDataSources).put(APIConstants.SESSION_ID, TestUtil.getSessionId());

            buildEntityKeys = false;
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(178)
    void testNetRouteEvent(VertxTestContext testContext, TestInfo testInfo)
    {
        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        var item = NetRouteConfigStore.getStore().getItemByValue(NETROUTE_DESTINATION, "8.8.8.8");

        Assertions.assertNotNull(item);

        var visualizationDataSources = new JsonArray();

        try
        {
            var dataSources = new JsonArray().addAll((JsonArray) widgetContext.remove(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

            for (var index = 0; index < dataSources.size(); index++)
            {
                var visualizationDataSource = dataSources.getJsonObject(index);

                var visualizationDataDataPoints = new JsonArray();

                var dataPoints = visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS);

                for (var i = 0; i < dataPoints.size(); i++)
                {
                    var visualizationDataDataPoint = dataPoints.getJsonObject(i);

                    visualizationDataDataPoint.put(ENTITIES, new JsonArray().add(item.getLong(ID)));

                    visualizationDataDataPoint.put(ENTITY_KEYS, DateTimeUtil.currentSeconds() + CARET_SEPARATOR + item.getLong(ID) + CARET_SEPARATOR + NetRouteConstants.NETROUTE_EVENT_COLUMN);

                    visualizationDataDataPoints.add(visualizationDataDataPoint);
                }

                visualizationDataSource.put(VisualizationConstants.DATA_POINTS, visualizationDataDataPoints);

                visualizationDataSources.add(visualizationDataSource);
            }

            widgetContext.put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, visualizationDataSources).put(APIConstants.SESSION_ID, TestUtil.getSessionId());

            buildEntityKeys = false;
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(179)
    void testNetRouteMetricPolicyHistogram(VertxTestContext testContext, TestInfo testInfo)
    {
        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        var item = NetRouteConfigStore.getStore().getItemByValue(NETROUTE_DESTINATION, "8.8.8.8");

        Assertions.assertNotNull(item);

        var visualizationDataSources = new JsonArray();

        try
        {
            var dataSources = new JsonArray().addAll((JsonArray) widgetContext.remove(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

            for (var index = 0; index < dataSources.size(); index++)
            {
                var visualizationDataSource = dataSources.getJsonObject(index);

                var visualizationDataDataPoints = new JsonArray();

                var dataPoints = visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS);

                for (var i = 0; i < dataPoints.size(); i++)
                {
                    var visualizationDataDataPoint = dataPoints.getJsonObject(i);

                    visualizationDataDataPoint.put(ENTITIES, new JsonArray().add(item.getLong(ID)));

                    visualizationDataDataPoints.add(visualizationDataDataPoint);
                }

                visualizationDataSource.put(VisualizationConstants.DATA_POINTS, visualizationDataDataPoints);

                visualizationDataSources.add(visualizationDataSource);
            }

            widgetContext.put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, visualizationDataSources).put(APIConstants.SESSION_ID, TestUtil.getSessionId());

            buildEntityKeys = false;
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(180)
    void testNetRouteMetricPolicyStream(VertxTestContext testContext, TestInfo testInfo)
    {
        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        var item = NetRouteConfigStore.getStore().getItemByValue(NETROUTE_DESTINATION, "8.8.8.8");

        Assertions.assertNotNull(item);

        var visualizationDataSources = new JsonArray();

        try
        {
            var dataSources = new JsonArray().addAll((JsonArray) widgetContext.remove(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

            for (var index = 0; index < dataSources.size(); index++)
            {
                var visualizationDataSource = dataSources.getJsonObject(index);

                var visualizationDataDataPoints = new JsonArray();

                var dataPoints = visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS);

                for (var i = 0; i < dataPoints.size(); i++)
                {
                    var visualizationDataDataPoint = dataPoints.getJsonObject(i);

                    visualizationDataDataPoint.put(ENTITIES, new JsonArray().add(item.getLong(ID)));

                    visualizationDataDataPoints.add(visualizationDataDataPoint);
                }

                visualizationDataSource.put(VisualizationConstants.DATA_POINTS, visualizationDataDataPoints);

                visualizationDataSources.add(visualizationDataSource);
            }

            widgetContext.put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, visualizationDataSources).put(APIConstants.SESSION_ID, TestUtil.getSessionId());

            buildEntityKeys = false;
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(181)
    void testNetRouteMetricPolicyFlapWithFilter(VertxTestContext testContext, TestInfo testInfo)
    {
        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        var item = NetRouteConfigStore.getStore().getItemByValue(NETROUTE_DESTINATION, "8.8.8.8");

        Assertions.assertNotNull(item);

        var visualizationDataSources = new JsonArray();

        try
        {
            var dataSources = new JsonArray().addAll((JsonArray) widgetContext.remove(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

            var filters = dataSources.getJsonObject(0).getJsonObject(FILTERS);

            filters.getJsonObject(DATA_FILTER).getJsonArray(CONDITION_GROUPS)
                    .getJsonObject(0).getJsonArray(CONDITIONS).getJsonObject(0).put(VALUE, item.getLong(ID));

            for (var index = 0; index < dataSources.size(); index++)
            {
                var visualizationDataSource = dataSources.getJsonObject(index);

                var visualizationDataDataPoints = new JsonArray();

                var dataPoints = visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS);

                for (var i = 0; i < dataPoints.size(); i++)
                {
                    var visualizationDataDataPoint = dataPoints.getJsonObject(i);

                    visualizationDataDataPoint.put(ENTITIES, new JsonArray().add(item.getLong(ID)));

                    visualizationDataDataPoints.add(visualizationDataDataPoint);
                }

                visualizationDataSource.put(VisualizationConstants.DATA_POINTS, visualizationDataDataPoints);

                visualizationDataSources.add(visualizationDataSource);
            }

            widgetContext.put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, visualizationDataSources).put(APIConstants.SESSION_ID, TestUtil.getSessionId());

            buildEntityKeys = false;
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(182)
    void testNetRouteEventPolicyHistogram(VertxTestContext testContext, TestInfo testInfo)
    {
        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        var item = NetRouteConfigStore.getStore().getItemByValue(NETROUTE_DESTINATION, "8.8.8.8");

        Assertions.assertNotNull(item);

        var visualizationDataSources = new JsonArray();

        try
        {
            var dataSources = new JsonArray().addAll((JsonArray) widgetContext.remove(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

            for (var index = 0; index < dataSources.size(); index++)
            {
                var visualizationDataSource = dataSources.getJsonObject(index);

                var visualizationDataDataPoints = new JsonArray();

                var dataPoints = visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS);

                for (var i = 0; i < dataPoints.size(); i++)
                {
                    var visualizationDataDataPoint = dataPoints.getJsonObject(i);

                    visualizationDataDataPoint.put(ENTITIES, new JsonArray().add(item.getLong(ID)));

                    visualizationDataDataPoints.add(visualizationDataDataPoint);
                }

                visualizationDataSource.put(VisualizationConstants.DATA_POINTS, visualizationDataDataPoints);

                visualizationDataSources.add(visualizationDataSource);
            }

            widgetContext.put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, visualizationDataSources).put(APIConstants.SESSION_ID, TestUtil.getSessionId());

            buildEntityKeys = false;
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(183)
    void testNetRouteEventPolicyStream(VertxTestContext testContext, TestInfo testInfo)
    {
        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        eventQuery = true;

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        var item = NetRouteConfigStore.getStore().getItemByValue(NETROUTE_DESTINATION, "8.8.8.8");

        Assertions.assertNotNull(item);

        var visualizationDataSources = new JsonArray();

        try
        {
            var dataSources = new JsonArray().addAll((JsonArray) widgetContext.remove(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

            for (var index = 0; index < dataSources.size(); index++)
            {
                var visualizationDataSource = dataSources.getJsonObject(index);

                var visualizationDataDataPoints = new JsonArray();

                var dataPoints = visualizationDataSource.getJsonArray(VisualizationConstants.DATA_POINTS);

                for (var i = 0; i < dataPoints.size(); i++)
                {
                    var visualizationDataDataPoint = dataPoints.getJsonObject(i);

                    visualizationDataDataPoint.put(ENTITIES, new JsonArray().add(item.getLong(ID)));

                    visualizationDataDataPoints.add(visualizationDataDataPoint);
                }

                visualizationDataSource.put(VisualizationConstants.DATA_POINTS, visualizationDataDataPoints);

                visualizationDataSources.add(visualizationDataSource);
            }

            widgetContext.put(VisualizationConstants.VISUALIZATION_DATA_SOURCES, visualizationDataSources).put(APIConstants.SESSION_ID, TestUtil.getSessionId());

            buildEntityKeys = false;
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        setQueryId(widgetContext.getLong(ID), testContext, true);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(184)
    void testMetricExplorerRaw(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EVENT_TYPE) && message.body().getString(EVENT_TYPE).equalsIgnoreCase(UI_ACTION_VISUALIZATION_RENDER) && message.body().getJsonObject(EVENT_CONTEXT) != null)
            {
                var context = message.body().getJsonObject(EVENT_CONTEXT);

                var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary("result")), null, false, null, false, true);

                LOGGER.info(testInfo.getTestMethod().get().getName() + ": result: " + result);

                assertNotNull(result.getJsonArray(RESULT));

                assertFalse(result.getJsonArray(RESULT).isEmpty());

                assertFalse(result.getJsonArray(RESULT).getJsonObject(0).containsKey(OBJECT_ID));

                testContext.completeNow();
            }
        });

        var widgetContext = TestConstants.prepareParams(testInfo.getTestMethod().get().getName());

        var id = ObjectConfigStore.getStore().getItemByIP("***********");

        Assertions.assertNotNull(id);

        widgetContext.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0).getJsonArray(VisualizationConstants.DATA_POINTS).getJsonObject(0).getJsonArray(ENTITIES).add(id);

        widgetContext.put(APIConstants.SESSION_ID, TestUtil.getSessionId());

        setQueryId(widgetContext.getLong(ID), testContext, false);

        send(widgetContext, testInfo.getTestMethod().get().getName());
    }

    private void assertCacheTestResult(JsonObject context, String visualizationName)
    {
        Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_DATA_SOURCES));

        Assertions.assertNotNull(context.getValue(VisualizationConstants.VISUALIZATION_PROPERTIES));

        assertEquals(visualizationName, context.getString(VisualizationConstants.VISUALIZATION_NAME));

        var result = VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), LOGGER, false, null, false, true);

        LOGGER.info(visualizationName + ": Result->result: " + result.encodePrettily());

        switch (visualizationName)
        {
            case "Startup-Running Conflict Summary", "Device Summary":

                assertEquals(2, result.getJsonArray(RESULT).size());

                break;

            case "Startup-Running Conflict Overview":

                assertEquals(0, result.getJsonArray(RESULT).getJsonObject(0).getInteger("Not Applicable"));

                assertEquals(2, result.getJsonArray(RESULT).getJsonObject(0).getInteger("Conflict Detected"));

                assertEquals(0, result.getJsonArray(RESULT).getJsonObject(0).getInteger("In Sync"));

                break;

            case "Baseline-Running Conflict Summary":

                assertEquals(String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, visualizationName, ErrorMessageConstants.NO_ENTITY_QUALIFIED), result.getString(ERROR));

                break;

            case "Baseline-Running Conflict Overview":

                assertEquals(2, result.getJsonArray(RESULT).getJsonObject(0).getInteger("Not Applicable"));

                assertEquals(0, result.getJsonArray(RESULT).getJsonObject(0).getInteger("Conflict Detected"));

                break;

            case "Failed Backup Summary":

                assertEquals(String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, visualizationName, ErrorMessageConstants.NO_ENTITY_QUALIFIED), VisualizationConstants.unpack(Buffer.buffer(context.getBinary(RESULT)), null, false, null, false, true).getString(ERROR));

                break;

            case "Backup Summary":

                assertEquals(2, result.getJsonArray(RESULT).getJsonObject(0).getInteger(STATUS_SUCCEED));

                assertEquals(0, result.getJsonArray(RESULT).getJsonObject(0).getInteger(STATUS_FAIL));

                break;

            case "Device Overview":

                assertEquals(2, result.getJsonArray(RESULT).getJsonObject(0).getInteger(NMSConstants.Type.SWITCH.getName()));

                break;


            case "testAvailabilityHeatmapVM", "testAvailabilityHeatmapApps", "testAvailabilityHeatmapAccessPoint",
                 "testMetricEntityKeyInstances":

                assertEquals(1, result.getInteger(STATUS));

                assertFalse(result.getJsonArray(RESULT).isEmpty());

                assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(APIConstants.Entity.OBJECT.getName().toLowerCase()));

                assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(STATUS));

                break;

            case "testMetricPolicyHeatmap", "testMetricPolicyHeatmapFilterByTag":

                assertEquals(1, result.getInteger(STATUS));

                assertFalse(result.getJsonArray(RESULT).isEmpty());

                assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(APIConstants.Entity.OBJECT.getName().toLowerCase()));

                assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(SEVERITY));

                break;

            case "testAvailabilityHeatmapAccessPointWithTagInstances", "testAvailabilityHeatmapVMsWithTag":

                assertEquals(1, result.getInteger(STATUS));

                assertFalse(result.getJsonArray(RESULT).isEmpty());

                assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(TAGS));

                assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(STATUS));

                assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(INSTANCE));

                assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(APIConstants.Entity.OBJECT.getName().toLowerCase()));

                break;

            case "testVisualizeReportWithNoApplication":

                assertEquals(0, result.getLong("rows"));

                assertTrue(result.containsKey(ERROR) && result.getString(ERROR).contains("No entity qualified"));

                break;

            case "testAvailabilityHeatmapInstanceResultByMonitor":

                assertEquals(1, result.getInteger(STATUS));

                assertFalse(result.getJsonArray(RESULT).isEmpty());

                assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(STATUS));

                assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(INSTANCE));

                assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(APIConstants.Entity.OBJECT.getName().toLowerCase()));

                break;

            case "testNetworkGridInDashboard":

                assertEquals(1, result.getInteger(STATUS));

                assertFalse(result.getJsonArray(RESULT).isEmpty());

                break;

            case "Branch Link Utilization":

                assertEquals(1, result.getInteger(STATUS));

                assertFalse(result.getJsonArray(RESULT).isEmpty());

                var expectedResult = "[{\"monitor\":\"1\",\"interface\":\"G 1/0\",\"interface~uptime.percent^avg\":75.0,\"interface~ip.address^last\":\"***********\",\"interface~traffic.utilization.percent^avg\":75.0,\"interface~traffic.utilization.percent^min\":25.0,\"interface~traffic.utilization.percent^max\":85.0},{\"monitor\":\"2\",\"interface\":\"G 1/1\",\"interface~uptime.percent^avg\":25.0,\"interface~ip.address^last\":\"***********\",\"interface~traffic.utilization.percent^avg\":75.0,\"interface~traffic.utilization.percent^min\":25.0,\"interface~traffic.utilization.percent^max\":85.0}]";

                assertEquals(expectedResult, result.getJsonArray(RESULT).toString());

                break;

            case "testConfigActionHistory":

                assertEquals(1, result.getInteger(STATUS));

                assertFalse(result.getJsonArray(RESULT).isEmpty());

                assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey("config.operation") && result.getJsonArray(RESULT).getJsonObject(0).getString("config.operation").contains("backup"));

                break;

            case "testAvailabilityMonitorByGaugeDrillDown":

                assertEquals(1, result.getInteger(STATUS));

                assertFalse(result.getJsonArray(RESULT).isEmpty());

                assertEquals("***********", result.getJsonArray(RESULT).getJsonObject(0).getString(OBJECT_IP));

                assertEquals("Router", result.getJsonArray(RESULT).getJsonObject(0).getString(OBJECT_TYPE));

                break;

            case "testAvailabilityInstanceByGaugeDrillDown":

                assertEquals(1, result.getInteger(STATUS));

                assertFalse(result.getJsonArray(RESULT).isEmpty());

                assertEquals(22, result.getJsonArray(RESULT).size());

                assertEquals("**********", result.getJsonArray(RESULT).getJsonObject(0).getString(OBJECT_IP));

                assertTrue(result.getJsonArray(RESULT).getJsonObject(0).containsKey(INSTANCE));

                break;
        }
    }

    public void setQueryId(long id, VertxTestContext testContext, boolean completeTest)
    {
        try
        {
            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EventBusConstants.EVENT_VISUALIZATION_TEST, message ->
            {
                try
                {
                    if (completeTest)
                    {
                        testContext.completeNow();
                    }
                    else
                    {

                        message.body().getMap().forEach((key, value) ->
                        {
                            try
                            {
                                if (value instanceof Long && id == CommonUtil.getLong(key))
                                {
                                    LOGGER.debug("query id received for widget::" + key + " with query Id::" + CommonUtil.getLong(value));

                                    queryId = CommonUtil.getLong(value);

                                    QUERY_CONTEXT.put(CommonUtil.getLong(value), testContext);
                                }
                            }
                            catch (Exception e)
                            {
                                LOGGER.error(e);
                                LOGGER.debug("Inside SetQueryId : " + message.body());
                            }
                        });
                    }
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister();

                    testContext.failNow(exception);
                }
            });
        }
        catch (Exception exception)
        {
            messageConsumer.unregister();

            testContext.failNow(exception);
        }
    }

    private void decodeResponse(long id, VertxTestContext testContext, JsonArray columns)
    {
        consumer = TestUtil.vertx().eventBus().localConsumer(EVENT_VISUALIZATION_QUERY_RESPONSE + ".test", message ->
        {
            var event = message.body();

            LOGGER.info(String.format("Decoded response : %s of id : %s ", event.encode(), id));

            assertTrue(event.containsKey(RESULT));

            assertFalse(event.getJsonObject(RESULT).isEmpty());

            var rows = event.getJsonObject(RESULT).getJsonArray(RESULT);

            LOGGER.info(String.format("fields : %s ", columns.encode()));

            assertFalse(rows.isEmpty());

            for (var column : columns)
            {
                for (var index = 0; index < rows.size(); index++)
                {
                    assertTrue(rows.getJsonObject(index).containsKey(column.toString()));
                }
            }

            consumer.unregister(result -> testContext.completeNow());
        });
    }

    private void send(JsonObject context, String name)
    {
        testCase = name;

        if (name.contains("EventHistory") && !name.contains("Flow"))
        {
            context.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES).getJsonObject(0).put(VisualizationConstants.CATEGORY, VisualizationConstants.VisualizationDataSource.LOG.getName());
        }

        Bootstrap.vertx().eventBus().send(VISUALIZATION_TEST, context.put(VisualizationConstants.VISUALIZATION_NAME, name));
    }

}
