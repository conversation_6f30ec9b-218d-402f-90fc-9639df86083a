[{"_type": "0", "id": 10000000002158, "visualization.name": "Running Instances", "visualization.description": "Azure PostgreSQL Running Instances", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"header": {"title": "Running Instances", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": ""}]}, "style": {"icon": {"name": "running-instance"}, "color.data.point": ""}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002159, "visualization.name": "Connections", "visualization.description": "Azure PostgreSQL Connections", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.postgresql.server.active.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.postgresql.server.failed.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.postgresql.server.active.connections", "icon": {"name": "active-connections", "placement": "prefix"}}, "header": {"title": "Active Connections", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Failed", "value": "azure.postgresql.server.failed.connections.avg"}, {"label": "Active", "value": "azure.postgresql.server.active.connections.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002160, "visualization.name": "Highest Disk Latency", "visualization.description": "Azure PostgreSQL Highest Disk Latency", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Highest Disk Latency", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": ""}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002161, "visualization.name": "Disk IOPS", "visualization.description": "Azure PostgreSQL Disk IOPS", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "", "icon": {"name": "rtt", "placement": "prefix"}}, "header": {"title": "Disk IOPS", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": ""}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002162, "visualization.name": "CPU Utilization", "visualization.description": "Azure PostgreSQL CPU Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.postgresql.server.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.postgresql.server.cpu.percent", "icon": {"name": "cpu", "placement": "prefix"}}, "header": {"title": "CPU Utilization", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.postgresql.server.cpu.percent.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002163, "visualization.name": "Memory Percent", "visualization.description": "Azure PostgreSQL Memory Percent", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.postgresql.server.memory.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.postgresql.server.memory.percent", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "Memory Percent", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.postgresql.server.memory.percent.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002164, "visualization.name": "Top Instances With Highest CPU Utilization", "visualization.description": "Azure PostgreSQL Top Instances With Highest CPU Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.postgresql.server.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.postgresql.server.cpu.percent.avg"}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002165, "visualization.name": "Top Instances With Highest Memory Consumption", "visualization.description": "Azure PostgreSQL Top Instances With Highest Memory Consumption", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.postgresql.server.memory.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.postgresql.server.memory.percent.avg"}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002166, "visualization.name": "Top Instances With Highest IO Consumption", "visualization.description": "Azure PostgreSQL Top Instances With Highest IO Consumption", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": ""}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002167, "visualization.name": "Top Instances With Active Connections", "visualization.description": "Azure PostgreSQL Top Instances With Active Connections", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.postgresql.server.active.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.postgresql.server.active.connections.avg"}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002168, "visualization.name": "Top Instances With Failed Connections", "visualization.description": "Azure PostgreSQL Top Instances With Failed Connections", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.postgresql.server.failed.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.postgresql.server.failed.connections.avg"}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002169, "visualization.name": "Top Database By Network IN", "visualization.description": "Azure PostgreSQL Top Database By Network IN", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.postgresql.server.network.in.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.postgresql.server.network.in.bytes.avg"}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002170, "visualization.name": "Top Database By Network OUT", "visualization.description": "Azure PostgreSQL Top Database By Network OUT", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.postgresql.server.network.out.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.postgresql.server.network.out.bytes.avg"}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000002171, "visualization.name": "Top Expensive Queries", "visualization.description": "Azure PostgreSQL Top Expensive Queries", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "", "title": "Query Text", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "", "title": "Start Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "", "title": "End Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "", "title": "Execution Time(ms)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "", "title": "Execution Count", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "", "title": "DB Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}]}}, "container.type": "dashboard"}]