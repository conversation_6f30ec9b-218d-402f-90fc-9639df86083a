[{"visualization.name": "Infrastructure Heatmap", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "HeatMap", "visualization.data.sources": [{"type": "policy", "category": "metric", "visualization.result.by": ["severity"], "filters": {"data.filter": {}}, "data.points": [{"data.point": "severity", "aggregator": "count"}]}], "visualization.properties": {}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 287585494747}, {"visualization.name": "<PERSON><PERSON>", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Gauge", "visualization.type": "MetroTile", "visualization.data.sources": [{"type": "policy", "category": "metric", "visualization.result.by": ["severity"], "filters": {"data.filter": {}, "result.filter": {}}, "severity": ["DOWN", "CRITICAL", "MAJOR", "WARNING"], "data.points": [{"data.point": "severity", "aggregator": "count"}]}], "visualization.properties": {"gauge": {"style": {"layout": "Radial View", "chart.legend": "no", "chart.label": "no", "type": "number", "font.size": "small", "text.align": "left", "color.conditions": [{"color": "#f04e3e", "value": 0}, {"color": "#f58518", "value": 0}, {"color": "#f5bc18", "value": 0}]}}}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 287585494748}, {"visualization.name": "Top Network Monitors by <PERSON><PERSON>", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Pie", "visualization.data.sources": [{"type": "policy", "category": "metric", "visualization.result.by": ["object.id"], "filters": {"data.filter": {}, "result.filter": {}}, "severity": ["DOWN", "CRITICAL", "MAJOR", "WARNING"], "data.points": [{"data.point": "severity", "aggregator": "count", "entity.type": "Group", "entities": [10000000000002]}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "vertical.legend": "no", "line.width": 1, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "10", "order": "desc", "column": "severity.count"}}}, "visualization.result.by": ["object.id"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 287585494749}, {"visualization.name": "Top Server Monitors by Alert count", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Pie", "visualization.data.sources": [{"type": "policy", "category": "metric", "visualization.result.by": ["object.id"], "filters": {"data.filter": {}, "result.filter": {}}, "severity": ["DOWN", "CRITICAL", "MAJOR", "WARNING"], "data.points": [{"data.point": "severity", "aggregator": "count", "entity.type": "Group", "entities": [10000000000017]}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "10", "order": "desc", "column": "severity.count"}}}, "visualization.result.by": ["object.id"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 287585494750}, {"visualization.name": "Top Cloud Monitors by Alert count", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Pie", "visualization.data.sources": [{"type": "policy", "category": "metric", "visualization.result.by": ["object.id"], "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "severity", "aggregator": "count", "entity.type": "Group", "entities": [10000000000013]}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "10", "order": "desc", "column": "severity.count"}}}, "visualization.result.by": ["object.id"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 96435113520220}, {"visualization.name": "Top Policy by <PERSON><PERSON>", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "HorizontalBar", "visualization.data.sources": [{"type": "policy", "category": "metric", "visualization.result.by": ["policy.id"], "filters": {"data.filter": {}}, "data.points": [{"data.point": "severity", "aggregator": "count"}]}], "visualization.properties": {"chart": {"chart.legend": "no", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "severity.count"}}}, "visualization.result.by": ["policy.id"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 96435113520222}, {"visualization.name": "Top Monitor by <PERSON><PERSON>", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "HorizontalBar", "visualization.data.sources": [{"type": "policy", "category": "metric", "visualization.result.by": ["object.id"], "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "severity", "aggregator": "count"}]}], "visualization.properties": {"chart": {"chart.legend": "no", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "10", "order": "desc", "column": "severity.count"}}}, "visualization.result.by": ["object.id"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 96435113520223}, {"visualization.name": "Alert <PERSON>", "category": "metric", "visualization.timeline": {"relative.timeline": "last.week", "visualization.time.range.inclusive": "no"}, "visualization.category": "Stream", "visualization.type": "Grid", "visualization.data.sources": [{"type": "policy.flap", "category": "metric", "join.type": "any", "visualization.result.by": [], "filters": {"data.filter": {}, "result.filter": {}}, "severity": ["DOWN", "CRITICAL", "MAJOR", "WARNING"], "data.points": [{"data.point": "duration", "aggregator": ""}, {"data.point": "object.id", "aggregator": ""}, {"data.point": "instance", "aggregator": ""}, {"data.point": "severity", "aggregator": ""}, {"data.point": "policy.id", "aggregator": ""}, {"data.point": "policy.type", "aggregator": ""}, {"data.point": "metric", "aggregator": ""}, {"data.point": "value", "aggregator": ""}]}, {"type": "policy.trigger.tick", "category": "metric", "visualization.result.by": [], "filters": {"data.filter": {}}, "data.points": [{"data.point": "instance", "aggregator": "last"}, {"data.point": "policy.id", "aggregator": "last"}, {"data.point": "policy.first.trigger.tick", "aggregator": "last"}, {"data.point": "object.id", "aggregator": "last"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "duration", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "object.id", "title": "Monitor", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "policy.name", "title": "Policy", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "policy.type", "title": "Policy Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "metric", "title": "Metric", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "instance", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "severity", "title": "Severity", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "policy.id", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}, {"name": "value", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {}}, {"name": "policy.first.trigger.tick", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10, "style": {}}, {"name": "duration.time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11, "style": {}}, {"name": "timestamp", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12, "style": {}}, {"name": "object.type", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 13, "style": {}}, {"name": "object.vendor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 14, "style": {}}]}}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}, "join.alias": true, "join.type": "any", "join.columns": ["policy.id", "instance", "object.id"], "join.result": "policy.stream", "join.replacable.columns": {"policy.id": "policy.id^value", "instance": "instance^value", "object.id": "object.id^value"}, "container.type": "dashboard", "_type": "0", "id": 96435113520224}, {"visualization.name": "Monitor Availability", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Gauge", "visualization.type": "MetroTile", "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.up.count", "aggregator": "avg"}, {"data.point": "monitor.down.count", "aggregator": "avg"}, {"data.point": "monitor.unreachable.count", "aggregator": "avg"}, {"data.point": "monitor.maintenance.count", "aggregator": "avg"}]}], "visualization.properties": {"gauge": {"style": {"layout": "Radial View", "chart.legend": "no", "chart.label": "no", "type": "number", "font.size": "small", "text.align": "left", "color.conditions": [{"color": "#f04e3e"}, {"color": "#f58518"}, {"color": "#f5bc18"}]}}}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 342085551631000}, {"visualization.name": "Top Virtualization Monitors by Alert count", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Pie", "visualization.data.sources": [{"type": "policy", "category": "metric", "visualization.result.by": ["object.id"], "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "severity", "aggregator": "count", "entity.type": "Group", "entities": [10000000000026]}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "10", "order": "desc", "column": "severity.count"}}}, "visualization.result.by": ["object.id"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 342085551631001}, {"visualization.name": "Top Wireless Monitors by Alert count", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Pie", "visualization.data.sources": [{"type": "policy", "category": "metric", "visualization.result.by": ["object.id"], "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "severity", "aggregator": "count", "entity.type": "Group", "entities": [10000000000020]}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": "10", "order": "desc", "column": "severity.count"}}}, "visualization.result.by": ["object.id"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 342085551631002}]