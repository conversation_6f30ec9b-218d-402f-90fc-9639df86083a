[{"visualization.name": "AWS EBS Write bytes per second", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Chart", "visualization.time.range.inclusive": "no", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "aws.ebs.volume.write.bytes.per.sec", "aggregator": "avg"}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "no", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc"}}}, "visualization.result.by": ["monitor"], "granularity": {"value": 5, "unit": "m"}, "_type": "0", "id": 1820764169243713, "container.type": "dashboard"}, {"visualization.name": "Top EC2 Instance by CPU Usage", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.time.range.inclusive": "no", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "aws.ec2.cpu.percent", "aggregator": "avg"}, {"data.point": "aws.ec2.cpu.percent", "aggregator": "sparkline"}]}], "visualization.properties": {"sparkline": "yes", "grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "title": "Monitor", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "aws.ec2.cpu.percent.avg", "title": "CPU Percent", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "object.type", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "object.vendor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "aws.ec2.cpu.percent.sparkline", "title": "Sparkline", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "type": "sparkline", "style": {"inline.chart": {"type": "sparkarea", "color": "#8085E9"}}}], "sorting": {"limit": 10, "order": "desc", "column": "aws.ec2.cpu.percent.avg"}}}, "visualization.result.by": ["monitor"], "granularity": {"value": 5, "unit": "m"}, "_type": "0", "id": 1820764169243714, "container.type": "dashboard"}, {"visualization.name": "Top AWS EC2 by Network bytes per sec.", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Chart", "visualization.time.range.inclusive": "no", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "aws.ec2.network.bytes.per.sec", "aggregator": "avg"}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "no", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc"}}}, "visualization.result.by": ["monitor"], "granularity": {"value": 5, "unit": "m"}, "_type": "0", "id": 1820764169243715, "container.type": "dashboard"}, {"visualization.name": "Top AWS S3 Bucket by size", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.time.range.inclusive": "no", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "aws.s3.bucket.bytes", "aggregator": "avg"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "layout": "key-value", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "aws.s3.bucket~bytes.avg", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "object.type", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "object.vendor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}], "sorting": {"limit": 10, "order": "desc", "column": "aws.s3.bucket.bytes.avg"}}}, "visualization.result.by": ["monitor"], "granularity": {"value": 5, "unit": "m"}, "_type": "0", "id": 1820764169243726, "container.type": "dashboard"}, {"visualization.name": "AWS EC2 Instances", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Gauge", "visualization.time.range.inclusive": "no", "visualization.type": "MetroTile", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ec2.instances", "aggregator": "last"}]}], "visualization.properties": {"gauge": {"style": {"chart.legend": "no", "chart.label": "no", "type": "number", "font.size": "medium", "text.align": "center", "color.conditions": [{"color": "#f04e3e", "value": 0}, {"color": "#f58518", "value": 0}, {"color": "#f5bc18", "value": 0}]}}}, "granularity": {"value": 5, "unit": "m"}, "_type": "0", "id": 320155317294, "visualization.result.by": [], "container.type": "dashboard"}, {"visualization.name": "AWS EBS Volumes", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Gauge", "visualization.time.range.inclusive": "no", "visualization.type": "MetroTile", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ebs.volumes", "aggregator": "last"}]}], "visualization.properties": {"gauge": {"style": {"chart.legend": "no", "chart.label": "no", "type": "number", "font.size": "medium", "text.align": "center", "color.conditions": [{"color": "#f04e3e", "value": 0}, {"color": "#f58518", "value": 0}, {"color": "#f5bc18", "value": 0}]}}}, "granularity": {"value": 5, "unit": "m"}, "_type": "0", "id": 320155317296, "visualization.result.by": [], "container.type": "dashboard"}, {"visualization.name": "AWS Health Overview", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "HeatMap", "visualization.data.sources": [{"type": "policy", "category": "metric", "visualization.result.by": ["monitor", "severity"], "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "severity", "aggregator": "count", "entity.type": "Group", "entities": [10000000000014]}]}], "visualization.properties": {}, "visualization.result.by": ["monitor"], "granularity": {"value": 5, "unit": "m"}, "_type": "0", "id": 99657876910427, "container.type": "dashboard"}, {"visualization.name": "AWS Cloud Alert Overview", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Gauge", "visualization.type": "MetroTile", "visualization.data.sources": [{"type": "policy", "category": "metric", "visualization.result.by": ["severity"], "filters": {"data.filter": {}, "result.filter": {}}, "severity": ["DOWN", "CRITICAL", "MAJOR", "WARNING"], "data.points": [{"data.point": "severity", "aggregator": "count", "entity.type": "Group", "entities": [10000000000014]}]}], "visualization.properties": {"gauge": {"style": {"layout": "Radial View", "chart.legend": "no", "chart.label": "no", "type": "number", "font.size": "small", "text.align": "left", "color.conditions": [{"color": "#f04e3e", "value": 0}, {"color": "#f58518", "value": 0}, {"color": "#f5bc18", "value": 0}]}}}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 96435113520209}, {"visualization.name": "AWS EBS IOPS", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ebs.volume.read.ops.per.sec", "aggregator": "avg"}, {"data.point": "aws.ebs.volume.write.ops.per.sec", "aggregator": "avg"}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc"}}}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 96435113520211}, {"visualization.name": "AWS EBS Read bytes per second", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "aws.ebs.volume.read.bytes.per.sec", "aggregator": "avg"}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "no", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc"}}}, "visualization.result.by": ["monitor"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 96435113520212}, {"visualization.name": "AWS RDS Disk IO Latency", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.rds.disk.io.write.latency.ms", "aggregator": "avg"}, {"data.point": "aws.rds.disk.io.read.latency.ms", "aggregator": "avg"}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc"}}}, "visualization.result.by": ["monitor"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 96435113520214}, {"visualization.name": "AWS Service Cost", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Gauge", "visualization.type": "MetroTile", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.billing.service.cost", "aggregator": "last"}]}], "visualization.properties": {"gauge": {"style": {"chart.legend": "no", "chart.label": "no", "type": "number", "font.size": "medium", "text.align": "center", "icon": {"name": "dollar-sign", "placement": "suffix"}, "color.conditions": [{"color": "#f04e3e", "value": 0}, {"color": "#f58518", "value": 0}, {"color": "#f5bc18", "value": 0}]}}}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 1585017354910}, {"visualization.name": "AWS Billing Forecast", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Gauge", "visualization.type": "MetroTile", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.billing.usage.forecast", "aggregator": "last"}]}], "visualization.properties": {"gauge": {"style": {"chart.legend": "no", "chart.label": "no", "type": "number", "font.size": "medium", "text.align": "center", "icon": {"name": "dollar-sign", "placement": "suffix"}, "color.conditions": [{"color": "#f04e3e", "value": 0}, {"color": "#f58518", "value": 0}, {"color": "#f5bc18", "value": 0}]}}}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 1585017354911}, {"visualization.name": "AWS RDS Instances", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Gauge", "visualization.type": "MetroTile", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.rds.instances", "aggregator": "avg"}]}], "visualization.properties": {"gauge": {"style": {"chart.legend": "no", "chart.label": "no", "type": "number", "font.size": "medium", "text.align": "center", "color.conditions": [{"color": "#f04e3e", "value": 0}, {"color": "#f58518", "value": 0}, {"color": "#f5bc18", "value": 0}]}}}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 1585017354912}, {"visualization.name": "AWS S3 Bucket Objects", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Gauge", "visualization.type": "MetroTile", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.s3.bucket.objects", "aggregator": "avg"}]}], "visualization.properties": {"gauge": {"style": {"chart.legend": "no", "chart.label": "no", "type": "number", "font.size": "medium", "text.align": "center", "color.conditions": [{"color": "#f04e3e", "value": 0}, {"color": "#f58518", "value": 0}, {"color": "#f5bc18", "value": 0}]}}}, "visualization.result.by": [], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 1585017354913}, {"visualization.name": "Top S3 buckets by requests", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "aws.s3.bucket.requests", "aggregator": "avg"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "layout": "key-value", "style": {"header.font.size": "small"}, "columns": [{"name": "object.type", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "monitor", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "aws.s3.bucket.requests.avg", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "object.vendor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}]}}, "visualization.result.by": ["monitor"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 1585017354915}, {"visualization.name": "Top EBS Volumes by Latency", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "aws.ebs.volume.avg.write.latency.ms", "aggregator": "avg"}, {"data.point": "aws.ebs.volume.avg.read.latency.ms", "aggregator": "avg"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "title": "Monitor", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "aws.ebs.volume.avg.write.latency.ms.avg", "title": "Write Latency", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "aws.ebs.volume.avg.read.latency.ms.avg", "title": "Read Latency", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "object.type", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "object.vendor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}], "sorting": {"limit": 10, "order": "desc", "column": "aws.ebs.volume.avg.write.latency.ms.avg"}}}, "visualization.result.by": ["monitor"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 1585017354919}, {"visualization.name": "Top RDS Instance by Database Connections", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "aws.rds.database.connections", "aggregator": "avg"}, {"data.point": "aws.rds.database.connections", "aggregator": "sparkline"}]}], "visualization.properties": {"sparkline": "yes", "grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "title": "Monitor", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "aws.rds.database.connections.avg", "title": "Database Connections", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "object.type", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "object.vendor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "aws.rds.database.connections.sparkline", "title": "Sparkline", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "type": "sparkline", "style": {"inline.chart": {"type": "sparkarea", "color": "#F58518"}}}], "sorting": {"limit": 10, "order": "desc", "column": "aws.rds.database.connections.avg"}}}, "visualization.result.by": ["monitor"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 1585017354921}, {"visualization.name": "Top RDS by Storage free bytes", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "aws.rds.storage.free.bytes", "aggregator": "avg"}]}], "visualization.properties": {"chart": {"chart.legend": "no", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "aws.rds.storage.free.bytes.avg"}}}, "visualization.result.by": ["monitor"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 1585017354922}, {"visualization.name": "RDS Disk IO Operations/s", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "aws.rds.disk.io.ops.per.sec", "aggregator": "avg"}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "no", "vertical.legend": "no", "line.width": 2, "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc"}}}, "visualization.result.by": ["monitor"], "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard", "_type": "0", "id": 1585017354923}]