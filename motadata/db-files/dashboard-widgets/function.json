[{"_type": "0", "id": 10000000001746, "visualization.name": "Active Function", "visualization.description": "Active Function Azure Function", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.functions", "aggregator": "last", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.functions", "icon": {"name": "function", "placement": "prefix"}}, "header": {"title": "Active Function", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.functions.last"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001747, "visualization.name": "Avg. Response time", "visualization.description": "Avg. Response time Azure Function", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "last", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "", "icon": {"name": "response-time", "placement": "prefix"}}, "header": {"title": "Avg. Response time", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": ".last"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001748, "visualization.name": "Total Connections", "visualization.description": "Total Connections Azure Function", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.connections", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.function.connections", "icon": {"name": "sitemap", "placement": "prefix"}}, "header": {"title": "Total Connections", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.function.connections.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001749, "visualization.name": "Queued Requests", "visualization.description": "Queued Requests Azure Function", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.request.queued.requests", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.function.request.queued.requests", "icon": {"name": "requests", "placement": "prefix"}}, "header": {"title": "Queued Requests", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.function.request.queued.requests.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001750, "visualization.name": "Memory", "visualization.description": "Memory Azure Function", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.memory.used.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "azure.function.private.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.function.private.bytes", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "Memory", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Usage", "value": "azure.function.memory.used.bytes.avg"}, {"label": "Private bytes", "value": "azure.function.private.bytes.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001751, "visualization.name": "Throughput", "visualization.description": "Throughput Azure Function", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.sent.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "azure.function.received.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.function.sent.bytes", "icon": {"name": "tacho-meter", "placement": "prefix"}}, "header": {"title": "Throughput", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "<PERSON><PERSON>", "value": "azure.function.sent.bytes.avg"}, {"label": "Received", "value": "azure.function.received.bytes.avg"}]}}}, "container.type": "dashboard"}, {"id": 10000000001752, "visualization.name": "Top Function By Connections", "visualization.description": "Top Function By Connections Azure Function", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.connections", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.function.connections.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001753, "visualization.name": "Top Function By Errors", "visualization.description": "Top Function By Errors Azure Function", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.5xx.requests", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.function.5xx.requests.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001754, "visualization.name": "Top Function By Memory Usage", "visualization.description": "Top Function By Memory Usage Azure Function", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.memory.used.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.function.memory.used.bytes.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001755, "visualization.name": "Top Function By Read Operations", "visualization.description": "Top Function By Read Operations Azure Function", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.read.ops.per.sec", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.function.read.ops.per.sec.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001756, "visualization.name": "Top Function By Write Operations", "visualization.description": "Top Function By Write Operations Azure Function", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.write.ops.per.sec", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.function.write.ops.per.sec.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001757, "visualization.name": "Top Function By <PERSON><PERSON>", "visualization.description": "Top Function By Byte Sent Azure Function", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.sent.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.function.sent.bytes.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001758, "visualization.name": "Top Function By Byte Received", "visualization.description": "Top Function By Byte Received Azure Function", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.received.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.function.received.bytes.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001759, "visualization.name": "Top Function By Requests in Application Queue", "visualization.description": "Top Function By Requests in Application Queue Azure Function", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.function.request.queued.requests", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.function.request.queued.requests.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}]