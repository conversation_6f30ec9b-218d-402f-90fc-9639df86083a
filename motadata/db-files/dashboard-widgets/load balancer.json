[{"_type": "0", "id": 10000000001671, "visualization.name": "Total Loadbalancer", "visualization.description": "Total Loadbalancer Azure Loadbalancer", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.instances", "aggregator": "last", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "", "icon": {"name": "host", "placement": "prefix"}}, "header": {"title": "Total Loadbalancer", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.loadbalancer.instances.last"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001672, "visualization.name": "SNAT Ports", "visualization.description": "SNAT Ports Azure Loadbalancer", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.snat.ports", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "azure.loadbalancer.used.snat.ports", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.loadbalancer.used.snat.ports", "icon": {"name": "interface", "placement": "prefix"}}, "header": {"title": "SNAT Ports", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Allocated", "value": "azure.loadbalancer.snat.ports.avg"}, {"label": "Used", "value": "azure.loadbalancer.used.snat.ports.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001673, "visualization.name": "SNAT Connection Count", "visualization.description": "SNAT Connection Count Azure Loadbalancer", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.snat.connections", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.loadbalancer.snat.connections", "icon": {"name": "active-connections", "placement": "prefix"}}, "header": {"title": "SNAT Connection Count", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.loadbalancer.snat.connections.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001674, "visualization.name": "TCP SYN Packets", "visualization.description": "TCP SYN Packets Azure Loadbalancer", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.syn.packets", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.loadbalancer.syn.packets", "icon": {"name": "synchronization", "placement": "prefix"}}, "header": {"title": "TCP SYN Packets", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.loadbalancer.syn.packets.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001675, "visualization.name": "Packets Count", "visualization.description": "Packets Count Azure Loadbalancer", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.packets", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.loadbalancer.packets", "icon": {"name": "inno-db", "placement": "prefix"}}, "header": {"title": "Packets Count", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.loadbalancer.packets.avg"}]}}}, "container.type": "dashboard"}, {"_type": "0", "id": 10000000001676, "visualization.name": "Byte Count", "visualization.description": "Byte Count Azure Loadbalancer", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.loadbalancer.bytes", "icon": {"name": "transactions", "placement": "prefix"}}, "header": {"title": "Byte Count", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.loadbalancer.bytes.avg"}]}}}, "container.type": "dashboard"}, {"id": 10000000001677, "visualization.name": "DIP Availability", "visualization.description": "DIP Availability Azure Loadbalancer", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.dip.availability", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001678, "visualization.name": "Top LB By SNAT Port Usage", "visualization.description": "Top LB By SNAT Port Usage Azure Loadbalancer", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "HorizontalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.used.snat.ports", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.loadbalancer.used.snat.ports.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001679, "visualization.name": "SNAT Connection Count", "visualization.description": "SNAT Connection Count Azure Loadbalancer", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "HorizontalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.snat.connections", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.loadbalancer.snat.connections.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001680, "visualization.name": "Top LB's <PERSON> Bytes Transmitted", "visualization.description": "Top LB's By Bytes Transmitted Azure Loadbalancer", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.bytes", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.loadbalancer.bytes.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001681, "visualization.name": "Top LB's By TCP SYN Packets", "visualization.description": "Top LB's By TCP SYN Packets Azure Loadbalancer", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.syn.packets", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.loadbalancer.syn.packets.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}, {"id": 10000000001682, "visualization.name": "Top LB's By Packet Count", "visualization.description": "Top LB's By Packet Count Azure Loadbalancer", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.loadbalancer.packets", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.loadbalancer.packets.avg"}}}, "granularity": {"value": 5, "unit": "m"}, "container.type": "dashboard"}]