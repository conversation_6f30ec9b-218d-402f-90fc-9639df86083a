[{"id": 10000000000966, "_type": "0", "visualization.name": "Windows RDP Sessions", "visualization.description": "Windows RDP Sessions", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.rdp.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "windows.rdp.sessions"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "icon": {"name": "sessions", "placement": "prefix"}}, "header": {"title": "Sessions", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "windows.rdp.sessions.last"}]}}}}, {"id": 10000000000967, "_type": "0", "visualization.name": "Active Sessions", "visualization.description": "Windows RDP Active Sessions", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.rdp.active.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "windows.rdp.active.sessions"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "windows.rdp.active.sessions", "icon": {"name": "active-connections", "placement": "prefix"}}, "header": {"title": "Active Sessions", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "windows.rdp.active.sessions.last"}]}}}}, {"id": 10000000000968, "_type": "0", "visualization.name": "Windows RDP Inactive Sessions", "visualization.description": "Windows RDP Inactive Sessions", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.rdp.inactive.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "windows.rdp.inactive.sessions"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "icon": {"name": "rejected-connections", "placement": "prefix"}}, "header": {"title": "Inactive Session", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "windows.rdp.inactive.sessions.last"}]}}}}, {"id": 10000000000969, "_type": "0", "visualization.name": "Session Statistics", "visualization.description": "Windows RDP Session Statistics", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.rdp.session~id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "windows.rdp.session~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "windows.rdp.session~user", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "windows.rdp.session~idle.time.minutes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "windows.rdp.session~logon.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "windows.rdp.session~id.last", "title": "ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "windows.rdp.session~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "windows.rdp.session", "title": "Session", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "windows.rdp.session~user.last", "title": "User", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "windows.rdp.session~idle.time.minutes.last", "title": "Idle Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "windows.rdp.session~logon.time.last", "title": "Logon Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}]}}}, {"_type": "0", "id": 10000000000970, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Application Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000971, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Application Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "system.process~uptime.percent.avg"}}}}, {"id": 10000000000972, "_type": "0", "visualization.name": "Session Details", "visualization.description": "Windows RDP Session Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "windows.rdp.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "windows.rdp.active.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "windows.rdp.inactive.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}]