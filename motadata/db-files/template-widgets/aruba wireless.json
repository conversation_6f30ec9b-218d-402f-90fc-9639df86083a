[{"_type": "0", "id": 10000000001009, "visualization.name": "Aruba Access Points", "visualization.description": "Aruba Access Points", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.access.points", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aruba.wireless.access.points"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aruba.wireless.access.points", "icon": {"name": "router", "placement": "prefix"}}, "header": {"title": "Access Points", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Total", "value": "aruba.wireless.access.points.last"}]}}}}, {"_type": "0", "id": 10000000001010, "visualization.name": "Aruba Rogues", "visualization.description": "Aruba Rogues", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.rogue.clients", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.rogue.access.points", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aruba.wireless.rogue.clients"}, {"type": "metric", "data.point": "aruba.wireless.rogue.access.points"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aruba.wireless.rogue.access.points", "icon": {"name": "cloud-wifi", "placement": "prefix"}}, "header": {"title": "Rogues", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "APs", "value": "aruba.wireless.rogue.access.points.last"}, {"label": "Clients", "value": "aruba.wireless.rogue.clients.last"}]}}}}, {"_type": "0", "id": 10000000001011, "visualization.name": "Aruba Active Clients", "visualization.description": "Aruba Active Clients", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.clients", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aruba.wireless.clients"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aruba.wireless.clients", "icon": {"name": "users", "placement": "prefix"}}, "header": {"title": "Active Clients", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aruba.wireless.clients.last"}]}}}}, {"_type": "0", "id": 10000000001012, "visualization.name": "Aruba WLAN", "visualization.description": "Aruba WLAN", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.wlans", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aruba.wireless.wlans"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aruba.wireless.wlans", "icon": {"name": "wifi", "placement": "prefix"}}, "header": {"title": "WLAN", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aruba.wireless.wlans.last"}]}}}}, {"_type": "0", "id": 10000000001013, "visualization.name": "Aruba Response Time", "visualization.description": "Aruba Response Time", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ping.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ping.latency.ms"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "ping.latency.ms", "icon": {"name": "response-time", "placement": "prefix"}}, "header": {"title": "Response Time", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "ping.latency.ms.last"}]}}}}, {"_type": "0", "id": 10000000001014, "visualization.name": "Aruba Packet Lost", "visualization.description": "Aruba Packet Lost", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ping.packet.lost.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ping.packet.lost.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "ping.packet.lost.percent", "icon": {"name": "rejected-connections", "placement": "prefix"}}, "header": {"title": "Packet Lost", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "ping.packet.lost.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000001015, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000001016, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000001017, "visualization.name": "Aruba CPU/Memory Utilization", "visualization.description": "Aruba CPU/Memory Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.controller.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.controller.memory.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001018, "visualization.name": "Aruba Top Clients By Packets", "visualization.description": "Aruba Top Clients By Packets", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.client~packets.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.client~packets.per.sec", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "aruba.wireless.client", "title": "Client", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "aruba.wireless.client~packets.per.sec.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"inline.chart": {"type": "sparkline"}}}, {"name": "aruba.wireless.client~packets.per.sec.avg", "title": "Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "type": "number", "style": {}}], "sorting": {"limit": "5", "order": "desc", "column": "aruba.wireless.client~packets.per.sec.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000001019, "visualization.name": "Aruba Top Access Point Interface By Clients", "visualization.description": "Aruba Top Access Point Interface By Clients", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.access.point.interface~clients", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point.interface~clients", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "aruba.wireless.access.point.interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "aruba.wireless.access.point.interface~clients.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"inline.chart": {"type": "sparkline"}}}, {"name": "aruba.wireless.access.point.interface~clients.avg", "title": "Total Client", "type": "number", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}], "sorting": {"limit": "5", "order": "desc", "column": "aruba.wireless.access.point.interface~clients.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000001020, "visualization.name": "Aruba Top Wireless Clients By Traffic", "visualization.description": "Aruba Top Wireless Clients By Traffic", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.client~traffic.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.client~traffic.bytes.per.sec", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "aruba.wireless.client", "title": "Clients", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "aruba.wireless.client~traffic.bytes.per.sec.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"inline.chart": {"type": "sparkline"}}}, {"name": "aruba.wireless.client~traffic.bytes.per.sec.avg", "title": "Total Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}], "sorting": {"limit": "5", "order": "desc", "column": "aruba.wireless.client~traffic.bytes.per.sec.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000002242, "visualization.name": "Aruba Interface Details", "visualization.description": "Aruba Interface Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}, "visualization.result.by": ["interface"]}, "data.points": [{"data.point": "interface~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~in.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~out.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~sent.octets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~received.octets", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold"]}}, {"name": "interface~name.last", "title": "Interface Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"classes": ["font-bold"]}}, {"name": "interface~status.last", "title": "Operational Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "type": "status"}, {"name": "interface~address.last", "title": "Interface Address", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"classes": ["font-bold"], "icon": {"name": "user-friends", "placement": "prefix", "classes": ["text-primary"]}}}, {"name": "Packets", "title": "Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "position": 6, "type": "number", "formula": {"operation": "combine", "columns": ["interface~in.packets.last", "interface~out.packets.last"]}, "style": {"classes": ["font-bold"], "icon": {"name": "traffic", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "interface~in.packets.last", "title": "In Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 7, "style": {"icon": {"name": "long-arrow-right", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "interface~out.packets.last", "title": "Out Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 8, "style": {"icon": {"name": "long-arrow-left", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "Octets", "title": "Octets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "position": 9, "formula": {"operation": "combine", "columns": ["interface~sent.octets.last", "interface~received.octets.last"]}, "style": {"classes": ["font-bold"], "icon": {"name": "traffic", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "interface~sent.octets.last", "title": "Sent Octets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 10, "style": {"icon": {"name": "long-arrow-left", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "interface~received.octets.last", "title": "Received Octets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 11, "style": {"icon": {"name": "long-arrow-right", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}]}}}, {"_type": "0", "id": 10000000001021, "visualization.name": "Aruba Access Point", "visualization.description": "Aruba Access Point Grid", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Access Point", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.access.point~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~mac.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~started.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point~clients", "aggregator": "last", "entity.type": "monitor", "entities": []}]}, {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.access.point.interface~sent.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point.interface~received.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.access.point.interface~mac.address", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "aruba.wireless.access.point", "title": "Access Point", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold"]}}, {"name": "aruba.wireless.access.point~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 3}, {"name": "aruba.wireless.access.point~ip.address.last", "title": "IP Address", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"classes": ["font-bold"], "icon": {"name": "map-marker-alt", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "aruba.wireless.access.point~clients.last", "title": "Clients", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"classes": ["font-bold"], "icon": {"name": "user-friends", "placement": "prefix", "classes": ["text-primary"]}}}, {"name": "Traffic", "title": "Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "position": 6, "formula": {"operation": "combine", "columns": ["aruba.wireless.access.point.interface~sent.bytes.per.sec.avg", "aruba.wireless.access.point.interface~received.bytes.per.sec.avg"]}, "style": {"classes": ["font-bold"], "icon": {"name": "traffic", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "aruba.wireless.access.point.interface~sent.bytes.per.sec.avg", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {"icon": {"name": "long-arrow-left", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "aruba.wireless.access.point.interface~received.bytes.per.sec.avg", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {"icon": {"name": "long-arrow-right", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "aruba.wireless.access.point~mac.address.last", "title": "MAC Address", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"classes": ["font-bold"], "icon": {"name": "desktop", "placement": "prefix", "classes": ["text-secondary-red "]}}}, {"name": "aruba.wireless.access.point~started.time.last", "title": "Uptime", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10, "style": {"classes": ["font-bold"]}}]}}}, {"_type": "0", "id": 10000000001022, "visualization.name": "Aruba WLAN Summary", "visualization.description": "Aruba WLAN Summary", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.wlan~clients", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.wlan~traffic.received.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.wlan~traffic.sent.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.wlan~sent.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.wlan~received.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.wlan~up.access.points", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.wlan~down.access.points", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "aruba.wireless.wlan", "title": "WLAN", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold"]}}, {"name": "aruba.wireless.wlan~clients.last", "title": "Clients", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"classes": ["font-bold"]}, "icon": {"name": "user-circle", "placement": "prefix", "classes": ["text-secondary-green"]}}, {"name": "aruba.wireless.wlan~up.access.points.last", "title": "Up AP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"classes": ["font-bold"], "icon": {"name": "user-friends", "placement": "prefix", "classes": ["text-primary"]}}}, {"name": "aruba.wireless.wlan~down.access.points.last", "title": "Down AP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"classes": ["font-bold"], "icon": {"name": "user-friends", "placement": "prefix", "classes": ["text-primary"]}}}, {"name": "Traffic Packets", "title": "Traffic (Packets)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "position": 5, "formula": {"operation": "combine", "columns": ["aruba.wireless.wlan~sent.packets.last", "aruba.wireless.wlan~received.packets.last"]}, "style": {"classes": ["font-bold"], "icon": {"name": "traffic", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "aruba.wireless.wlan~sent.packets.last", "title": "Sent Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 6, "style": {"icon": {"name": "long-arrow-left", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "aruba.wireless.wlan~received.packets.last", "title": "Received Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 7, "style": {"icon": {"name": "long-arrow-right", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "aruba.wireless.wlan~traffic.received.bytes.per.sec.last", "title": "Received Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 8, "style": {"icon": {"name": "long-arrow-right", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "aruba.wireless.wlan~traffic.sent.bytes.per.sec.last", "title": "Sent Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"icon": {"name": "long-arrow-left", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "Traffic Bytes", "title": "Traffic (Bytes)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "position": 10, "formula": {"operation": "combine", "columns": ["aruba.wireless.wlan~traffic.sent.bytes.per.sec.last", "aruba.wireless.wlan~traffic.received.bytes.per.sec.last"]}}]}}}, {"_type": "0", "id": 10000000001025, "visualization.name": "Rogue Access Points", "visualization.description": "Aruba Rogue Access Points", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.rogue.access.point~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.rogue.access.point~ssid", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.rogue.access.point~class.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.rogue.access.point~channel", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.rogue.access.point~interface.type", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "title": " ", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "aruba.wireless.rogue.access.point", "title": "Access Point", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-semibold"], "icon": {"name": "inventory", "placement": "prefix", "classes": ["text-secondary-red"]}}}, {"name": "aruba.wireless.rogue.access.point~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "aruba.wireless.rogue.access.point~ssid.last", "title": "SSID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"classes": ["font-semibold"], "icon": {"name": "user-friends", "placement": "prefix", "classes": ["text-secondary-red"]}}}, {"name": "aruba.wireless.rogue.access.point~class.type.last", "title": "Class Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"classes": ["font-semibold"], "icon": {"name": "topology", "placement": "prefix", "classes": ["text-primary"]}}}, {"name": "aruba.wireless.rogue.access.point~channel.last", "title": "Channel", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "type": "label", "style": {}}, {"name": "aruba.wireless.rogue.access.point~interface.type.last", "title": "Interface Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"classes": ["font-semibold"], "icon": {"name": "port", "placement": "prefix", "classes": ["text-neutral-light"]}}}]}}}, {"_type": "0", "id": 10000000001026, "visualization.name": "Rogue Access Points Clients", "visualization.description": "Aruba Rogue Access Points Clients", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aruba.wireless.rogue.client~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.rogue.client~ap.bssid", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.rogue.client~channel", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.rogue.client~class.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.rogue.client~ssid", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aruba.wireless.rogue.client~interface.type", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "title": " ", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "aruba.wireless.rogue.client", "title": "Client", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-semibold"], "icon": {"name": "inventory", "placement": "prefix", "classes": ["text-secondary-red"]}}}, {"name": "aruba.wireless.rogue.client~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "type": "status"}, {"name": "aruba.wireless.rogue.client~ap.bssid.last", "title": "BSSID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"classes": ["font-semibold"], "icon": {"name": "user", "placement": "prefix", "classes": ["text-secondary-orange"]}}}, {"name": "aruba.wireless.rogue.client~class.type.last", "title": "Class Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"classes": ["font-semibold"], "icon": {"name": "topology", "placement": "prefix", "classes": ["text-primary"]}}}, {"name": "aruba.wireless.rogue.client~ssid.last", "title": "SSID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {"classes": ["font-semibold"], "icon": {"name": "user-friends", "placement": "prefix", "classes": ["text-secondary-red"]}}}, {"name": "aruba.wireless.rogue.client~interface.type.last", "title": "Interface Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {"classes": ["font-semibold"], "icon": {"name": "port", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "aruba.wireless.rogue.client~channel.last", "title": "Channel", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "type": "label", "style": {}}]}}}]