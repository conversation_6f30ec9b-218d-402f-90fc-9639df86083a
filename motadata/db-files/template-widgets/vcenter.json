[{"_type": "0", "id": 10000000000753, "visualization.name": "CPU VCenter", "visualization.description": "CPU Virtualization (VM Ware) Category", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "vcenter.cpu.hz", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "vcenter.cpu.used.hz", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "vcenter.cpu.hz"}, {"type": "metric", "data.point": "vcenter.cpu.used.hz"}]}], "visualization.properties": {"gauge": {"header": {"title": "CPU", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Capacity", "value": "vcenter.cpu.hz.last"}, {"label": "Used", "value": "vcenter.cpu.used.hz.last"}]}, "style": {"icon": {"name": "cpu"}, "color.data.point": "vcenter.cpu.used.hz"}}}}, {"_type": "0", "id": 10000000000754, "visualization.name": "Memory VCenter", "visualization.description": "Memory Virtualization (VM Ware) Category", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "vcenter.memory.installed.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "vcenter.memory.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "vcenter.memory.installed.bytes"}, {"type": "metric", "data.point": "vcenter.memory.used.bytes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Memory", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Capacity", "value": "vcenter.memory.installed.bytes.last"}, {"label": "Used", "value": "vcenter.memory.used.bytes.last"}]}, "style": {"icon": {"name": "memory"}, "color.data.point": "vcenter.memory.used.bytes"}}}}, {"_type": "0", "id": 10000000000755, "visualization.name": "Nodes VCenter", "visualization.description": "Nodes Virtualization (VM Ware) Category", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "vcenter.connected.nodes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "vcenter.disconnected.nodes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "vcenter.connected.nodes"}, {"type": "metric", "data.point": "vcenter.disconnected.nodes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Nodes", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Connected", "value": "vcenter.connected.nodes.last"}, {"label": "Disconnected", "value": "vcenter.disconnected.nodes.last"}]}, "style": {"icon": {"name": "node-count"}, "color.data.point": "vcenter.disconnected.nodes"}}}}, {"_type": "0", "id": 10000000000756, "visualization.name": "Virtual Machines VCenter", "visualization.description": "Virtual Machines Virtualization (VM Ware) Category", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "vcenter.virtual.machines", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "vcenter.running.virtual.machines", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "vcenter.virtual.machines"}, {"type": "metric", "data.point": "vcenter.running.virtual.machines"}]}], "visualization.properties": {"gauge": {"header": {"title": "Virtual Machine", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Total", "value": "vcenter.virtual.machines.last"}, {"label": "Running", "value": "vcenter.running.virtual.machines.last"}]}, "style": {"icon": {"name": "vm"}, "color.data.point": "vcenter.running.virtual.machines"}}}}, {"_type": "0", "id": 10000000000757, "visualization.name": "DateStore VCenter", "visualization.description": "DateStore Virtualization (VM Ware) Category", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "vcenter.offline.datastores", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "vcenter.datastores", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "vcenter.offline.datastores"}, {"type": "metric", "data.point": "vcenter.datastores"}]}], "visualization.properties": {"gauge": {"header": {"title": "Datastore", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Total", "value": "vcenter.datastores.last"}, {"label": "Offline", "value": "vcenter.offline.datastores.last"}]}, "style": {"icon": {"name": "datastore"}, "color.data.point": "vcenter.offline.datastores"}}}}, {"_type": "0", "id": 10000000000758, "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.name": "Resource Pools VCenter", "visualization.description": "Resource Pools Virtualization (VM Ware) Category", "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "vcenter.resource.pools", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "vcenter.resource.pools"}]}], "visualization.properties": {"gauge": {"header": {"title": "Resource Pools", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "vcenter.resource.pools.last"}]}, "style": {"icon": {"name": "network"}, "color.data.point": "vcenter.resource.pools"}}}}, {"_type": "0", "id": 10000000000759, "visualization.name": "Datacenter By CPU Utilization ", "visualization.description": "Top Datacenter By CPU Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "vcenter.datacenter~cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000760, "visualization.name": "Datacenter By Memory Utilization ", "visualization.description": "Top Datacenter By Memory Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "vcenter.datacenter~memory.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000761, "visualization.name": "Top Cluster By CPU Utilization", "visualization.description": "Top Cluster By CPU Utilization VCenter", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "vcenter.cluster~cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "vcenter.cluster~cpu.percent", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "vcenter.cluster", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "monitor", "style": {}}, {"name": "vcenter.cluster~cpu.percent.avg", "title": "value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "alignment": "center", "style": {}}, {"name": "vcenter.cluster~cpu.percent.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "vcenter.cluster~cpu.percent.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000000762, "visualization.name": "Top Cluster By Memory Utilization", "visualization.description": "Top Cluster By Memory Utilization VCenter", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "vcenter.cluster~memory.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "vcenter.cluster~memory.used.bytes", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "vcenter.cluster", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "monitor", "style": {}}, {"name": "vcenter.cluster~memory.used.bytes.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "alignment": "center", "style": {}}, {"name": "vcenter.cluster~memory.used.bytes.sparkline", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "vcenter.cluster~memory.used.bytes.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000000763, "visualization.name": "Top ESXI By CPU Utilization", "visualization.description": "Top ESXI By CPU Utilization VCenter", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "vcenter.node~cpu.used.hz", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "vcenter.node~cpu.used.hz", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "vcenter.node", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "monitor", "style": {}}, {"name": "vcenter.node~cpu.used.hz.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "alignment": "center", "style": {}}, {"name": "vcenter.node~cpu.used.hz.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "vcenter.node~cpu.used.hz.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000000764, "visualization.name": "Top ESXI By Memory Utilization", "visualization.description": "Top ESXI By Memory Utilization VCenter", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "vcenter.node~memory.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "vcenter.node~memory.used.bytes", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "vcenter.node", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "monitor", "style": {}}, {"name": "vcenter.node~memory.used.bytes.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "alignment": "center", "style": {}}, {"name": "vcenter.node~memory.used.bytes.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "vcenter.node~memory.used.bytes.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000000765, "visualization.name": "Top VMs By Memory Utilization", "visualization.description": "Top VMs By Memory Utilization VCenter", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "vcenter.vm~memory.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "vcenter.vm~memory.used.percent", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "vcenter.vm", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "vcenter.vm~memory.used.percent.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "alignment": "center", "style": {}}, {"name": "vcenter.vm~memory.used.percent.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "vcenter.vm~memory.used.percent.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000000766, "visualization.name": "Top VMs By CPU Utilization", "visualization.description": "Top VMs By CPU Utilization VCenter", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "vcenter.vm~cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "vcenter.vm~cpu.percent", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "vcenter.vm", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "vcenter.vm~cpu.percent.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "alignment": "center", "style": {}}, {"name": "vcenter.vm~cpu.percent.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "vcenter.vm~cpu.percent.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000000767, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000768, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000000769, "visualization.name": "DataStore Utilization", "visualization.description": "VCenter Disk Virtualization (VM Ware) Category", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "vcenter.datastore~capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "vcenter.datastore~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "vcenter.datastore~free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"view": "Storage Details", "searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "vcenter.datastore", "alias": "system.disk.volume", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "vcenter.datastore~capacity.bytes.last", "alias": "system.disk.volume.capacity.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "vcenter.datastore~used.bytes.last", "alias": "system.disk.volume.used.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "vcenter.datastore~free.bytes.last", "alias": "system.disk.volume.free.bytes.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}]