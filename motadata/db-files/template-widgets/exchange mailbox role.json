[{"_type": "0", "id": 10000000002259, "visualization.name": "Operational Servers", "visualization.description": "Mail Box Role Operational Servers", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.mailbox.role.operational.servers", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "exchange.mailbox.role.operational.servers"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "exchange.mailbox.role.operational.servers", "icon": {"name": "server", "placement": "prefix"}}, "header": {"title": "Operational Servers", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "exchange.mailbox.role.operational.servers.last"}]}}}}, {"_type": "0", "id": 10000000002260, "visualization.name": "MailBoxes", "visualization.description": "Mail Box Role MailBoxes", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.mailbox.role.mailboxes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "exchange.mailbox.role.mailboxes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "exchange.mailbox.role.mailboxes", "icon": {"name": "mailbox", "placement": "prefix"}}, "header": {"title": "MailBoxes", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "exchange.mailbox.role.mailboxes.last"}]}}}}, {"_type": "0", "id": 10000000002261, "visualization.name": "Mail Submissions", "visualization.description": "Mail Box Role Mail Submissions", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.mailbox.role.mail.succeed.submissions.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.mailbox.role.mail.failed.submissions.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "exchange.mailbox.role.mail.succeed.submissions.per.sec"}, {"type": "metric", "data.point": "exchange.mailbox.role.mail.failed.submissions.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "exchange.mailbox.role.mail.succeed.submissions.per.sec", "icon": {"name": "incoming-messages", "placement": "prefix"}}, "header": {"title": "Mail Submissions", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Succesful", "value": "exchange.mailbox.role.mail.succeed.submissions.per.sec.last"}, {"label": "Failed", "value": "exchange.mailbox.role.mail.failed.submissions.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000002262, "visualization.name": "Assistant CPU Percent", "visualization.description": "Mail Box Role Assistant CPU Percent", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.mailbox.role.mailbox.assistant.cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "exchange.mailbox.role.mailbox.assistant.cpu.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "exchange.mailbox.role.mailbox.assistant.cpu.percent", "icon": {"name": "cpu", "placement": "prefix"}}, "header": {"title": "Assistant CPU Percent", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "exchange.mailbox.role.mailbox.assistant.cpu.percent.last"}]}}}}, {"_type": "0", "id": 10000000002263, "visualization.name": "Queue Length", "visualization.description": "Mail Box Role Queue Length", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.mailbox.role.replay.queue.length", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.mailbox.role.copy.queue.length", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "exchange.mailbox.role.replay.queue.length"}, {"type": "metric", "data.point": "exchange.mailbox.role.copy.queue.length"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "exchange.mailbox.role.replay.queue.length", "icon": {"name": "queue", "placement": "prefix"}}, "header": {"title": "Queue Length", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Replay", "value": "exchange.mailbox.role.replay.queue.length.last"}, {"label": "Copy", "value": "exchange.mailbox.role.copy.queue.length.last"}]}}}}, {"_type": "0", "id": 10000000002264, "visualization.name": "Mailbox Assistant Memory", "visualization.description": "Mail Box Role Mailbox Assistant Memory", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.mailbox.role.mailbox.assistant.memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "exchange.mailbox.role.mailbox.assistant.memory.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "exchange.mailbox.role.mailbox.assistant.memory.bytes", "icon": {"name": "messages", "placement": "prefix"}}, "header": {"title": "Mailbox Assistant Memory", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "exchange.mailbox.role.mailbox.assistant.memory.bytes.last"}]}}}}, {"_type": "0", "id": 10000000001902, "visualization.name": "Mail Submissions", "visualization.description": "Exchange Mail Submittions", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.mailbox.role.mail.succeed.submissions.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.mailbox.role.mail.failed.submissions.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.mailbox.role.mail.temporary.failed.submissions.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001903, "visualization.name": "Information Store RPC Requests", "visualization.description": "Exchange Information Store RPC Requests", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.mailbox.role.information.store.rpc.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.mailbox.role.information.store.rpc.outstanding.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001904, "visualization.name": "Information Store RPC Latency", "visualization.description": "Exchange Information Store RPC Latency", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.client.access.role.rpc.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.mailbox.role.information.store.rpc.slow.request.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001905, "visualization.name": "Mailbox Database", "visualization.description": "Exchange Mailbox Database", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.mailbox.role.database.read.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.mailbox.role.database.write.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.mailbox.role.database.cache.hit.ratio.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001906, "visualization.name": "Buffer Pool Size", "visualization.description": "Exchange Buffer Pool Size", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.mailbox.role.io.log.read.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.mailbox.role.io.log.write.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.mailbox.role.log.copy.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.mailbox.role.log.write.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.mailbox.role.log.replay.pending.syncs.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.mailbox.role.log.record.stalls.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001907, "visualization.name": "Mail Role Replication Check", "visualization.description": "Exchange Mail Role Replication Check", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.mailbox.role.replication.check~server", "aggregator": "last"}, {"data.point": "exchange.mailbox.role.replication.check~result", "aggregator": "last"}, {"data.point": "exchange.mailbox.role.replication.check~error", "aggregator": "last"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "title": "Monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "exchange.mailbox.role.replication.check", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "exchange.mailbox.role.replication.check~result.last", "title": "Result", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "exchange.mailbox.role.replication.check~server.last", "title": "Server", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "exchange.mailbox.role.replication.check~error.last", "title": "Error", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}]}}}, {"id": 10000000001908, "visualization.name": "Mailbox Assistant Database", "visualization.description": "Exchange Mailbox Assistant Database", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "exchange.mailbox.role.mailbox.assistant.database~instance.name", "aggregator": "last"}, {"data.point": "exchange.mailbox.role.mailbox.assistant.database~processing.time.ms", "aggregator": "avg"}, {"data.point": "exchange.mailbox.role.mailbox.assistant.database~processes.per.sec", "aggregator": "avg"}, {"data.point": "exchange.mailbox.role.mailbox.assistant.database~event.queue.length", "aggregator": "avg"}, {"data.point": "exchange.mailbox.role.mailbox.assistant.database~event.polls.per.sec", "aggregator": "avg"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "title": "", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "exchange.mailbox.role.mailbox.assistant.database~instance.name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "exchange.mailbox.role.mailbox.assistant.database~processing.time.ms.avg", "title": "Processing", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "exchange.mailbox.role.mailbox.assistant.database~processes.per.sec.avg", "title": "Processes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "exchange.mailbox.role.mailbox.assistant.database~event.queue.length.avg", "title": "Event Queue Length", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "exchange.mailbox.role.mailbox.assistant.database~event.polls.per.sec.avg", "title": "Event Polls", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}]}}}, {"id": 10000000001909, "visualization.name": "Exchange Mailbox", "visualization.description": "Exchange Mailbox Mailbox Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["exchange.mailbox"], "data.points": [{"data.point": "exchange.mailbox", "aggregator": "last"}, {"data.point": "exchange.mailbox~display.name", "aggregator": "last"}, {"data.point": "exchange.mailbox~email.address", "aggregator": "last"}, {"data.point": "exchange.mailbox~database", "aggregator": "last"}, {"data.point": "exchange.mailbox~server", "aggregator": "last"}, {"data.point": "exchange.mailbox~size.bytes", "aggregator": "avg"}, {"data.point": "exchange.mailbox~last.login", "aggregator": "last"}, {"data.point": "exchange.mailbox~items", "aggregator": "avg"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "title": "", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "exchange.mailbox.last", "title": "Mailbox", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "exchange.mailbox~display.name.last", "title": "Display Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "exchange.mailbox~email.address.last", "title": "Email Address", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "exchange.mailbox~database.last", "title": "Database", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "exchange.mailbox~server.last", "title": "Server", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "exchange.mailbox~size.bytes.avg", "title": "<PERSON><PERSON>(MB)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "exchange.mailbox~last.login.last", "title": "Last Login", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}, {"name": "exchange.mailbox~items.avg", "title": "Items", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {}}]}}}]