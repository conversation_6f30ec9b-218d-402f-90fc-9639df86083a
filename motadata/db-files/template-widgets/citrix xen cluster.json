[{"_type": "0", "id": 10000000000840, "visualization.name": "CPU Core", "visualization.description": "CPU Core Citrix Xen Cluster", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.cluster.cpu.cores", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "citrix.xen.cluster.cpu.cores"}]}], "visualization.properties": {"gauge": {"header": {"title": "CPU Cores", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "citrix.xen.cluster.cpu.cores.last"}]}, "style": {"icon": {"name": "cpu"}, "color.data.point": "citrix.xen.cluster.cpu.cores"}}}}, {"_type": "0", "id": 10000000000841, "visualization.name": "Logical Processors", "visualization.description": "Logical Processors Citrix Xen Cluster", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.cluster.logical.processors", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "citrix.xen.cluster.logical.processors"}]}], "visualization.properties": {"gauge": {"header": {"title": "Logical Processors", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "citrix.xen.cluster.logical.processors.last"}]}, "style": {"icon": {"name": "cube"}, "color.data.point": "citrix.xen.cluster.logical.processors"}}}}, {"_type": "0", "id": 10000000000842, "visualization.name": "Nodes", "visualization.description": "Nodes Citrix Xen Cluster", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.cluster.nodes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "citrix.xen.cluster.nodes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Nodes", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "citrix.xen.cluster.nodes.last"}]}, "style": {"icon": {"name": "node-count"}, "color.data.point": "citrix.xen.cluster.nodes"}}}}, {"_type": "0", "id": 10000000000843, "visualization.name": "Virtual Machines", "visualization.description": "Virtual Machines Citrix Xen Cluster", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.cluster.virtual.machines", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "citrix.xen.cluster.virtual.machines"}]}], "visualization.properties": {"gauge": {"header": {"title": "Virtual Machines", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "citrix.xen.cluster.virtual.machines.last"}]}, "style": {"icon": {"name": "vm"}, "color.data.point": "citrix.xen.cluster.virtual.machines"}}}}, {"_type": "0", "id": 10000000000844, "visualization.name": "Running Virtual Machines", "visualization.description": "Running Virtual Machines Citrix Xen Cluster", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.cluster.running.virtual.machines", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "citrix.xen.cluster.running.virtual.machines"}]}], "visualization.properties": {"gauge": {"header": {"title": "Running VMs", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "citrix.xen.cluster.running.virtual.machines.last"}]}, "style": {"icon": {"name": "circle-play"}, "color.data.point": "citrix.xen.cluster.running.virtual.machines"}}}}, {"_type": "0", "id": 10000000000845, "visualization.name": "Paused Virtual Machines", "visualization.description": "Paused Virtual Machines Citrix Xen Cluster", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.cluster.paused.virtual.machines", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.cluster.suspended.virtual.machines", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.cluster.halted.virtual.machines", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "citrix.xen.cluster.paused.virtual.machines"}, {"type": "metric", "data.point": "citrix.xen.cluster.suspended.virtual.machines"}, {"type": "metric", "data.point": "citrix.xen.cluster.halted.virtual.machines"}]}], "visualization.properties": {"gauge": {"header": {"title": "Paused VMs", "data.points": [{"label": "Paused VMs", "value": "citrix.xen.cluster.paused.virtual.machines.last"}], "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Suspended", "value": "citrix.xen.cluster.suspended.virtual.machines.last"}, {"label": "Halted", "value": "citrix.xen.cluster.halted.virtual.machines.last"}]}, "style": {"icon": {"name": "circle-pause"}, "color.data.point": "citrix.xen.cluster.paused.virtual.machines"}}}}, {"_type": "0", "id": 10000000000846, "visualization.name": "Node Details", "visualization.description": "Node Details Citrix Xen Cluster", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.cluster.node~ip", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.cluster.node~is.master", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.cluster.node~enabled", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.cluster.node~cpu.speed.hz", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.cluster.node~memory.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.cluster.node~disk.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.cluster.node~logical.processors", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "title": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "citrix.xen.cluster.node", "title": "Node", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "citrix.xen.cluster.node~ip.last", "title": "IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "citrix.xen.cluster.node~is.master.last", "title": "Master", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"icon": {"placement": "prefix", "conditions": [{"operator": "=", "value": "Yes", "icon": "check-circle"}, {"operator": "=", "value": "No", "icon": "times-circle"}]}}}, {"name": "citrix.xen.cluster.node~enabled.last", "title": "Enabled", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"icon": {"placement": "prefix", "conditions": [{"operator": "=", "value": "Yes", "icon": "check-circle"}, {"operator": "=", "value": "No", "icon": "times-circle"}]}}}, {"name": "citrix.xen.cluster.node~logical.processors.last", "title": "Logical Processor", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"classes": ["label"]}}, {"name": "citrix.xen.cluster.node~cpu.speed.hz.last", "title": "CPU", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "citrix.xen.cluster.node~memory.used.percent.last", "title": "Memory Used", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {"inline.chart": {"type": "gauge"}}}, {"name": "citrix.xen.cluster.node~disk.used.percent.last", "title": "Disk Used", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"inline.chart": {"type": "gauge"}}}]}}}, {"_type": "0", "id": 10000000000847, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000848, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000000849, "visualization.name": "Response Time vs. Packet Lost", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ping.packet.lost.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ping.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000850, "visualization.name": "VM Details", "visualization.description": "VMs Citrix Xen Cluster Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.cluster.vm~ip", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.cluster.vm~power.state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.cluster.vm~cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.cluster.vm~disk.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.cluster.vm~memory.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.cluster.vm~node.ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "title": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "citrix.xen.cluster.vm", "title": "VM", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "citrix.xen.cluster.vm~ip.last", "title": "IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "ipaddress", "style": {}}, {"name": "citrix.xen.cluster.vm~power.state.last", "title": "Power State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status"}, {"name": "citrix.xen.cluster.vm~node.ip.address.last", "title": "Node IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "ipaddress", "style": {}}, {"name": "citrix.xen.cluster.vm~cpu.percent.last", "title": "CPU", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "gauge"}}}, {"name": "citrix.xen.cluster.vm~disk.capacity.bytes.last", "title": "Disk Capacity", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "alignment": "center", "style": {}}, {"name": "citrix.xen.cluster.vm~memory.free.bytes.last", "title": "Memory Free", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000851, "visualization.name": "Top 10  Vms By CPU Utilization", "visualization.description": "Top 10 Vms By CPU Utilization Citrix Xen Cluster ", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.cluster.vm~cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.cluster.vm~cpu.percent", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "citrix.xen.cluster.vm", "title": "VM", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "citrix.xen.cluster.vm~cpu.percent.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "citrix.xen.cluster.vm~cpu.percent.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "citrix.xen.cluster.vm~cpu.percent.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000000852, "visualization.name": "Top 10  Vms By Disk Utilization", "visualization.description": "Top 10  Vms By Disk Utilization Citrix Xen Cluster ", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.cluster.vm~disk.capacity.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.cluster.vm~disk.capacity.bytes", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "title": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "citrix.xen.cluster.vm", "title": "VM", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "citrix.xen.cluster.vm~disk.capacity.bytes.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "citrix.xen.cluster.vm~disk.capacity.bytes.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "citrix.xen.cluster.vm~disk.capacity.bytes.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000000853, "visualization.name": "Top 10 Vms By Free Memory Utilization", "visualization.description": "Top 10  Vms By Free Memory Utilization Citrix Xen Cluster ", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "citrix.xen.cluster.vm~memory.free.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "citrix.xen.cluster.vm~memory.free.bytes", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "title": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "citrix.xen.cluster.vm", "title": "VM", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "citrix.xen.cluster.vm~memory.free.bytes.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "citrix.xen.cluster.vm~memory.free.bytes.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "citrix.xen.cluster.vm~memory.free.bytes.avg"}}, "sparkline": "yes"}}]