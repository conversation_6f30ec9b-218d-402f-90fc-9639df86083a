[{"_type": "0", "id": 10000000002200, "visualization.name": "IO Percent", "visualization.description": "Azure MySql IO Percent", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.io.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.mysql.server.io.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "IO Percent", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.mysql.server.io.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "rtt"}, "color.data.point": "azure.mysql.server.io.percent"}}}}, {"_type": "0", "id": 10000000002201, "visualization.name": "Memory", "visualization.description": "Azure MySql Memory Usage", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.memory.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.mysql.server.memory.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.mysql.server.memory.percent", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "Memory", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.mysql.server.memory.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000002202, "visualization.name": "CPU", "visualization.description": "Azure MySql CPU", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.mysql.server.cpu.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.mysql.server.cpu.percent", "icon": {"name": "cpu", "placement": "prefix"}}, "header": {"title": "CPU", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.mysql.server.cpu.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000002203, "visualization.name": "Connections", "visualization.description": "Azure MySql Connections", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.active.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.mysql.server.active.connections"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.mysql.server.active.connections", "icon": {"name": "active-connections", "placement": "prefix"}}, "header": {"title": "Active Connections", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.mysql.server.active.connections.last"}]}}}}, {"_type": "0", "id": 10000000002204, "visualization.name": "Storage", "visualization.description": "Azure MySql Storage", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.data.storage.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.mysql.server.data.storage.used.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.mysql.server.data.storage.used.percent", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Storage", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.mysql.server.data.storage.used.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000002205, "visualization.name": "Network", "visualization.description": "Azure MySql Network", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.network.ingress.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.mysql.server.network.egress.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.mysql.server.network.ingress.bytes"}, {"type": "metric", "data.point": "azure.mysql.server.network.egress.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.mysql.server.network.ingress.bytes", "icon": {"name": "network", "placement": "prefix"}}, "header": {"title": "Network", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "IN", "value": "azure.mysql.server.network.ingress.bytes.last"}, {"label": "OUT", "value": "azure.mysql.server.network.egress.bytes.last"}]}}}}, {"_type": "0", "id": 10000000002206, "visualization.name": "CPU", "visualization.description": "Azure MySql CPU", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "azure.mysql.server.cpu.percent.avg"}}}}, {"_type": "0", "id": 10000000002207, "visualization.name": "Network Details", "visualization.description": "Azure MySql Network Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.network.ingress.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.mysql.server.network.egress.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002208, "visualization.name": "Connection Details", "visualization.description": "Azure MySql Connection Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.active.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.mysql.server.failed.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002209, "visualization.name": "Replication Lag", "visualization.description": "Azure MySql Replication Lag", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.replication.lag.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002210, "visualization.name": "Server Storage Utilization", "visualization.description": "Azure MySql Server Storage Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.data.storage.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002211, "visualization.name": "Server Storage Utilization", "visualization.description": "Azure MySql Server Storage Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.data.storage.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.mysql.server.data.storage.capacity.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002212, "visualization.name": "Log Storage Utilization", "visualization.description": "Azure MySql Log Storage Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.log.storage.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.mysql.server.log.storage.capacity.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002213, "visualization.name": "Log Storage Utilization", "visualization.description": "Azure MySql Log Storage Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.mysql.server.log.storage.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}]