[{"_type": "0", "id": 10000000001333, "visualization.name": "Interface Status Count", "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~up.count", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~down.count", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "interface~up.count"}, {"type": "metric", "data.point": "interface~down.count"}]}], "visualization.properties": {"gauge": {"header": {"title": "Interface", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Up", "value": "up"}, {"label": "Down", "value": "down"}]}, "style": {"icon": {"name": "port"}, "color.data.point": "interface~up.count"}}}, "visualization.timeline": {"relative.timeline": "today"}}, {"_type": "0", "id": 10000000001334, "visualization.name": "Active Sessions", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "fortinet.active.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "fortinet.active.sessions"}]}], "visualization.properties": {"gauge": {"header": {"title": "Active Sessions", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "fortinet.active.sessions.last"}]}, "style": {"icon": {"name": "sessions"}, "color.data.point": "fortinet.active.sessions.last"}}}}, {"_type": "0", "id": 10000000001335, "visualization.name": "Response Time", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ping.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ping.lost.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ping.latency.ms"}, {"type": "metric", "data.point": "ping.max.latency.ms"}, {"type": "metric", "data.point": "ping.min.latency.ms"}, {"type": "metric", "data.point": "ping.lost.packets"}]}], "visualization.properties": {"gauge": {"header": {"title": "Response Time", "style": {"font.size": "medium"}, "data.points": [{"label": "Lost Packets", "value": "ping.lost.packets.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "ping.latency.ms.last"}]}, "style": {"icon": {"name": "response-time"}, "color.data.point": "ping.latency.ms.last"}}}}, {"_type": "0", "id": 10000000001336, "visualization.name": "Disk Utilization", "visualization.description": "Fortinate System Disk Details Network Category", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.disk.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.disk.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "system.disk.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "system.disk.capacity.bytes"}, {"type": "metric", "data.point": "system.disk.used.bytes"}, {"type": "metric", "data.point": "system.disk.free.bytes"}, {"type": "metric", "data.point": "system.disk.used.percent"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "disk", "placement": "prefix"}, "font.size": "medium", "color.data.point": "system.disk.used.percent"}, "header": {"title": "Disk", "style": {"font.size": "medium"}, "data.points": [{"label": "Capacity", "value": "system.disk.capacity.bytes.last", "type": "gauge"}, {"label": "Free", "value": "system.disk.free.bytes.last", "type": "gauge"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "system.disk.used.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000001337, "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.name": "CPU (%)", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "system.cpu.percent"}, {"type": "metric", "data.point": "system.cpu.idle.percent"}, {"type": "metric", "data.point": "system.cpu.system.percent"}, {"type": "metric", "data.point": "system.cpu.user.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "CPU", "style": {"font.size": "medium", "icon": "cpu"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "system.cpu.percent.last", "type": "gauge"}]}, "style": {"type": "number", "font.size": "small", "color.conditions": [{"color": "#f04e3e"}, {"color": "#f58518"}, {"color": "#f5bc18"}], "color.data.point": "system.cpu.percent", "icon": {"name": "cpu"}}}}}, {"_type": "0", "id": 10000000001338, "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.name": "Memory Used (%)", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.memory.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "system.memory.used.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "Memory", "style": {"font.size": "medium", "icon": "memory"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "system.memory.used.percent.last", "type": "gauge"}]}, "style": {"type": "number", "font.size": "small", "color.conditions": [{"color": "#f04e3e"}, {"color": "#f58518"}, {"color": "#f5bc18"}], "color.data.point": "system.memory.used.percent", "icon": {"name": "memory"}}}}}, {"_type": "0", "id": 10000000001339, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000001340, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000002149, "visualization.name": "Response Time vs Packet Lost", "visualization.description": "Response Time vs Packet Lost Fortinet Firewall", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ping.packet.lost.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ping.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001342, "visualization.name": "CPU Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001343, "visualization.name": "Memory Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.memory.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001344, "visualization.name": "TCP Retransmitted Segments Fortinet Firewall", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "network.connection.tcp.retransmitted.segments", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000002153, "visualization.name": "Policy Details", "visualization.description": "Policy Details Fortinet Firewall", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "policy~name", "aggregator": "last", "entity.type": "Monitor", "entities": []}, {"data.point": "policy~hit.count", "aggregator": "last", "entity.type": "Monitor", "entities": []}, {"data.point": "policy~active.sessions", "aggregator": "last", "entity.type": "Monitor", "entities": []}, {"data.point": "policy~to.zone", "aggregator": "last", "entity.type": "Monitor", "entities": []}, {"data.point": "policy~from.zone", "aggregator": "last", "entity.type": "Monitor", "entities": []}, {"data.point": "policy~last.used", "aggregator": "last", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "policy~name.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "policy~hit.count.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "policy~active.sessions.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "policy~to.zone.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "policy~from.zone.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "policy~last.used.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}]}}, "granularity": {"value": 5, "unit": "m"}}, {"_type": "0", "id": 10000000001362, "visualization.name": "Interface Switch PortView", "visualization.category": "Grid", "visualization.type": "Port View", "join.type": "custom", "join.result": "port.view", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "join.type": "custom", "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "interface~type^last", "operator": "in", "value": ["ethernetCsmacd (6)", "gigabitEthernet (117)"]}]}]}, "result.filter": {}}, "data.points": [{"data.point": "interface~index", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~status", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": []}}}, {"_type": "0", "id": 10000000001363, "visualization.name": "Interface Details", "visualization.category": "Grid", "visualization.type": "Interface", "publish.sub.query.progress": false, "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~index", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~alias", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~discard.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~error.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~traffic.utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~out.traffic.utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~in.traffic.utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~speed.bits.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~description", "aggregator": "last", "entity.type": "monitor", "entities": []}]}, {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "vlan~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "vlan~port", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "yes", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 1}, {"name": "interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 2, "type": "interface", "style": {}}, {"name": "interface~alias.last", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"classes": ["font-bold"]}}, {"name": "interface~ip.address.last", "title": "Interface IP", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"]}}, {"name": "interface~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "type": "status", "computed": "no", "formula": {"conditions": [{"result": "Up", "operator": "=", "value": "up"}, {"result": "Down", "operator": "=", "value": "down"}]}, "style": {"classes": ["font-bold"], "icon": {"classes": [], "placement": "prefix", "conditions": [{"icon": "times-circle", "operator": "=", "value": "down"}, {"icon": "check-circle", "operator": "=", "value": "up"}]}}}, {"name": "interface~traffic.utilization.percent.last", "title": "Traffic Utilization", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"classes": ["font-bold"], "inline.chart": {"type": "gauge"}}}, {"name": "interface~in.traffic.utilization.percent.last", "title": "IN (%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {"classes": ["font-bold"], "inline.chart": {"type": "gauge"}}}, {"name": "interface~out.traffic.utilization.percent.last", "title": "OUT (%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {"classes": ["font-bold"], "inline.chart": {"type": "gauge"}}}, {"name": "interface~type.last", "title": "Port Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"classes": ["font-bold"], "icon": {"name": "port", "classes": ["text-secondary-orange"]}}}, {"name": "interface~error.packets.last", "title": "Error", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 10, "style": {"classes": ["font-bold"], "icon": {"name": "times-circle", "classes": ["text-secondary-red"]}}}, {"name": "interface~discard.packets.last", "title": "Discarded", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 11, "style": {"classes": ["font-bold"], "icon": {"name": "trash", "classes": ["text-secondary-orange"]}}}, {"name": "interface~index.last", "title": "Interface Index", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12, "style": {}}, {"name": "interface~name.last", "title": "Interface Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "vlan", "title": "VLAN", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 13, "type": "interface", "style": {}}, {"name": "vlan~name.last", "title": "Assigned VLAN", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 14, "style": {"classes": ["font-bold"], "icon": {"name": "topology", "classes": []}}}, {"name": "vlan~port.last", "title": "VLAN Port", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "no", "orderable": "no", "position": 15, "style": {}}, {"name": "interface~speed.bits.per.sec.last", "title": "Speed", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 16, "style": {}}, {"name": "interface~description.last", "title": "Description", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 17, "style": {}}]}}}, {"_type": "0", "id": 10000000001380, "visualization.name": " Fortinet Firewall Site To Site VPN Details", "visualization.description": " Fortinet Firewall Site To Site VPN Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "tunnel~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tunnel~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tunnel~source.ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tunnel~destination.ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "tunnel~active.time.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 45}, "columns": [{"name": "monitor", "title": "Monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "tunnel", "title": "Tunnel", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "tunnel~name.last", "title": "Tunnel Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "tunnel~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 4}, {"name": "tunnel~source.ip.address.last", "title": "Source IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "tunnel~destination.ip.address.last", "title": "Destination IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "tunnel~active.time.seconds.last", "title": "Active Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "computed": "yes", "style": {"width.percent": 20, "icon": {"name": "clock", "classes": ["text-primary"]}, "classes": ["font-bold"]}}]}}}, {"_type": "0", "id": 10000000001383, "visualization.name": "Remote VPN Sessions", "visualization.description": "Remote VPN Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "remote.vpn.client~connection.ip", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "remote.vpn.client~in.traffic.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "remote.vpn.client~out.traffic.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "remote.vpn.client~index", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "remote.vpn.client~index.virtual.domain", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "remote.vpn.client~remote.vpn.ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "remote.vpn.client~duration", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 45}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "remote.vpn.client", "title": "Client", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "type": "ipaddress", "style": {}}, {"name": "remote.vpn.client~index.last", "title": "Index", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "remote.vpn.client~connection.ip.last", "title": "Connection IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "remote.vpn.client~remote.vpn.ip.address.last", "title": "IP Address", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "remote.vpn.client~duration.last", "title": "Duration", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"icon": {"name": "calendar-alt", "placement": "prefix", "classes": ["text-primary"]}}}, {"name": "remote.vpn.client~index.virtual.domain.last", "title": "Virtual Domain", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "traffic", "title": "Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "computed": "yes", "formula": {"operation": "combine", "columns": ["remote.vpn.client~in.traffic.bytes.per.sec.last", "remote.vpn.client~out.traffic.bytes.per.sec.last"]}, "style": {"width.percent": 20, "icon": {"name": "traffic", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "remote.vpn.client~in.traffic.bytes.per.sec.last", "title": "In Traffic", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 9, "style": {"icon": {"name": "long-arrow-right", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "remote.vpn.client~out.traffic.bytes.per.sec.last", "title": "Out Traffic", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 10, "style": {"icon": {"name": "long-arrow-left", "placement": "prefix", "classes": ["text-neutral-light"]}}}]}}}]