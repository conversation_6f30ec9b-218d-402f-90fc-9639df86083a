[{"_type": "0", "id": 10000000020033, "visualization.name": "Cisco ACI Controllers", "visualization.description": "Cisco ACI Controllers", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.aci.controllers", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.aci.controllers"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "controller", "placement": "prefix"}}, "header": {"title": "Controllers", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "cisco.aci.controllers.last"}]}}}}, {"_type": "0", "id": 10000000020034, "visualization.name": "Cisco ACI Endpoints", "visualization.description": "Cisco ACI Endpoints", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.aci.endpoints", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.aci.endpoints"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "network-discovery", "placement": "prefix"}}, "header": {"title": "Endpoints", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "cisco.aci.endpoints.last"}]}}}}, {"_type": "0", "id": 10000000020035, "visualization.name": "Cisco ACI Fabric", "visualization.description": "Cisco ACI Fabric", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.aci.fabrics", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.aci.fabrics"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "fabric", "placement": "prefix"}}, "header": {"title": "<PERSON><PERSON><PERSON>", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "cisco.aci.fabrics.last"}]}}}}, {"_type": "0", "id": 10000000020036, "visualization.name": "Cisco ACI Tenants", "visualization.description": "Cisco ACI Tenants", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.aci.tenants", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.aci.tenants"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "tenants", "placement": "prefix"}}, "header": {"title": "Tenants", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "cisco.aci.tenants.last"}]}}}}, {"_type": "0", "id": 10000000020037, "visualization.name": "Cisco ACI Endpoint group", "visualization.description": "Cisco ACI Endpoint group", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.aci.endpoint.groups", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.aci.endpoint.groups"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "network-discovery", "placement": "prefix"}}, "header": {"title": "Endpoint Group", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "cisco.aci.endpoint.groups.last"}]}}}}, {"_type": "0", "id": 10000000020038, "visualization.name": "Cisco ACI Active users", "visualization.description": "Cisco ACI Active users", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.aci.active.users", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.aci.active.users"}]}], "visualization.properties": {"gauge": {"header": {"title": "Active Users", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "cisco.aci.active.users.last"}]}, "style": {"icon": {"name": "active-users"}}}}}, {"_type": "0", "id": 10000000020039, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000020040, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000020041, "visualization.name": "Infrastructure Health Status", "visualization.description": "Infrastructure Health Status", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.aci.fabric.global.health.score", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.tenant.global.health.score", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000020045, "visualization.name": "Controller Summary", "visualization.description": "Controller Summary", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.aci.controller~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.controller~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.controller~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.controller~model", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.controller~ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.controller~serial.number", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.controller~pod.id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.controller~node.id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.controller~last.rebooted.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "cisco.aci.controller", "title": "DN", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.controller~name.last", "title": "NAME", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.controller~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "style": {}}, {"name": "cisco.aci.controller~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.controller~model.last", "title": "Model", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.controller~ip.last", "title": "IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.controller~serial.number.last", "title": "Serial Number", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.controller~pod.id.last", "title": "POD ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.controller~node.id.last", "title": "Node id", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.controller~health.score.last", "title": "Health score", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.controller~last.rebooted.time.last", "title": "last rebooted at", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000020042, "visualization.name": "<PERSON><PERSON><PERSON>", "visualization.description": "<PERSON><PERSON><PERSON>", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.aci.fabric~node", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.fabric~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.fabric~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.fabric~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.fabric~model", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.fabric~ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.fabric~serial.number", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.fabric~pod.id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.fabric~node.id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.fabric~health.score", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.fabric~last.rebooted.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "cisco.aci.fabric", "title": "DN", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.fabric~node.last", "title": "node", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.fabric~name.last", "title": "NAME", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.fabric~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "style": {}}, {"name": "cisco.aci.fabric~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.fabric~model.last", "title": "Model", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.fabric~ip.address.last", "title": "IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.fabric~serial.number.last", "title": "Serial Number", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.fabric~pod.id.last", "title": "POD ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.fabric~node.id.last", "title": "Node id", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.fabric~health.score.last", "title": "Health score", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.fabric~last.rebooted.time.last", "title": "last rebooted at", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000020043, "visualization.name": "Tenant Summary", "visualization.description": "Tenant Summary", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.aci.tenant~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.tenant~alias", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.tenant~description", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.tenant~application.profiles", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.tenant~endpoint.groups", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.tenant~bridge.domains", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.tenant~vrfs", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.tenant~health.score", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "cisco.aci.tenant", "title": "name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.tenant~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.tenant~alias.last", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.tenant~description.last", "title": "Description", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.tenant~application.profiles.last", "title": "Application Profile", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}, "type": "view_more_drawer"}, {"name": "cisco.aci.tenant~endpoint.groups.last", "title": "Endpoint Groups", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}, "type": "view_more_drawer"}, {"name": "cisco.aci.tenant~bridge.domains.last", "title": "Bridge Domains", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}, "type": "view_more_drawer"}, {"name": "cisco.aci.tenant~vrfs.last", "title": "VRFs", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}, "type": "view_more_drawer"}, {"name": "cisco.aci.tenant~health.score.last", "title": "Health score", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000020044, "visualization.name": "Endpoint Summary", "visualization.description": "Endpoint Summary", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.aci.endpoint~group", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.endpoint~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.endpoint~ipv4.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.endpoint~tenant", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.aci.endpoint~mac.address", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "cisco.aci.endpoint", "title": "Name", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.endpoint~mac.address.last", "title": "Endpoint", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.endpoint~group.last", "title": "endpoint group", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.endpoint~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.endpoint~ipv4.address.last", "title": "IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.aci.endpoint~tenant.last", "title": "Tenant", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}]