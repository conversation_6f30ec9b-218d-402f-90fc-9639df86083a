[{"_type": "0", "id": 10000000003001, "visualization.name": "Cisco SD WAN Edge Status", "visualization.description": "Cisco SD WAN Edge Status", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.vedge.sync.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.reachability.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.health.status", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.vedge.sync.status"}, {"type": "metric", "data.point": "cisco.vedge.reachability.status"}, {"type": "metric", "data.point": "cisco.vedge.health.status"}]}], "visualization.properties": {"gauge": {"header": {"title": "Status", "style": {"font.size": "medium"}, "data.points": [{"label": "Config Sync", "value": "cisco.vedge.sync.status.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Reachability", "value": "cisco.vedge.reachability.status.last"}, {"label": "Health", "value": "cisco.vedge.health.status.last"}]}, "style": {"icon": {"name": "chart-line"}}}}}, {"_type": "0", "id": 10000000003002, "visualization.name": "Cisco SD WAN Edge CPU Count", "visualization.description": "Cisco SD WAN Edge CPU Count", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.vedge.cpus", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.vedge.cpus"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "cpu", "placement": "prefix"}}, "header": {"title": "CPU Count", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "cisco.vedge.cpus.last"}]}}}}, {"_type": "0", "id": 10000000003003, "visualization.name": "Cisco SD WAN Edge Control Connections", "visualization.description": "Cisco SD WAN Edge Control Connections", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.vedge.control.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.vedge.control.connections"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Control Connections", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "cisco.vedge.control.connections.last"}]}}}}, {"_type": "0", "id": 10000000003004, "visualization.name": "Cisco SD WAN Edge BFD Sessions", "visualization.description": "Cisco SD WAN Edge BFD Sessions", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.vedge.bfd.up.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.bfd.down.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.vedge.bfd.up.sessions"}, {"type": "metric", "data.point": "cisco.vedge.bfd.down.sessions"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "bfd-sessions", "placement": "prefix"}}, "header": {"title": "BFD Sessions", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Up", "value": "cisco.vedge.bfd.up.sessions.last"}, {"label": "Down", "value": "cisco.vedge.bfd.down.sessions.last"}]}}}}, {"_type": "0", "id": 10000000003005, "visualization.name": "Cisco SD WAN Edge CPU Load", "visualization.description": "Cisco SD WAN Edge CPU Load", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.vedge.cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.vedge.cpu.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "cpu", "placement": "prefix"}}, "header": {"title": "CPU Load", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "type": "gauge", "value": "cisco.vedge.cpu.percent.last"}]}}}}, {"_type": "0", "id": 10000000003006, "visualization.name": "Cisco SD WAN Edge Memory Utilization", "visualization.description": "Cisco SD WAN Edge Memory Utilization", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.vedge.memory.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.vedge.memory.used.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "Memory Utilization", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "cisco.vedge.memory.used.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "memory"}}}}}, {"_type": "0", "id": 10000000003007, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000003008, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000003009, "visualization.name": "CPU vs Memory Utilization", "visualization.description": "Cisco SD WAN Edge CPU vs Memory Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.vedge.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.memory.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003010, "visualization.name": "Hardware Sensor Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Hardware Sensor", "publish.sub.query.progress": false, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.vedge.hardware.temperature.sensor~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.hardware.temperature.sensor~celsius", "aggregator": "last", "entity.type": "monitor", "entities": []}]}, {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.vedge.hardware.voltage.sensor~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.hardware.voltage.sensor~milli.volts", "aggregator": "last", "entity.type": "monitor", "entities": []}]}, {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.vedge.hardware.fan.sensor~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.hardware.fan.sensor~speed", "aggregator": "last", "entity.type": "monitor", "entities": []}]}, {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.vedge.hardware.power.sensor~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.hardware.power.sensor~watts", "aggregator": "last", "entity.type": "monitor", "entities": []}]}, {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.vedge.hardware.current.sensor~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.hardware.current.sensor~amperes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": []}}}, {"_type": "0", "id": 10000000003011, "visualization.name": "Interface Summary", "visualization.description": "Interface Summary", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.vedge.interface~operational.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.interface~admin.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.interface~received.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.interface~sent.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.interface~received.error.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.interface~sent.error.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.interface~received.discard.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.interface~sent.discard.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.interface~description", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.interface~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.interface~index", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.interface~vpn.id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.interface~subnet.mask", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.interface~speed.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.interface~sent.packets.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.interface~received.packets.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.interface~sent.octets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.interface~received.octets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.interface~classifier.entry.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.interface~in.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.interface~out.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "yes", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "cisco.vedge.interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.interface~operational.status.last", "title": "Operational Status", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "computed": "yes", "formula": {"conditions": [{"result": "Up", "operator": "=", "value": "up"}, {"result": "Down", "operator": "=", "value": "down"}]}, "style": {"classes": ["font-bold"], "icon": {"classes": [], "placement": "prefix", "conditions": [{"icon": "times-circle", "operator": "=", "value": "down"}, {"icon": "check-circle", "operator": "=", "value": "up"}]}}}, {"name": "cisco.vedge.interface~admin.status.last", "title": "Admin Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "computed": "yes", "formula": {"conditions": [{"result": "Up", "operator": "=", "value": "up"}, {"result": "Down", "operator": "=", "value": "down"}]}, "style": {"classes": ["font-bold"], "icon": {"classes": [], "placement": "prefix", "conditions": [{"icon": "times-circle", "operator": "=", "value": "down"}, {"icon": "check-circle", "operator": "=", "value": "up"}]}}}, {"name": "cisco.vedge.interface~received.bytes.per.sec.last", "title": "Rx Bps", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.interface~sent.bytes.per.sec.last", "title": "Tx Bps", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.interface~received.error.packets.last", "title": "Rx <PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.interface~sent.error.packets.last", "title": "Tx Errors", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.interface~received.discard.packets.last", "title": "Rx Drops", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.interface~sent.discard.packets.last", "title": "Tx Drops", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.interface~description.last", "title": "Description", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.interface~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.interface~index.last", "title": "If Index", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.interface~vpn.id.last", "title": "VPN Id", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.interface~subnet.mask.last", "title": "IPv4 Subnet Mask", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.interface~speed.bytes.per.sec.last", "title": "Speed", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.interface~received.packets.per.sec.last", "title": "Rx PPS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.interface~sent.packets.per.sec.last", "title": "Tx PPS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.interface~in.packets.last", "title": "Rx Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.interface~out.packets.last", "title": "Tx Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.interface~received.octets.last", "title": "Rx Octets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.interface~sent.octets.last", "title": "Tx Octets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.interface~classifier.entry.name.last", "title": "Classifier Entry Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "object.type", "title": "Type", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "object.vendor", "title": "<PERSON><PERSON><PERSON>", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "object.ip", "title": "Object IP", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000003012, "visualization.name": "Jitter", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["cisco.vedge.tloc"], "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["cisco.vedge.tloc"], "data.points": [{"data.point": "cisco.vedge.tloc~jitter.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"sorting": {"limit": 5, "order": "asc", "column": "cisco.vedge.tloc~jitter.ms.avg"}, "rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003013, "visualization.name": "Packet Lost", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["cisco.vedge.tloc"], "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["cisco.vedge.tloc"], "data.points": [{"data.point": "cisco.vedge.tloc~lost.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"sorting": {"limit": 5, "order": "asc", "column": "cisco.vedge.tloc~lost.percent.avg"}, "rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003014, "visualization.name": "Latency", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["cisco.vedge.tloc"], "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["cisco.vedge.tloc"], "data.points": [{"data.point": "cisco.vedge.tloc~latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"sorting": {"limit": 5, "order": "asc", "column": "cisco.vedge.tloc~latency.ms.avg"}, "rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003015, "visualization.name": "TLOC Summary", "visualization.description": "TLOC Summary", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.vedge.tloc~latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.tloc~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.tloc~interface.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.tloc~interface.description", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.tloc~lost.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.tloc~jitter.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "cisco.vedge.tloc", "title": "Local Color", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.tloc~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "style": {}}, {"name": "cisco.vedge.tloc~interface.name.last", "title": "Local If Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.tloc~interface.description.last", "title": "Local If Description", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.tloc~latency.ms.last", "title": "Avg Latency(ms)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.tloc~lost.percent.last", "title": "Avg Loss(%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.tloc~jitter.ms.last", "title": "Avg <PERSON>(ms)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000003016, "visualization.name": "Tunnels By QOE", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["cisco.vedge.tunnel"], "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["cisco.vedge.tunnel"], "data.points": [{"data.point": "cisco.vedge.tunnel~qoe", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"sorting": {"limit": 5, "order": "asc", "column": "cisco.vedge.tunnel~qoe.avg"}, "rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003017, "visualization.name": "Tunnels By Jitter", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["cisco.vedge.tunnel"], "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["cisco.vedge.tunnel"], "data.points": [{"data.point": "cisco.vedge.tunnel~jitter.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"sorting": {"limit": 5, "order": "asc", "column": "cisco.vedge.tunnel~jitter.ms.avg"}, "rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003018, "visualization.name": "Tunnels By Latency", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["cisco.vedge.tunnel"], "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["cisco.vedge.tunnel"], "data.points": [{"data.point": "cisco.vedge.tunnel~latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"sorting": {"limit": 5, "order": "asc", "column": "cisco.vedge.tunnel~latency.ms.avg"}, "rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000003019, "visualization.name": "Tunnel Summary", "visualization.description": "Tunnel Summary", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.vedge.tunnel~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.tunnel~health", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.tunnel~local.color", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.tunnel~local.ip", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.tunnel~remote.color", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.tunnel~remote.ip", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.tunnel~protocol", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.tunnel~qoe", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.tunnel~latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.tunnel~lost.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.tunnel~jitter.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.tunnel~received.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.tunnel~sent.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.tunnel~source.ip", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.tunnel~destination.ip", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.tunnel~source.port", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.tunnel~destination.port", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.tunnel~site.id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.vedge.tunnel~remote.host.name", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "yes", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "cisco.vedge.tunnel", "title": "Tunnel Name", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.tunnel~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "style": {}}, {"name": "cisco.vedge.tunnel~health.last", "title": "Health", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "style": {}}, {"name": "cisco.vedge.tunnel~local.color.last", "title": "Locol Color", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.tunnel~local.ip.last", "title": "Local IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.tunnel~remote.color.last", "title": "Remote Color", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.tunnel~remote.ip.last", "title": "Remote IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.tunnel~protocol.last", "title": "Protocol", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.tunnel~qoe.last", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.tunnel~latency.ms.last", "title": "Avg Latency(ms)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.tunnel~lost.percent.last", "title": "Avg Loss(%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.tunnel~jitter.ms.last", "title": "Avg <PERSON>(ms)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.tunnel~received.packets.last", "title": "Rx Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.tunnel~sent.packets.last", "title": "Tx Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.tunnel~source.ip.last", "title": "Source IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.tunnel~destination.ip.last", "title": "Destination IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.tunnel~source.port.last", "title": "Source Port", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.tunnel~destination.port.last", "title": "Destination Port", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.tunnel~site.id.last", "title": "Site ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.vedge.tunnel~remote.host.name.last", "title": "Remote Host Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "object.type", "title": "Type", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "object.vendor", "title": "<PERSON><PERSON><PERSON>", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "object.ip", "title": "Object IP", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}]