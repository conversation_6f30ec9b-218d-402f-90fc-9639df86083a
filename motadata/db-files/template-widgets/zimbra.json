[{"_type": "0", "id": 10000000001988, "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.name": "CPU (%)", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "system.cpu.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "CPU", "style": {"font.size": "medium", "icon": "cpu"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "system.cpu.percent.last", "type": "gauge"}]}, "style": {"type": "number", "font.size": "small", "color.conditions": [{"color": "#f04e3e"}, {"color": "#f58518"}, {"color": "#f5bc18"}], "color.data.point": "system.cpu.percent", "icon": {"name": "cpu"}}}}}, {"_type": "0", "id": 10000000001989, "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.name": "RAM", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": ""}]}], "visualization.properties": {"gauge": {"header": {"title": "RAM", "style": {"font.size": "medium", "icon": "cpu"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "", "type": "gauge"}]}, "style": {"type": "number", "font.size": "small", "color.data.point": "system.cpu.percent", "icon": {"name": "memory"}}}}}, {"_type": "0", "id": 10000000001990, "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.name": "Disk", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": ""}]}], "visualization.properties": {"gauge": {"header": {"title": "Disk", "style": {"font.size": "medium", "icon": "cpu"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "", "type": "gauge"}]}, "style": {"type": "number", "font.size": "small", "color.data.point": "", "icon": {"name": "disk"}}}}}, {"_type": "0", "id": 10000000001991, "visualization.name": "DB Connections", "visualization.description": "Zimbra DB Connections", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "zimbra.db.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "zimbra.db.used.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "zimbra.db.connection.latency.ms"}, {"type": "metric", "data.point": "zimbra.db.connections"}, {"type": "metric", "data.point": "zimbra.db.used.connections"}]}], "visualization.properties": {"gauge": {"header": {"title": "DB Connections", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Total", "value": "zimbra.db.connections.last"}, {"label": "Used", "value": "zimbra.db.used.connections.last"}]}, "style": {"icon": {"name": "database"}, "color.data.point": "zimbra.db.connections.last"}}}}, {"_type": "0", "id": 10000000001992, "visualization.name": "Message", "visualization.description": "Zimbra Message", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "zimbra.lmtp.delivered.messages", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "zimbra.lmtp.received.messages", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "zimbra.lmtp.delivered.bytes"}, {"type": "metric", "data.point": "zimbra.lmtp.received.bytes"}, {"type": "metric", "data.point": "zimbra.lmtp.delivered.messages"}, {"type": "metric", "data.point": "zimbra.lmtp.received.messages"}]}], "visualization.properties": {"gauge": {"header": {"title": "Message", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Delivered", "value": "zimbra.lmtp.delivered.messages.last"}, {"label": "Received", "value": "zimbra.lmtp.received.messages.last"}]}, "style": {"icon": {"name": "messages"}, "color.data.point": "zimbra.lmtp.delivered.messages.last"}}}}, {"_type": "0", "id": 10000000001993, "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.name": "Threads", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"header": {"title": "Threads", "style": {"font.size": "medium", "icon": "cpu"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "", "type": "gauge"}]}, "style": {"type": "number", "font.size": "small", "color.data.point": "", "icon": {"name": "queue"}}}}}, {"id": **************, "_type": "0", "visualization.name": "LDAP Account", "visualization.description": "Zimbra LDAP Account", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "zimbra.mailbox.cache.hit.ratio.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": **************, "_type": "0", "visualization.name": "DB Connections", "visualization.description": "Zimbra DB Connections", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "zimbra.db.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "zimbra.db.used.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001996, "_type": "0", "visualization.name": "Sorts", "visualization.description": "Zimbra Sorts", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "zimbra.gc.major.count", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "zimbra.gc.minor.count", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001997, "_type": "0", "visualization.name": "Pop3", "visualization.description": "Zimbra Pop3", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "zimbra.pop3.received.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001998, "_type": "0", "visualization.name": "Soap", "visualization.description": "Zimbra Soap", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "zimbra.soap.received.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001999, "_type": "0", "visualization.name": "<PERSON><PERSON>", "visualization.description": "Zimbra Heap", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "zimbra.heap.free.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "zimbra.heap.used.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000002000, "_type": "0", "visualization.name": "Mailbox", "visualization.description": "Zimbra Mailbox", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "zimbra.mailbox.cache.hit.ratio.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}]