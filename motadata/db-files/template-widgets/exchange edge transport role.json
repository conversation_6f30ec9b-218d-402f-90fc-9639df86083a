[{"_type": "0", "id": 10000000002253, "visualization.name": "Messages", "visualization.description": "Edge Transport Role Messages", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.edge.transport.role.sent.messages.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.edge.transport.role.received.messages.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "exchange.edge.transport.role.sent.messages.per.sec"}, {"type": "metric", "data.point": "exchange.edge.transport.role.received.messages.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "exchange.edge.transport.role.received.messages.per.sec", "icon": {"name": "messages", "placement": "prefix"}}, "header": {"title": "Messages", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "<PERSON><PERSON>", "value": "exchange.edge.transport.role.sent.messages.per.sec.last"}, {"label": "Received", "value": "exchange.edge.transport.role.received.messages.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000002254, "visualization.name": "Database Operations", "visualization.description": "Edge Transport Role Database Operations", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.edge.transport.role.database.writes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.edge.transport.role.database.reads.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "exchange.edge.transport.role.database.writes.per.sec"}, {"type": "metric", "data.point": "exchange.edge.transport.role.database.reads.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "exchange.edge.transport.role.database.reads.per.sec", "icon": {"name": "database", "placement": "prefix"}}, "header": {"title": "Database Operations", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Reads", "value": "exchange.edge.transport.role.database.reads.per.sec.last"}, {"label": "Writes", "value": "exchange.edge.transport.role.database.writes.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000002255, "visualization.name": "Submission Queue", "visualization.description": "Edge Transport Role Submission Queue", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.edge.transport.role.submission.queue.length", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "exchange.edge.transport.role.submission.queue.length"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "exchange.edge.transport.role.submission.queue.length", "icon": {"name": "queue", "placement": "prefix"}}, "header": {"title": "Submission Queue", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "exchange.edge.transport.role.submission.queue.length.last"}]}}}}, {"_type": "0", "id": 10000000002256, "visualization.name": "Message Bytes", "visualization.description": "Edge Transport Role Message Bytes", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.edge.transport.role.message.bytes.per.message", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "exchange.edge.transport.role.message.bytes.per.message"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "exchange.edge.transport.role.message.bytes.per.message", "icon": {"name": "message-size", "placement": "prefix"}}, "header": {"title": "Message Bytes", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "exchange.edge.transport.role.message.bytes.per.message.last"}]}}}}, {"_type": "0", "id": 10000000002257, "visualization.name": "Message Deliveries", "visualization.description": "Edge Transport Role Message Deliveries", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.edge.transport.role.message.deliveries.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "exchange.edge.transport.role.message.deliveries.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "exchange.edge.transport.role.message.deliveries.per.sec", "icon": {"name": "outgoing-messages", "placement": "prefix"}}, "header": {"title": "Message Deliveries", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "exchange.edge.transport.role.message.deliveries.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000002258, "visualization.name": "Message Submissions", "visualization.description": "Edge Transport Role Message Submissions", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.edge.transport.role.message.submissions.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "exchange.edge.transport.role.message.submissions.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "exchange.edge.transport.role.message.submissions.per.sec", "icon": {"name": "incoming-messages", "placement": "prefix"}}, "header": {"title": "Message Submissions", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "exchange.edge.transport.role.message.submissions.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000001894, "visualization.name": "Transport Load Accessment", "visualization.description": "Microsoft Exchange Transport Load Accessment", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.edge.transport.role.sent.messages.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.edge.transport.role.received.messages.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.edge.transport.role.message.submissions.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001895, "visualization.name": "Transport Database Details", "visualization.description": "Microsoft Exchange Transport Database Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.edge.transport.role.database.reads.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.edge.transport.role.database.writes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001896, "visualization.name": "Transport DB Log Details", "visualization.description": "Microsoft Exchange Transport DB Log Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.edge.transport.role.log.writes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.edge.transport.role.log.reads.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.edge.transport.role.log.record.stalls.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001897, "visualization.name": "Transport Log Waiting Threads", "visualization.description": "Microsoft Exchange Transport Log Waiting Threads", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.edge.transport.role.log.waiting.threads", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001898, "visualization.name": "Transport Message Resubmission Latency", "visualization.description": "Microsoft Exchange Transport Message Resubmission Latency", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.edge.transport.role.message.resubmission.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001899, "visualization.name": "Transport Bytes Per Message", "visualization.description": "Microsoft Exchange Transport Bytes Per Message", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.edge.transport.role.message.bytes.per.message", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001900, "visualization.name": "Transport Mailbox Queue", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "VerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.edge.transport.role.retry.mailbox.delivery.queue.length", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "exchange.edge.transport.role.unreachable.queue.length", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "exchange.edge.transport.role.retry.non.smtp.delivery.queue.length", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "exchange.edge.transport.role.active.mailbox.delivery.queue.length", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "exchange.edge.transport.role.poison.queue.length", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "exchange.edge.transport.role.submission.queue.length", "aggregator": "avg", "entity.type": "Group", "entities": []}, {"data.point": "exchange.edge.transport.role.active.non.smtp.delivery.queue.length", "aggregator": "avg", "entity.type": "Group", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "exchange.edge.transport.role.retry.mailbox.delivery.queue.length.avg"}}}, "granularity": {"value": 5, "unit": "m"}}, {"_type": "0", "id": 10000000001901, "visualization.name": "Transport Queue Length", "visualization.description": "Microsoft Exchange Transport Queue Length", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.edge.transport.role.mailbox.queue~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.edge.transport.role.mailbox.queue~messages", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.edge.transport.role.mailbox.queue~incoming.messages", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.edge.transport.role.mailbox.queue~outgoing.messages", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "exchange.edge.transport.role.mailbox.queue", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "exchange.edge.transport.role.mailbox.queue~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "exchange.edge.transport.role.mailbox.queue~messages.last", "title": "Message", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "exchange.edge.transport.role.mailbox.queue~incoming.messages.last", "title": "Incoming Message", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "exchange.edge.transport.role.mailbox.queue~outgoing.messages.last", "title": "Outgoing Message", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}]