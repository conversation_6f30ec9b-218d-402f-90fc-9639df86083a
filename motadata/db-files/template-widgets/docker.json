[{"_type": "0", "id": 10000000002812, "visualization.name": "Containers", "visualization.description": "Containers Category", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "docker.containers", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "docker.containers"}]}], "visualization.properties": {"gauge": {"header": {"title": "Containers", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "docker.containers.last"}]}, "style": {"icon": {"name": "docker-container"}, "color.data.point": "docker.containers.last"}}}}, {"_type": "0", "id": 10000000002813, "visualization.name": "Container State", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "docker.stopped.containers", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "docker.running.containers", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "docker.stopped.containers"}, {"type": "metric", "data.point": "docker.running.containers"}]}], "visualization.properties": {"gauge": {"header": {"title": "Container State", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Running", "value": "docker.running.containers.last"}, {"label": "Stopped", "value": "docker.stopped.containers.last"}]}, "style": {"icon": {"name": "docker-container"}, "color.data.point": "docker.stopped.containers"}}}}, {"_type": "0", "id": 10000000002814, "visualization.name": "Docker Images", "visualization.description": "Docker Images Category", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "docker.images", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "docker.images"}]}], "visualization.properties": {"gauge": {"header": {"title": "Docker Images", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "docker.images.last"}]}, "style": {"icon": {"name": "image"}, "color.data.point": "docker.images.last"}}}}, {"_type": "0", "id": 10000000002815, "visualization.name": "CPU Core", "visualization.description": "CPU Core Category", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "docker.cpu.cores", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "docker.cpu.cores"}]}], "visualization.properties": {"gauge": {"header": {"title": "CPU Core", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "docker.cpu.cores.last"}]}, "style": {"icon": {"name": "cpu"}, "color.data.point": "docker.cpu.cores.last"}}}}, {"_type": "0", "id": 10000000002816, "visualization.name": "Volume", "visualization.description": "Volume Category", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "docker.volumes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "docker.volumes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Volume", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "docker.volumes.last"}]}, "style": {"icon": {"name": "database"}, "color.data.point": "docker.volumes.last"}}}}, {"_type": "0", "id": 10000000002817, "visualization.name": "Processes", "visualization.description": "Processes Category", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "docker.processes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "docker.processes"}]}], "visualization.properties": {"gauge": {"header": {"title": "Processes", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "docker.processes.last"}]}, "style": {"icon": {"name": "network"}, "color.data.point": "docker.processes.last"}}}}, {"_type": "0", "id": 10000000002818, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000002819, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000002820, "visualization.name": "Container State", "visualization.description": "Container State", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "docker.stopped.containers", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "docker.running.containers", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002821, "visualization.name": "Container Summary", "visualization.description": "Container Summary Category", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "docker.container~id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~image", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~image.tag", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~image.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~processes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~volumes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~received.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~sent.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~restarts", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "docker.container~created.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "title": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "type": "monitor", "style": {}}, {"name": "docker.container~id.last", "title": "ID", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "docker.container", "title": "CONTAINER", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "container", "style": {}}, {"name": "docker.container~status.last", "title": "STATUS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status"}, {"name": "docker.container~state.last", "title": "STATE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes"}, {"name": "docker.container~image.last", "title": "ASSOCIATED IMAGE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "alignment": "center", "style": {}}, {"name": "docker.container~image.tag.last", "title": "IMAGE TAG", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "docker.container~image.size.bytes.last", "title": "IMAGE SIZE", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "docker.container~processes.last", "title": "ASSOCIATED PROCESS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "docker.container~volumes.last", "title": "VOLUME", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "docker.container~cpu.percent.last", "title": "CPU%", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "docker.container~memory.used.byte.last", "title": "MEMORY", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "docker.container~received.bytes.last", "title": "RX BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "docker.container~sent.bytes.last", "title": "TX BYTES", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "docker.container~restarts.last", "title": "RESTART", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "docker.container~created.time.last", "title": "CREATED AT", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}, "visualization.empty.view": "no"}]