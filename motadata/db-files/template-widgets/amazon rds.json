[{"_type": "0", "id": 10000000000156, "visualization.name": "CPU", "visualization.description": "RDS CPU", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.rds.cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.rds.cpu.credit.usage", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.rds.cpu.credit.balance", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.rds.cpu.percent"}, {"type": "metric", "data.point": "aws.rds.cpu.credit.usage"}, {"type": "metric", "data.point": "aws.rds.cpu.credit.balance"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.rds.cpu.percent", "icon": {"name": "cpu", "placement": "prefix"}}, "header": {"title": "CPU", "style": {"font.size": "medium"}, "data.points": [{"label": "Credit Usage", "value": "aws.rds.cpu.credit.usage.last"}, {"label": "Credit Balance", "value": "aws.rds.cpu.credit.balance.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.rds.cpu.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000000157, "visualization.name": "Storage", "visualization.description": "RDS Storage", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.rds.storage.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.rds.allocated.storage.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.rds.storage.free.bytes"}, {"type": "metric", "data.point": "aws.rds.allocated.storage.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.rds.storage.free.bytes", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Storage", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Total", "value": "aws.rds.allocated.storage.bytes.last"}, {"label": "Free", "value": "aws.rds.storage.free.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000158, "visualization.name": "Free Memory", "visualization.description": "RDS Free Memory", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.rds.memory.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.rds.memory.free.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "memory", "placement": "prefix"}, "color.data.point": "aws.rds.memory.free.bytes"}, "header": {"title": "Free Memory", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.rds.memory.free.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000159, "visualization.name": "Connection", "visualization.description": "RDS Connection", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.rds.database.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.rds.database.connections"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.rds.database.connections", "icon": {"name": "active-connections", "placement": "prefix"}}, "header": {"title": "Connection", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.rds.database.connections.last"}]}}}}, {"_type": "0", "id": 10000000000160, "visualization.name": "IO Latency", "visualization.description": "RDS IO Latency", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.rds.disk.io.read.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.rds.disk.io.write.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.rds.disk.io.read.latency.ms"}, {"type": "metric", "data.point": "aws.rds.disk.io.write.latency.ms"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.rds.disk.io.write.latency.ms", "icon": {"name": "backup", "placement": "prefix"}}, "header": {"title": "IO Latency", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "aws.rds.disk.io.read.latency.ms.last"}, {"label": "Write", "value": "aws.rds.disk.io.write.latency.ms.last"}]}}}}, {"_type": "0", "id": 10000000000161, "visualization.name": "Network Traffic", "visualization.description": "RDS Network Traffic", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.rds.network.in.traffic.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.rds.network.out.traffic.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.rds.network.in.traffic.bytes.per.sec"}, {"type": "metric", "data.point": "aws.rds.network.out.traffic.bytes.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.rds.network.in.traffic.bytes.per.sec", "icon": {"name": "traffic", "placement": "prefix"}}, "header": {"title": "Network Traffic", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "In", "value": "aws.rds.network.in.traffic.bytes.per.sec.last"}, {"label": "Out", "value": "aws.rds.network.out.traffic.bytes.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000000162, "visualization.name": "CPU Utilization", "visualization.description": "RDS CPU Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.rds.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000163, "visualization.name": "CPU Credit Details", "visualization.description": "RDS CPU Credit Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.rds.cpu.credit.usage", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.rds.cpu.credit.balance", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000164, "visualization.name": "DB Connections", "visualization.description": "RDS DB Connections", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.rds.database.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000165, "visualization.name": "IO Latency Chart", "visualization.description": "RDS IO Latency Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.rds.disk.io.read.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.rds.disk.io.write.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000166, "visualization.name": "IO Throughput", "visualization.description": "RDS IO Throughput", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.rds.disk.io.read.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.rds.disk.io.write.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000167, "visualization.name": "IO Operation", "visualization.description": "RDS IO Operation", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.rds.disk.io.read.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.rds.disk.io.write.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000168, "visualization.name": "Available Storage", "visualization.description": "RDS Available Storage", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.rds.storage.free.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000169, "visualization.name": "Available Memory", "visualization.description": "RDS Available Memory", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.rds.memory.free.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000170, "visualization.name": "S<PERSON>p <PERSON>", "visualization.description": "RDS Swap Usage", "visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.rds.swap.memory.used.bytes", "aggregator": "avg", "entity.type": "monitor"}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}, "granularity": {"value": 5, "unit": "m"}}, {"_type": "0", "id": 10000000000171, "visualization.name": "Network Traffic", "visualization.description": "RDS Network Traffic", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.rds.network.in.traffic.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.rds.network.out.traffic.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.rds.network.traffic.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}]