[{"_type": "0", "id": 10000000002942, "visualization.name": "Nutanix VM vCPU", "visualization.description": "Nutanix VM vCPU", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.vm~cpus", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "nutanix.vm~cpus"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "", "icon": {"name": "cpu", "placement": "prefix"}}, "header": {"title": "vCPU", "style": {"font.size": "medium"}}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "nutanix.vm~cpus.last"}]}}}}, {"_type": "0", "id": 10000000002943, "visualization.name": "Nutanix VM Memory Capacity", "visualization.description": "Nutanix VM Memory Capacity", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.vm~memory.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "nutanix.vm~memory.capacity.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "Memory Capacity", "style": {"font.size": "medium"}}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "nutanix.vm~memory.capacity.bytes.last"}]}}}}, {"_type": "0", "id": 10000000002944, "visualization.name": "Nutanix Virtual Disk", "visualization.description": "Nutanix Virtual Disk", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.vm~virtual.disks", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "nutanix.vm~virtual.disks"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Virtual Disk", "style": {"font.size": "medium"}}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "nutanix.vm~virtual.disks.last"}]}}}}, {"_type": "0", "id": 10000000002945, "visualization.name": "Nutanix VM Disk Capacity", "visualization.description": "Nutanix VM Disk Capacity", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.vm~disk.capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "nutanix.vm~disk.capacity.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Disk Capacity", "style": {"font.size": "medium"}}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "nutanix.vm~disk.capacity.bytes.last"}]}}}}, {"_type": "0", "id": 10000000002946, "visualization.name": "Nutanix VM CPU Usage", "visualization.description": "Nutanix VM CPU Usage", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.vm~cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "nutanix.vm~cpu.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "CPU Usage", "style": {"font.size": "medium", "icon": "cpu"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "nutanix.vm~cpu.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "cpu"}, "color.data.point": ""}}}}, {"_type": "0", "id": 10000000002947, "visualization.name": "Nutanix VM Memory Usage", "visualization.description": "Nutanix VM Memory Usage", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.vm~memory.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "nutanix.vm~memory.used.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "Memory Usage", "style": {"font.size": "medium", "icon": "cpu"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "nutanix.vm~memory.used.percent.last", "type": "gauge"}]}, "style": {"icon": {"name": "memory"}, "color.data.point": ""}}}}, {"_type": "0", "id": 10000000002948, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.vm~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "nutanix.vm~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "nutanix.vm~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "nutanix.vm~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "nutanix.vm~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "nutanix.vm~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "nutanix.vm~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.vm~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "nutanix.vm~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "nutanix.vm~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "nutanix.vm~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "nutanix.vm~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "nutanix.vm~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "nutanix.vm~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000002949, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.vm~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.vm~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.vm~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "nutanix.vm~uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000002950, "visualization.name": "CPU vs Memory Utilization", "visualization.description": "Nutanix VM CPU vs Memory Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.vm~cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~memory.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002951, "visualization.name": "IOPS", "visualization.description": "Nutanix VM IOPS", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.vm~io.read.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~io.write.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002952, "visualization.name": "IO Latency", "visualization.description": "Nutanix VM IO Latency", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.vm~io.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002953, "visualization.name": "IO Bandwidth", "visualization.description": "Nutanix VM IO Bandwidth", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "nutanix.vm~io.read.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "nutanix.vm~io.write.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}]