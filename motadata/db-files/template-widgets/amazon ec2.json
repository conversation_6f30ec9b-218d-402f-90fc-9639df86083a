[{"_type": "0", "id": 10000000000117, "visualization.name": "vCPU", "visualization.description": "EC2 vCPU", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ec2.cpu", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.ec2.cpu"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.ec2.cpu", "icon": {"name": "cpu", "placement": "prefix"}}, "header": {"title": "vCPU", "style": {"font.size": "medium"}}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.ec2.cpu.last"}]}}}}, {"_type": "0", "id": 10000000000118, "visualization.name": "Disk IOPS", "visualization.description": "EC2 Disk IOPS", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ec2.disk.io.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.ec2.disk.io.read.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.ec2.disk.io.write.ops.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.ec2.disk.io.ops.per.sec"}, {"type": "metric", "data.point": "aws.ec2.disk.io.read.ops.per.sec"}, {"type": "metric", "data.point": "aws.ec2.disk.io.write.ops.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.ec2.disk.io.ops.per.sec", "icon": {"name": "rtt", "placement": "prefix"}}, "header": {"title": "Disk IOPS", "style": {"font.size": "medium"}, "data.points": [{"label": "Disk IOPS", "value": "aws.ec2.disk.io.ops.per.sec.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "aws.ec2.disk.io.read.ops.per.sec.last"}, {"label": "Write", "value": "aws.ec2.disk.io.write.ops.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000000119, "visualization.name": "CPU", "visualization.description": "EC2 CPU", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ec2.cpu.credit.usage", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.ec2.cpu.credit.balance", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.ec2.cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.ec2.cpu.credit.usage"}, {"type": "metric", "data.point": "aws.ec2.cpu.credit.balance"}, {"type": "metric", "data.point": "aws.ec2.cpu.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.ec2.cpu.percent", "icon": {"name": "cpu", "placement": "prefix"}}, "header": {"title": "CPU", "style": {"font.size": "medium"}, "data.points": [{"label": "Credit Usage", "value": "aws.ec2.cpu.credit.usage.last"}, {"label": "Credit Balance", "value": "aws.ec2.cpu.credit.balance.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "aws.ec2.cpu.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000000120, "visualization.name": "Network Traffic", "visualization.description": "EC2 Network Traffic", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ec2.network.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.ec2.network.in.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.ec2.network.out.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.ec2.network.bytes.per.sec"}, {"type": "metric", "data.point": "aws.ec2.network.in.bytes.per.sec"}, {"type": "metric", "data.point": "aws.ec2.network.out.bytes.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.ec2.network.bytes.per.sec", "icon": {"name": "server", "placement": "prefix"}}, "header": {"title": "Network Traffic", "style": {"font.size": "medium"}, "data.points": [{"label": "Network Bytes Per Sec", "value": "aws.ec2.network.bytes.per.sec.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "IN", "value": "aws.ec2.network.in.bytes.per.sec.last"}, {"label": "OUT", "value": "aws.ec2.network.out.bytes.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000000121, "visualization.name": "Disk Throughput", "visualization.description": "EC2 Disk Throughput", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ec2.disk.io.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.ec2.disk.io.read.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "aws.ec2.disk.io.write.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.ec2.disk.io.bytes.per.sec"}, {"type": "metric", "data.point": "aws.ec2.disk.io.read.bytes.per.sec"}, {"type": "metric", "data.point": "aws.ec2.disk.io.write.bytes.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.ec2.disk.io.bytes.per.sec", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Disk Throughput", "style": {"font.size": "medium"}, "data.points": [{"label": "Disk IO Bytes Per Sec", "value": "aws.ec2.disk.io.bytes.per.sec.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "aws.ec2.disk.io.read.bytes.per.sec.last"}, {"label": "Write", "value": "aws.ec2.disk.io.write.bytes.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000000122, "visualization.name": "Status Fail", "visualization.description": "EC2 Status Fail", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ec2.status.check.failed.instance", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "aws.ec2.status.check.failed.instance"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "aws.ec2.status.check.failed.instance", "icon": {"name": "chart-line", "placement": "prefix"}}, "header": {"title": "Status Fail", "style": {"font.size": "medium"}}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Fail", "value": "aws.ec2.status.check.failed.instance.last"}]}}}}, {"_type": "0", "id": 10000000000123, "visualization.name": "CPU Utilization", "visualization.description": "EC2 CPU Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ec2.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000124, "visualization.name": "Surplus Credit Details", "visualization.description": "EC2 Surplus Credit Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ec2.cpu.surplus.credit.balance", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.ec2.cpu.surplus.credit.charged", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000125, "visualization.name": "Disk Throughput", "visualization.description": "EC2 Disk Throughput Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ec2.disk.io.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.ec2.disk.io.write.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.ec2.disk.io.read.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000126, "visualization.name": "Disk IOPS", "visualization.description": "EC2 Disk IOPS Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ec2.disk.io.write.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.ec2.disk.io.read.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000127, "visualization.name": "Network Traffic", "visualization.description": "EC2 Network Traffic Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ec2.network.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.ec2.network.out.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.ec2.network.in.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000128, "visualization.name": "Network Packets", "visualization.description": "EC2 Network Packets", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "aws.ec2.network.in.packets", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "aws.ec2.network.out.packets", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}]