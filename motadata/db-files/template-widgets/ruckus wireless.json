[{"_type": "0", "id": 10000000000993, "visualization.name": "Ruckus Access Points", "visualization.description": "Ruckus Access Points", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.access.points", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.disconnected.access.points", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ruckus.wireless.access.points"}, {"type": "metric", "data.point": "ruckus.wireless.disconnected.access.points"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "ruckus.wireless.access.points", "icon": {"name": "router", "placement": "prefix"}}, "header": {"title": "Access Points", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Total", "value": "ruckus.wireless.access.points.last"}, {"label": "Disconnected", "value": "ruckus.wireless.disconnected.access.points.last"}]}}}}, {"_type": "0", "id": 10000000000994, "visualization.name": "Ruckus Rogues", "visualization.description": "Ruckus Rogues", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.rogue.access.points", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ruckus.wireless.rogue.access.points"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "ruckus.wireless.rogue.access.points", "icon": {"name": "cloud-wifi", "placement": "prefix"}}, "header": {"title": "Rogue APs", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "ruckus.wireless.rogue.access.points.last"}]}}}}, {"_type": "0", "id": 10000000000995, "visualization.name": "Ruckus Clients", "visualization.description": "Ruckus Clients", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.clients", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.warning.clients", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ruckus.wireless.clients"}, {"type": "metric", "data.point": "ruckus.wireless.warning.clients"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "ruckus.wireless.warning.clients", "icon": {"name": "users", "placement": "prefix"}}, "header": {"title": "Clients", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Total", "value": "ruckus.wireless.clients.last"}, {"label": "Warning", "value": "ruckus.wireless.warning.clients.last"}]}}}}, {"_type": "0", "id": 10000000000996, "visualization.name": "Ruckus WLAN", "visualization.description": "Ruckus WLAN", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.active.wlans", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.inactive.wlans", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ruckus.wireless.active.wlans"}, {"type": "metric", "data.point": "ruckus.wireless.inactive.wlans"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "ruckus.wireless.inactive.wlans", "icon": {"name": "wifi", "placement": "prefix"}}, "header": {"title": "WLAN", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Active", "value": "ruckus.wireless.active.wlans.last"}, {"label": "Inactive", "value": "ruckus.wireless.inactive.wlans.last"}]}}}}, {"_type": "0", "id": 10000000000997, "visualization.name": "Ruckus Response Time", "visualization.description": "Ruckus Response Time", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ping.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ping.latency.ms"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "ping.latency.ms", "icon": {"name": "response-time", "placement": "prefix"}}, "header": {"title": "Response Time", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "ping.latency.ms.last"}]}}}}, {"_type": "0", "id": 10000000000998, "visualization.name": "Ruckus Packet Lost", "visualization.description": "Ruckus Packet Lost", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ping.packet.lost.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ping.packet.lost.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "ping.packet.lost.percent", "icon": {"name": "rejected-connections", "placement": "prefix"}}, "header": {"title": "Packet Lost", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "ping.packet.lost.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000000999, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000001000, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000001001, "visualization.name": "Ruckus Active/Inactive WLANs", "visualization.description": "Ruckus Active/Inactive WLANs", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.active.wlans", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.inactive.wlans", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001002, "visualization.name": "Ruckus Top Access Points By Clients Count", "visualization.description": "Ruckus Top Access Points By Clients Count", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.access.point~clients", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "ruckus.wireless.access.point", "title": "Access Point", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "ruckus.wireless.access.point~clients.last", "title": "Total Clients", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}], "sorting": {"limit": "5", "order": "desc", "column": "ruckus.wireless.access.point~clients.last"}}}}, {"_type": "0", "id": 10000000001003, "visualization.name": "Ruckus Top Clients By Packets", "visualization.description": "Ruckus Top Clients By Packets", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.client~packets", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.client~packets", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "ruckus.wireless.client", "title": "Client", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "ruckus.wireless.client~packets.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"inline.chart": {"type": "sparkline"}}}, {"name": "ruckus.wireless.client~packets.avg", "title": "Total Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "type": "number", "style": {}}], "sorting": {"limit": "5", "order": "desc", "column": "ruckus.wireless.client~packets.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000001004, "visualization.name": "Ruckus Top Wireless Clients By Traffic", "visualization.description": "Ruckus Top Wireless Clients By Traffic", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.client~traffic.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.client~traffic.bytes", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "ruckus.wireless.client", "title": "Clients", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "ruckus.wireless.client~traffic.bytes.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"inline.chart": {"type": "sparkline"}}}, {"name": "ruckus.wireless.client~traffic.bytes.avg", "title": "Total Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}], "sorting": {"limit": "5", "order": "desc", "column": "ruckus.wireless.client~traffic.bytes.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000001005, "visualization.name": "Ruckus Interface Details", "visualization.description": "Ruckus Interface Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~in.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~out.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~sent.octets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~received.octets", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "interface", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "interface~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold"]}}, {"name": "interface~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 3}, {"name": "Packets", "title": "Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "position": 4, "formula": {"operation": "combine", "columns": ["interface~in.packets.last", "interface~out.packets.last"]}, "style": {"classes": ["font-bold"], "icon": {"name": "traffic", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "interface~in.packets.last", "title": "In Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 5, "style": {"icon": {"name": "long-arrow-right", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "interface~out.packets.last", "title": "Out Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 6, "style": {"icon": {"name": "long-arrow-left", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "Octets", "title": "Octets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "position": 7, "formula": {"operation": "combine", "columns": ["interface~sent.octets.last", "interface~received.octets.last"]}, "style": {"classes": ["font-bold"], "icon": {"name": "traffic", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "interface~sent.octets.last", "title": "Sent Octets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "type": "number", "style": {"icon": {"name": "long-arrow-left", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "interface~received.octets.last", "title": "Received Octets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "type": "number", "style": {"icon": {"name": "long-arrow-right", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "interface~address.last", "title": "MAC Address", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10, "style": {"classes": ["font-bold"], "icon": {"name": "desktop", "placement": "prefix", "classes": ["text-secondary-red"]}}}]}}}, {"_type": "0", "id": 10000000002243, "visualization.name": "Ruckus Access Point", "visualization.description": "Ruckus Access Point Grid", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.access.point~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~mac.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~started.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~clients", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~sent.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.access.point~received.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "ruckus.wireless.access.point", "title": "Access Point", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold"]}}, {"name": "ruckus.wireless.access.point~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 3, "style": {"classes": ["font-bold"], "icon": {"placement": "prefix", "conditions": [{"icon": "long-arrow-up", "operator": "=", "value": "Connected"}, {"icon": "long-arrow-down", "operator": "!=", "value": "Connected"}]}}}, {"name": "ruckus.wireless.access.point~ip.address.last", "title": "IP Address", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"classes": ["font-bold"], "icon": {"name": "map-marker-alt", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "ruckus.wireless.access.point~clients.avg", "title": "Clients", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"classes": ["font-bold"], "icon": {"name": "user-friends", "placement": "prefix", "classes": ["text-primary"]}}}, {"name": "Traffic", "title": "Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "position": 7, "formula": {"operation": "combine", "columns": ["ruckus.wireless.access.point~sent.bytes.per.sec.avg", "ruckus.wireless.access.point~received.bytes.per.sec.avg"]}, "style": {"classes": ["font-bold"], "icon": {"name": "traffic", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "ruckus.wireless.access.point~sent.bytes.per.sec.avg", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {"icon": {"name": "long-arrow-left", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "ruckus.wireless.access.point~received.bytes.per.sec.avg", "title": "Received Bytes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"icon": {"name": "long-arrow-right", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "ruckus.wireless.access.point~mac.address.last", "title": "MAC Address", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10, "style": {"classes": ["font-bold"], "icon": {"name": "desktop", "placement": "prefix", "classes": ["text-secondary-red "]}}}, {"name": "ruckus.wireless.access.point~started.time.last", "title": "Uptime", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11, "style": {"classes": ["font-bold"]}}, {"name": "ruckus.wireless.access.point~cpu.percent.last", "title": "CPU(%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12, "style": {"classes": ["font-bold"]}}]}}}, {"_type": "0", "id": 10000000002244, "visualization.name": "Ruckus WLAN Summary", "visualization.description": "Ruckus WLAN Summary", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ruckus.wireless.wlan~clients", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.wlan~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.wlan~encryption", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.wlan~traffic.received.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.wlan~traffic.sent.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.wlan~sent.packets.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ruckus.wireless.wlan~received.packets.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "ruckus.wireless.wlan", "title": "WLAN", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold"]}}, {"name": "ruckus.wireless.wlan~clients.last", "title": "Clients", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"classes": ["font-bold"]}, "icon": {"name": "user-circle", "placement": "prefix", "classes": ["text-secondary-green"]}}, {"name": "ruckus.wireless.wlan~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "type": "status"}, {"name": "ruckus.wireless.wlan~encryption.last", "title": "Encryption", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"classes": ["font-bold"], "icon": {"name": "encryption", "placement": "prefix", "classes": ["text-secondary-yellow"]}}}, {"name": "Traffic Packets", "title": "Traffic (Packets)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "position": 6, "formula": {"operation": "combine", "columns": ["ruckus.wireless.wlan~sent.packets.per.sec.last", "ruckus.wireless.wlan~received.packets.per.sec.last"]}, "style": {"classes": ["font-bold"], "icon": {"name": "traffic", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "ruckus.wireless.wlan~sent.packets.per.sec.last", "title": "Sent Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 7, "style": {"icon": {"name": "long-arrow-left", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "ruckus.wireless.wlan~received.packets.per.sec.last", "title": "Received Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 8, "style": {"icon": {"name": "long-arrow-right", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "ruckus.wireless.wlan~traffic.received.bytes.per.sec.last", "title": "Received Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 9, "style": {"icon": {"name": "long-arrow-right", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "ruckus.wireless.wlan~traffic.sent.bytes.per.sec.last", "title": "Sent Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10, "style": {"icon": {"name": "long-arrow-left", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "Traffic Bytes", "title": "Traffic (Bytes)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "position": 11, "formula": {"operation": "combine", "columns": ["ruckus.wireless.wlan~traffic.sent.bytes.per.sec.last", "ruckus.wireless.wlan~traffic.received.bytes.per.sec.last"]}}]}}}, {"id": 10000000001008, "visualization.name": "Ruckus Rogue Device", "visualization.description": "Ruckus Rogue Device", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["ruckus.wireless.rogue.access.point"], "data.points": [{"data.point": "ruckus.wireless.rogue.access.point~status", "aggregator": "last"}, {"data.point": "ruckus.wireless.rogue.access.point~ruckus.wireless.rogue.type", "aggregator": "last"}, {"data.point": "ruckus.wireless.rogue.access.point~interface.type", "aggregator": "last"}, {"data.point": "ruckus.wireless.rogue.access.point~encryption", "aggregator": "last"}, {"data.point": "ruckus.wireless.rogue.access.point~channel", "aggregator": "last"}, {"data.point": "ruckus.wireless.rogue.access.point~last.detected", "aggregator": "last"}, {"data.point": "ruckus.wireless.rogue.access.point~mac.address", "aggregator": "last"}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "ruckus.wireless.rogue.access.point", "title": "SSID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "ruckus.wireless.rogue.access.point~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 3, "style": {}}, {"name": "ruckus.wireless.rogue.access.point~ruckus.wireless.rogue.type.last", "title": "Device Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "ruckus.wireless.rogue.access.point~interface.type.last", "title": "Radio Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "ruckus.wireless.rogue.access.point~encryption.last", "title": "Encryption", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "ruckus.wireless.rogue.access.point~channel.last", "title": "Channel", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "label", "position": 7, "style": {}}, {"name": "ruckus.wireless.rogue.access.point~last.detected.last", "title": "Last Detected", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}, {"name": "ruckus.wireless.rogue.access.point~mac.address.last", "title": "MAC Address", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {}}]}}, "visualization.result.by": ["ruckus.wireless.rogue.access.point"], "granularity": {"value": 5, "unit": "m"}}]