[{"_type": "0", "id": 10000000000973, "visualization.name": "Cisco Access Points", "visualization.description": "Cisco Access Points", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.access.points", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.wireless.access.points"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "cisco.wireless.access.points", "icon": {"name": "router", "placement": "prefix"}}, "header": {"title": "Access Points", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "cisco.wireless.access.points.last"}]}}}}, {"_type": "0", "id": 10000000000974, "visualization.name": "Cisco Rogues", "visualization.description": "Cisco Rogues", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.rogue.clients", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.rogue.access.points", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.wireless.rogue.clients"}, {"type": "metric", "data.point": "cisco.wireless.rogue.access.points"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "cisco.wireless.rogue.access.points", "icon": {"name": "cloud-wifi", "placement": "prefix"}}, "header": {"title": "Rogues", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "APs", "value": "cisco.wireless.rogue.access.points.last"}, {"label": "Clients", "value": "cisco.wireless.rogue.clients.last"}]}}}}, {"_type": "0", "id": 10000000000975, "visualization.name": "Cisco Active Clients", "visualization.description": "Cisco Active Clients", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.clients", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.wireless.access.points"}, {"type": "metric", "data.point": "cisco.wireless.clients"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "cisco.wireless.clients", "icon": {"name": "users", "placement": "prefix"}}, "header": {"title": "Active Clients", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "cisco.wireless.clients.last"}]}}}}, {"_type": "0", "id": 10000000000976, "visualization.name": "Cisco Memory", "visualization.description": "Cisco Memory", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.controller.memory.free.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.controller.memory.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.wireless.controller.memory.free.bytes"}, {"type": "metric", "data.point": "cisco.wireless.controller.memory.used.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "cisco.wireless.controller.memory.used.bytes", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "Memory", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Free", "value": "cisco.wireless.controller.memory.free.bytes.last"}, {"label": "Used", "value": "cisco.wireless.controller.memory.used.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000977, "visualization.name": "Cisco Response Time", "visualization.description": "Cisco Response Time", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ping.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ping.latency.ms"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "ping.latency.ms", "icon": {"name": "response-time", "placement": "prefix"}}, "header": {"title": "Response Time", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "ping.latency.ms.last"}]}}}}, {"_type": "0", "id": 10000000000978, "visualization.name": "Cisco Packet Lost", "visualization.description": "Cisco Packet Lost", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ping.packet.lost.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ping.packet.lost.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "ping.packet.lost.percent", "icon": {"name": "rejected-connections", "placement": "prefix"}}, "header": {"title": "Packet Lost", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "ping.packet.lost.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000000979, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000980, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000000981, "visualization.name": "Cisco CPU/Memory Utilization", "visualization.description": "Cisco CPU/Memory Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.controller.cpu.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.controller.memory.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000982, "visualization.name": "Cisco Top Clients By Packets", "visualization.description": "Cisco Top Clients By Packets", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.client~packets.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~packets.per.sec", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "cisco.wireless.client", "title": "Clients", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "cisco.wireless.client~packets.per.sec.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"inline.chart": {"type": "sparkline"}}}, {"name": "cisco.wireless.client~packets.per.sec.avg", "title": "Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}], "sorting": {"limit": "5", "order": "desc", "column": "cisco.wireless.client~packets.per.sec.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000000983, "visualization.name": "Cisco Top Access Points By Clients Count", "visualization.description": "Cisco Top Access Points By Clients Count", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.access.point~clients", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "cisco.wireless.access.point", "title": "Access Point", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "cisco.wireless.access.point~clients.avg", "title": "Client Count", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}], "sorting": {"limit": "5", "order": "desc", "column": "cisco.wireless.access.point~clients.avg"}}}}, {"_type": "0", "id": 10000000000984, "visualization.name": "Cisco Top Wireless Client By Traffic", "visualization.description": "Cisco Top Wireless Client By Traffic", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.client~traffic.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.client~traffic.bytes.per.sec", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "cisco.wireless.client", "title": "Clients", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "cisco.wireless.client~traffic.bytes.per.sec.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"inline.chart": {"type": "sparkline"}}}, {"name": "cisco.wireless.client~traffic.bytes.per.sec.avg", "title": "Total Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}], "sorting": {"limit": "5", "order": "desc", "column": "cisco.wireless.client~traffic.bytes.per.sec.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000000985, "visualization.name": "Cisco Interface Details", "visualization.description": "Cisco Interface Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}, "visualization.result.by": ["interface"]}, "data.points": [{"data.point": "interface~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~in.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~out.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~sent.octets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~received.octets", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small", "css.classes": [], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold"]}}, {"name": "interface~name.last", "title": "Interface Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"classes": ["font-bold"]}}, {"name": "interface~status.last", "title": "Operational Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "type": "status", "style": {"classes": ["font-bold"], "icon": {"name": "", "placement": "prefix", "conditions": [{"icon": "long-arrow-up", "operator": "=", "value": "up"}, {"icon": "long-arrow-down", "operator": "=", "value": "down"}]}}}, {"name": "interface~address.last", "title": "Interface Address", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"classes": ["font-bold"], "icon": {"name": "user-friends", "placement": "prefix", "classes": ["text-primary"]}}}, {"name": "Packets", "title": "Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "position": 6, "type": "number", "formula": {"operation": "combine", "columns": ["interface~in.packets.last", "interface~out.packets.last"]}, "style": {"classes": ["font-bold"], "icon": {"name": "traffic", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "interface~in.packets.last", "title": "In Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 7, "style": {"icon": {"name": "long-arrow-right", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "interface~out.packets.last", "title": "Out Packets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 8, "style": {"icon": {"name": "long-arrow-left", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "Octets", "title": "Octets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "position": 9, "formula": {"operation": "combine", "columns": ["interface~sent.octets.last", "interface~received.octets.last"]}, "style": {"classes": ["font-bold"], "icon": {"name": "traffic", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "interface~sent.octets.last", "title": "Sent Octets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 10, "style": {"icon": {"name": "long-arrow-left", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "interface~received.octets.last", "title": "Received Octets", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 11, "style": {"icon": {"name": "long-arrow-right", "placement": "prefix", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}]}}}, {"_type": "0", "id": 10000000000986, "visualization.name": "Cisco WLAN Summary", "visualization.description": "Cisco WLAN Summary", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.wlan~id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.wlan~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.wlan~interface.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.wlan~clients", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small", "css.classes": [], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "cisco.wireless.wlan", "title": "SSID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold"]}}, {"name": "cisco.wireless.wlan~id.last", "title": "Wlan <PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"classes": ["font-bold"]}}, {"name": "cisco.wireless.wlan~status.last", "title": "Admin Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "type": "status", "style": {"classes": ["font-bold"], "icon": {"name": "cisco.wireless.wlan~status", "placement": "prefix", "conditions": [{"icon": "check-circle", "operator": "=", "value": "enable"}, {"icon": "times-circle", "operator": "=", "value": "disable"}]}}}, {"name": "cisco.wireless.wlan~interface.name.last", "title": "Interface Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"classes": ["font-bold"], "icon": {"name": "port", "placement": "prefix", "classes": ["text-secondary-orange"]}}}, {"name": "cisco.wireless.wlan~clients.last", "title": "Interface Clients", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"classes": ["font-bold"]}, "icon": {"name": "user-circle", "placement": "prefix", "classes": ["text-secondary-green"]}}]}}}, {"_type": "0", "id": 10000000000987, "visualization.name": "Cisco Access Point", "visualization.description": "Cisco Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.access.point~admin.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~clients", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~slots", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~mac.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~operational.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~started.time.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.access.point~serial.number", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small", "css.classes": [], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "cisco.wireless.access.point", "title": "Access Point", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold"]}}, {"name": "cisco.wireless.access.point~admin.status.last", "title": "Admin Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 3}, {"name": "cisco.wireless.access.point~ip.address.last", "title": "IP Address", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"classes": ["font-bold"], "icon": {"name": "map-marker-alt", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "cisco.wireless.access.point~clients.last", "title": "Clients", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"classes": ["font-bold"], "icon": {"name": "user-friends", "placement": "prefix", "classes": ["text-primary"]}}}, {"name": "cisco.wireless.access.point~slots.last", "title": "Slots", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"classes": ["font-bold"], "icon": {"name": "port", "placement": "prefix", "classes": ["text-secondary-orange"]}}}, {"name": "cisco.wireless.access.point~mac.address.last", "title": "MAC Address", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {"classes": ["font-bold"], "icon": {"name": "desktop", "placement": "prefix", "classes": ["text-secondary-red"]}}}, {"name": "cisco.wireless.access.point~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 8}, {"name": "cisco.wireless.access.point~operational.status.last", "title": "Operational Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"icon": {"name": "check-circle", "placement": "prefix", "conditions": [{"icon": "check-circle", "operator": "=", "value": "Associated"}, {"icon": "times-circle", "operator": "=", "value": "Dissociated"}]}}}, {"name": "cisco.wireless.access.point~started.time.seconds.last", "title": "Uptime", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10, "style": {"classes": ["font-bold"]}}, {"name": "cisco.wireless.access.point~serial.number.last", "title": "Serial Number", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11, "style": {"classes": ["font-bold"]}}]}}}, {"_type": "0", "id": 10000000000991, "visualization.name": "Cisco Rogue Access Point", "visualization.description": "Cisco Rogue Access Point", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.rogue.access.point~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.rogue.access.point~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.rogue.access.point~clients", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.rogue.access.point~class.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.rogue.access.point~channel", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.rogue.access.point~mac.address", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small", "css.classes": [], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "cisco.wireless.rogue.access.point~name.last", "title": "Access Point Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-semibold", "text-primary"]}}, {"name": "cisco.wireless.rogue.access.point~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"classes": ["font-semibold"]}}, {"name": "cisco.wireless.rogue.access.point~clients.last", "title": "Clients", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"classes": ["font-semibold"], "icon": {"name": "user", "placement": "prefix", "classes": ["text-primary"]}}}, {"name": "cisco.wireless.rogue.access.point", "title": "SSID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"classes": ["font-semibold"], "icon": {"name": "user-friends", "placement": "prefix", "classes": ["text-primary"]}}}, {"name": "cisco.wireless.rogue.access.point~class.type.last", "title": "Class Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"classes": ["font-semibold"], "icon": {"name": "file-edit", "placement": "prefix", "classes": ["text-secondary-orange"]}}}, {"name": "cisco.wireless.rogue.access.point~channel.last", "title": "Channel", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "label", "position": 7, "style": {"classes": ["font-semibold"], "icon": {"name": "network", "placement": "prefix", "classes": ["text-secondary-yellow"]}}}, {"name": "cisco.wireless.rogue.access.point~mac.address.last", "title": "MAC Address", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {"classes": ["font-semibold"], "icon": {"name": "desktop", "placement": "prefix", "classes": ["text-secondary-red"]}}}]}}}, {"_type": "0", "id": 10000000000992, "visualization.name": "Cisco Rogue Client", "visualization.description": "Cisco Rogue Client", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.wireless.rogue.client~last.detected", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.rogue.client~channels", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.rogue.client~ssid", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.rogue.client~slot.id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.wireless.rogue.client~ap.interface", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small", "css.classes": [], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "cisco.wireless.rogue.client", "title": "<PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-semibold", "text-primary"]}}, {"name": "cisco.wireless.rogue.client~ssid.last", "title": "SSID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"classes": ["font-semibold"], "icon": {"name": "user-friends", "placement": "prefix", "classes": ["text-primary"]}}}, {"name": "cisco.wireless.rogue.client~channels.last", "title": "Channels", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"classes": ["font-semibold"]}}, {"name": "cisco.wireless.rogue.client~slot.id.last", "title": "Slot ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"classes": ["font-semibold"]}}, {"name": "cisco.wireless.rogue.client~ap.interface.last", "title": "AP Interface", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"classes": ["font-semibold"]}}, {"name": "cisco.wireless.rogue.client~last.detected.last", "title": "Last Detected", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"classes": ["font-semibold"], "icon": {"name": "history", "placement": "prefix", "classes": ["text-secondary-orange"]}}}]}}}]