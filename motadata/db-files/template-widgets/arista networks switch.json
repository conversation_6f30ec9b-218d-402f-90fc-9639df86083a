[{"_type": "0", "id": 10000000002028, "visualization.name": "Interface Status Count", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Gauge", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~up.count", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~down.count", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "interface~up.count"}, {"type": "metric", "data.point": "interface~down.count"}]}], "visualization.properties": {"gauge": {"header": {"title": "Interface", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Up", "value": "up"}, {"label": "Down", "value": "down"}]}, "style": {"icon": {"name": "interface"}, "color.data.point": "interface~up.count"}}}}, {"_type": "0", "id": 10000000002029, "visualization.name": "Latency", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ping.max.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ping.min.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ping.max.latency.ms"}, {"type": "metric", "data.point": "ping.min.latency.ms"}]}], "visualization.properties": {"gauge": {"header": {"title": "Latency", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Max", "value": "ping.max.latency.ms.last"}, {"label": "Min", "value": "ping.min.latency.ms.last"}]}, "style": {"icon": {"name": "backup"}, "color.data.point": "ping.min.latency.ms.last"}}}}, {"_type": "0", "id": 10000000002030, "visualization.name": "Packet Lost", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ping.received.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ping.sent.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ping.latency.ms"}, {"type": "metric", "data.point": "ping.lost.packets"}, {"type": "metric", "data.point": "ping.received.packets"}, {"type": "metric", "data.point": "ping.sent.packets"}]}], "visualization.properties": {"gauge": {"style": {"icon": {"name": "rejected-connections", "placement": "prefix"}, "font.size": "medium", "color.data.point": "ping.received.packets"}, "header": {"title": "Packet Lost", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Received", "value": "ping.received.packets.last"}, {"label": "Transmitted", "value": "ping.sent.packets.last"}]}}}}, {"_type": "0", "id": 10000000002031, "visualization.name": "Response Time", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ping.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "ping.lost.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "ping.latency.ms"}, {"type": "metric", "data.point": "ping.max.latency.ms"}, {"type": "metric", "data.point": "ping.min.latency.ms"}, {"type": "metric", "data.point": "ping.lost.packets"}]}], "visualization.properties": {"gauge": {"header": {"title": "Response Time", "style": {"font.size": "medium"}, "data.points": [{"label": "Lost Packets", "value": "ping.lost.packets.last"}]}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "ping.latency.ms.last"}]}, "style": {"icon": {"name": "response-time"}, "color.data.point": "ping.latency.ms.last"}}}}, {"_type": "0", "id": 10000000002032, "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.name": "CPU (%)", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "system.cpu.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "CPU", "style": {"font.size": "medium", "icon": "cpu"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "system.cpu.percent.last", "type": "gauge"}]}, "style": {"type": "number", "font.size": "small", "color.conditions": [{"color": "#f04e3e"}, {"color": "#f58518"}, {"color": "#f5bc18"}], "color.data.point": "system.cpu.percent", "icon": {"name": "cpu"}}}}}, {"_type": "0", "id": 10000000002033, "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.name": "Memory Used (%)", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.memory.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "system.memory.used.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "Memory", "style": {"font.size": "medium", "icon": "memory"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "system.memory.used.percent.last", "type": "gauge"}]}, "style": {"type": "number", "font.size": "small", "color.conditions": [{"color": "#f04e3e"}, {"color": "#f58518"}, {"color": "#f5bc18"}], "color.data.point": "system.memory.used.percent", "icon": {"name": "memory"}}}}}, {"_type": "0", "id": 10000000002034, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000002035, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000002036, "visualization.name": "Response Time vs Packet Lost", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ping.packet.lost.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ping.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002037, "visualization.name": "Top 10 Interfaces By Average Traffic", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~traffic.bits.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~in.traffic.utilization.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~out.traffic.utilization.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}, "type": "interface"}, {"name": "interface~traffic.bits.per.sec.avg", "title": "Total Traffic", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "interface~in.traffic.utilization.percent.avg", "title": "IN (%)", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "gauge", "classes": ["success"]}}}, {"name": "interface~out.traffic.utilization.percent.avg", "title": "OUT (%)", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "gauge"}}}], "sorting": {"limit": 10, "order": "desc", "column": "interface~traffic.bits.per.sec.avg"}}}}, {"_type": "0", "id": 10000000002038, "visualization.name": "Top 10 Interfaces By Average Error Packets", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~error.packets", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~error.packets", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1}, {"name": "interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}, "type": "interface"}, {"name": "interface~error.packets.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 2}, {"name": "interface~error.packets.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "interface~error.packets.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000002039, "visualization.name": "Top 10 Interfaces By Average Discard Packets", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~discard.packets", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "interface~discard.packets", "aggregator": "sparkline", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}, "type": "interface"}, {"name": "interface~discard.packets.avg", "title": "Value", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "style": {}}, {"name": "interface~discard.packets.sparkline", "title": " ", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "sparkline"}}}], "sorting": {"limit": 10, "order": "desc", "column": "interface~discard.packets.avg"}}, "sparkline": "yes"}}, {"_type": "0", "id": 10000000002040, "visualization.name": "VLAN Details", "visualization.category": "Grid", "visualization.type": "VLAN", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "vlan~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "vlan~port", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "vlan~ports", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "vlan~status", "aggregator": "last", "entity.type": "monitor", "entities": []}]}, {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~index", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~in.traffic.bits.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~out.traffic.bits.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "no", "column.selection": "no", "header": "yes", "view": "vlan", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor.last", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 1}, {"name": "vlan", "title": "VLAN", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold"], "width.percent": 5}}, {"name": "vlan~name.last", "title": "VLAN Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"icon": {"name": "topology", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "vlan~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "type": "status", "style": {"icon": {"placement": "prefix", "conditions": [{"value": "Active", "operator": "=", "icon": "check-circle"}, {"value": "Inactive", "operator": "=", "icon": "times-circle"}]}, "classes": ["font-bold"]}}, {"name": "vlan~ports.last", "title": "Port Count", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "vlan~port.last", "title": "Port", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 5, "type": "port", "style": {"classes": ["font-bold"]}}, {"name": "traffic", "title": "Traffic", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "computed": "yes", "formula": {"operation": "combine", "columns": ["interface~in.traffic.bits.per.sec.last", "interface~out.traffic.bits.per.sec.last"]}, "style": {"icon": {"name": "traffic", "classes": ["text-neutral-light"]}, "classes": ["font-bold"]}}, {"name": "interface~in.traffic.bits.per.sec.last", "title": "Traffic", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 8, "style": {"icon": {"name": "long-arrow-right", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "interface~out.traffic.bits.per.sec.last", "title": "Traffic", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 9, "style": {"icon": {"name": "long-arrow-left", "classes": ["text-neutral-light"]}}}, {"name": "monitor", "show": "no", "sortable": "no", "disable": "yes", "resizable": "no", "selectable": "no", "orderable": "no", "position": 10}]}}}, {"_type": "0", "id": 10000000002042, "visualization.name": "Interface Switch PortView", "visualization.category": "Grid", "visualization.type": "Port View", "join.type": "custom", "join.result": "port.view", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "join.type": "custom", "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "interface~type^last", "operator": "in", "value": ["ethernetCsmacd (6)", "gigabitEthernet (117)"]}]}]}, "result.filter": {}}, "data.points": [{"data.point": "interface~index", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~status", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": []}}}, {"_type": "0", "id": 10000000002043, "visualization.name": "Interface Details", "visualization.category": "Grid", "visualization.type": "Interface", "publish.sub.query.progress": false, "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~index", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~alias", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~discard.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~error.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~traffic.utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~out.traffic.utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~in.traffic.utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~speed.bits.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~description", "aggregator": "last", "entity.type": "monitor", "entities": []}]}, {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "vlan~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "vlan~port", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "yes", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 1}, {"name": "interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 2, "type": "interface", "style": {}}, {"name": "interface~alias.last", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"classes": ["font-bold"]}}, {"name": "interface~ip.address.last", "title": "Interface IP", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"]}}, {"name": "interface~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "type": "status", "formula": {"conditions": [{"result": "Up", "operator": "=", "value": "up"}, {"result": "Down", "operator": "=", "value": "down"}]}}, {"name": "interface~traffic.utilization.percent.last", "title": "Traffic Utilization", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"classes": ["font-bold"], "inline.chart": {"type": "gauge"}}}, {"name": "interface~in.traffic.utilization.percent.last", "title": "IN (%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {"classes": ["font-bold"], "inline.chart": {"type": "gauge"}}}, {"name": "interface~out.traffic.utilization.percent.last", "title": "OUT (%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {"classes": ["font-bold"], "inline.chart": {"type": "gauge"}}}, {"name": "interface~type.last", "title": "Port Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"classes": ["font-bold"], "icon": {"name": "port", "classes": ["text-secondary-orange"]}}}, {"name": "interface~error.packets.last", "title": "Error", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 10, "style": {"classes": ["font-bold"], "icon": {"name": "times-circle", "classes": ["text-secondary-red"]}}}, {"name": "interface~discard.packets.last", "title": "Discarded", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 11, "style": {"classes": ["font-bold"], "icon": {"name": "trash", "classes": ["text-secondary-orange"]}}}, {"name": "interface~index.last", "title": "Interface Index", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12, "style": {}}, {"name": "interface~name.last", "title": "Interface Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "vlan", "title": "VLAN", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 13, "type": "interface", "style": {}}, {"name": "vlan~name.last", "title": "Assigned VLAN", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 14, "style": {"classes": ["font-bold"], "icon": {"name": "topology", "classes": []}}}, {"name": "vlan~port.last", "title": "VLAN Port", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "no", "orderable": "no", "position": 15, "style": {}}, {"name": "interface~speed.bits.per.sec.last", "title": "Speed", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 16, "style": {}}, {"name": "interface~description.last", "title": "Description", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 17, "style": {}}]}}}]