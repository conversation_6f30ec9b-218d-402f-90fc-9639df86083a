[{"id": 10000000000714, "_type": "0", "visualization.name": "Processor", "visualization.description": "SAP HANA Processor", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.hana.processor.utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.available.processors", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.used.processors", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "system.cpu.percent"}, {"type": "metric", "data.point": "system.cpu.user.percent"}, {"type": "metric", "data.point": "system.cpu.interrupt.percent"}, {"type": "metric", "data.point": "sap.hana.processor.utilization.percent"}, {"type": "metric", "data.point": "sap.hana.available.processors"}, {"type": "metric", "data.point": "sap.hana.used.processors"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "sap.hana.processor.utilization.percent", "icon": {"name": "processor", "placement": "prefix"}}, "header": {"title": "Processor", "style": {"font.size": "medium"}, "data.points": [{"label": "processor.utilization.percent", "value": "sap.hana.processor.utilization.percent.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Available", "value": "sap.hana.available.processors.last"}, {"label": "Used", "value": "sap.hana.used.processors.last"}]}}}}, {"id": 10000000000715, "_type": "0", "visualization.name": "Memory", "visualization.description": "SAP HANA Memory", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.hana.memory.provisioned.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.memory.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "sap.hana.swap.memory.bytes"}, {"type": "metric", "data.point": "sap.hana.memory.used.bytes"}, {"type": "metric", "data.point": "sap.hana.memory.provisioned.bytes"}, {"type": "metric", "data.point": "sap.hana.memory.used.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "sap.hana.memory.used.percent", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "Memory", "style": {"font.size": "medium"}, "data.points": [{"label": "total.memory.bytes", "value": "sap.hana.memory.provisioned.bytes.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "sap.hana.memory.used.percent.last", "type": "gauge"}]}}}}, {"id": 10000000000716, "_type": "0", "visualization.name": "Connections", "visualization.description": "SAP HANA Connections", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.hana.queued.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.idle.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.active.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": ""}, {"type": "metric", "data.point": ""}, {"type": "metric", "data.point": "sap.hana.queued.connections"}, {"type": "metric", "data.point": "sap.hana.idle.connections"}, {"type": "metric", "data.point": "sap.hana.active.connections"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "sap.hana.active.connections", "icon": {"name": "active-connections", "placement": "prefix"}}, "header": {"title": "Connections", "style": {"font.size": "medium"}, "data.points": [{"label": "queued.connections", "value": "sap.hana.queued.connections.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Idle", "value": "sap.hana.idle.connections.last"}, {"label": "Active", "value": "sap.hana.active.connections.last"}]}}}}, {"id": 10000000000717, "_type": "0", "visualization.name": "Transactions", "visualization.description": "SAP HANA Transactions", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.hana.active.transactions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.inactive.transactions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.idle.transactions", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "sap.hana.blocked.transactions"}, {"type": "metric", "data.point": "sap.hana.active.transactions"}, {"type": "metric", "data.point": "sap.hana.inactive.transactions"}, {"type": "metric", "data.point": "sap.hana.idle.transactions"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "sap.hana.active.transactions", "icon": {"name": "transactions", "placement": "prefix"}}, "header": {"title": "Transactions", "style": {"font.size": "medium"}, "data.points": [{"label": "idle.transactions", "value": "sap.hana.idle.transactions.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Inactive", "value": "sap.hana.inactive.transactions.last"}, {"label": "Active", "value": "sap.hana.active.transactions.last"}]}}}}, {"id": 10000000000718, "_type": "0", "visualization.name": "Backup", "visualization.description": "SAP HANA Backup", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.hana.latest.backup.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "sap.hana.latest.backup.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "sap.hana.latest.backup.bytes", "icon": {"name": "backup", "placement": "prefix"}}, "header": {"title": "Backup", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "sap.hana.latest.backup.bytes.last"}]}}}}, {"id": 10000000000719, "_type": "0", "visualization.name": "Licence", "visualization.description": "SAP HANA Licence", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.hana.license.size.gb", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.license.used.gb", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "sap.hana.license.size.gb"}, {"type": "metric", "data.point": "sap.hana.license.used.gb"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "sap.hana.license.used.gb", "icon": {"name": "licence", "placement": "prefix"}}, "header": {"title": "License", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Size", "value": "sap.hana.license.size.gb.last"}, {"label": "Used", "value": "sap.hana.license.used.gb.last"}]}}}}, {"_type": "0", "id": 10000000000720, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Application Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000721, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Application Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "system.process~uptime.percent.avg"}}}}, {"id": 10000000000722, "_type": "0", "visualization.name": "<PERSON><PERSON><PERSON>", "visualization.description": "SAP HANA Schema Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.hana.schema~tables", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.schema~size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.schema", "title": "<PERSON><PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.schema~tables.last", "title": "Tables", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.schema~size.bytes.last", "title": "<PERSON><PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000723, "_type": "0", "visualization.name": "Expensive Queries", "visualization.description": "SAP HANA Expensive Queries", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.hana.expensive.query.connection.id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.expensive.query.cpu.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.expensive.query.duration.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.expensive.query.memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.expensive.query.error", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.expensive.query.error.code", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.expensive.query.records", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.expensive.query.connection.id.last", "title": " ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"], "icon": {"name": "hashtag", "placement": "prefix", "classes": ["text-neutral-light"]}}}, {"name": "sap.hana.expensive.query.cpu.time.last", "title": "CPU Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"]}}, {"name": "sap.hana.expensive.query.duration.ms.last", "title": "Duration", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"], "icon": {"name": "stopwatch-20", "placement": "prefix", "classes": ["text-secondary-orange"]}}}, {"name": "sap.hana.expensive.query.memory.bytes.last", "title": "Memory", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"], "icon": {"name": "sd-card", "placement": "prefix", "classes": ["text-secondary-green"]}}}, {"name": "sap.hana.expensive.query.error.last", "title": "Error", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "query", "title": "Query", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "computed": "yes", "formula": {"operation": "combine", "columns": ["sap.hana.expensive.query.error", "sap.hana.expensive.query.error.code"]}, "style": {"classes": ["font-bold"], "icon": {"name": "file-search", "placement": "prefix", "classes": ["text-primary"]}}}, {"name": "sap.hana.expensive.query.error.code.last", "title": "Error Code", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.expensive.query.records.last", "title": "Query Records", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"], "icon": {"name": "copy", "placement": "prefix", "classes": ["text-secondary-yelow"]}}}]}}}, {"id": 10000000000724, "_type": "0", "visualization.name": "Disk Details", "visualization.description": "SAP HANA Disk Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.hana.disk~path", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.disk~bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.disk~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.disk~host", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.disk~data.volume.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.disk~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "sap.hana.disk", "title": "Disk", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "sap.hana.disk~path.last", "title": "Path", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "sap.hana.disk~bytes.last", "title": "Bytes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "sap.hana.disk~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "sap.hana.disk~host.last", "title": "Host", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "sap.hana.disk~data.volume.bytes.last", "title": "Volume", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "sap.hana.disk~used.bytes.last", "title": "Used", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}]}}}, {"id": 10000000000725, "_type": "0", "visualization.name": "Database Details", "visualization.description": "SAP HANA Database Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.hana.database~description", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.database~status", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.database", "title": "Database", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.database~description.last", "title": "Description", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.database~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status"}]}}}, {"id": 10000000000726, "_type": "0", "visualization.name": "Service Details", "visualization.description": "SAP HANA Service Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.hana.service~host", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.service~port", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.service~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.service~cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.service~memory.used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.service~active.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.service~requests.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.service~request.latency.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.service", "title": "Service", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.service~host.last", "title": "Host", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.service~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "style": {}}, {"name": "sap.hana.service~port.last", "title": "Port", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.service~cpu.percent.last", "title": "CPU", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"inline.chart": {"type": "gauge"}}}, {"name": "sap.hana.service~memory.used.bytes.last", "title": "Memory Used", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.service~active.requests.last", "title": "Active Requests", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.service~requests.per.sec.last", "title": "Requests (per sec)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.service~request.latency.time.ms.last", "title": "Request Latency", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000727, "_type": "0", "visualization.name": "Disk Volume IOPS", "visualization.description": "SAP HANA Disk Volume IOPS", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.hana.volume~id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.volume~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.volume~host", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.volume~io.write.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.volume~io.read.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.volume~io.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.volume", "title": "Path", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.volume~id.last", "title": "Id", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.volume~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.volume~host.last", "title": "Host", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.volume~io.write.time.ms.last", "title": "Write Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.volume~io.read.time.ms.last", "title": "Read Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.volume~io.bytes.per.sec.last", "title": "Volume (Bytes/Sec.)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000728, "_type": "0", "visualization.name": "Jobs Details", "visualization.description": "SAP HANA Cache Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.hana.job.connection.id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.job.start.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.job.schema.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.job.current.progress", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "no", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.job", "title": "Job", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.job.connection.id.last", "title": "Connection ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.job.start.time.last", "title": "Start Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.job.schema.name.last", "title": "Schema Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.job.current.progress.last", "title": "Current Progress", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000729, "_type": "0", "visualization.name": "<PERSON><PERSON>", "visualization.description": "SAP HANA Cache Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.hana.cache~host", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.cache~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.cache~hits", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.cache~misses", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.cache~size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.cache", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.cache~host.last", "title": "Host", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.cache~size.bytes.last", "title": "<PERSON><PERSON> (MB)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.cache~used.bytes.last", "title": "Used (MB)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.cache~hits.last", "title": "Hit Ratio (%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.cache~misses.last", "title": "Misses", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000730, "_type": "0", "visualization.name": "Sessions Details", "visualization.description": "SAP HANA Sessions Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.hana.session.user.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.session.host", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.session.port", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.session.connection.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.session.connection.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.session.idle.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.session.current.schema.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.session.sent.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.session.sent.messages", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.session.query", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.session.user.name.last", "title": "Username", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.session.host.last", "title": "Host", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.session.port.last", "title": "Port", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.session.connection.status.last", "title": "Connection Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.session.connection.type.last", "title": "Connection Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.session.idle.time.ms.last", "title": "Idle Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.session.current.schema.name.last", "title": "Schema Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.session.sent.bytes.last", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.session.sent.messages.last", "title": "Sent Messages", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.session.query.last", "title": "Query", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000731, "_type": "0", "visualization.name": "Backup Catalog", "visualization.description": "SAP HANA Backup Catalog", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.hana.backup.catalog~source.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.backup.catalog~service.type.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.backup.catalog~start.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.backup.catalog~end.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.backup.catalog~entry.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.backup.catalog~state", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.backup.catalog~bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.backup.catalog", "title": "ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.backup.catalog~source.type.last", "title": "Source Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.backup.catalog~service.type.name.last", "title": "Service Type Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.backup.catalog~start.time.last", "title": "Start Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.backup.catalog~end.time.last", "title": "End Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.backup.catalog~bytes.last", "title": "Catalog Volume", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.backup.catalog~entry.type.last", "title": "Entry Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.backup.catalog~state.last", "title": "State", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000732, "_type": "0", "visualization.name": "Last Backup Details", "visualization.description": "SAP HANA Last Backup Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.hana.latest.backup.id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.latest.backup.start.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.latest.backup.end.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.latest.backup.entry.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.latest.backup.destination.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.latest.backup.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.backup.max.recovery.backint.channels", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.backup.backint.data.path", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.backup.data.file.path", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.backup.log.file.path", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "layout": "column", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.latest.backup.id.last", "title": "Backup ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.latest.backup.start.time.last", "title": "Backup Start Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.latest.backup.end.time.last", "title": "Backup End Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.latest.backup.entry.type.last", "title": "Backup Entry Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.latest.backup.destination.type.last", "title": "Backup Destination Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.latest.backup.bytes.last", "title": "Backup Bytes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.backup.max.recovery.backint.channels.last", "title": "Backup Max Recovery Backint Channels", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.backup.backint.data.path.last", "title": "Backup Backint Data Path", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.backup.data.file.path.last", "title": "Backup Data File Path", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.backup.log.file.path.last", "title": "Backup Log File Path", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000733, "_type": "0", "visualization.name": "Replication Details", "visualization.description": "SAP HANA Replication Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.hana.replication~site.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.replication~secondary.host", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.replication~secondary.site.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.replication~status", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.replication", "title": "ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.replication~site.name.last", "title": "Site Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.replication~secondary.host.last", "title": "Secondary Host", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.replication~secondary.site.name.last", "title": "Secondary Site", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sap.hana.replication~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000734, "_type": "0", "visualization.name": "Log <PERSON>", "visualization.description": "SAP HANA Log Role Replay", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sap.hana.log.replay~queue.host", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.log.replay.queue~volume", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.log.replay.queue~record.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.log.replay.queue~records", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.log.replay.queue~latency.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.log.replay.queue~wait.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sap.hana.log.replay.queue~record.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "sap.hana.log.replay.queue~host.last", "title": "Queue Host", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "sap.hana.log.replay.queue~volume.last", "title": "Volume", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "sap.hana.log.replay.queue~last", "title": "Queue", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "sap.hana.log.replay.queue~record.type.last", "title": "Record Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "sap.hana.log.replay.queue~records.last", "title": "Records", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "sap.hana.log.replay.queue~latency.time.ms.last", "title": "Latency Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "sap.hana.log.replay.queue~wait.time.ms.last", "title": "Wait Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}, {"name": "sap.hana.log.replay.queue~record.size.bytes.last", "title": "Record Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {}}]}}}]