[{"_type": "0", "id": 10000000001975, "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.name": "CPU", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.cpu.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "system.cpu.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "CPU", "style": {"font.size": "medium", "icon": "cpu"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "system.cpu.percent.last", "type": "gauge"}]}, "style": {"type": "number", "font.size": "small", "color.conditions": [{"color": "#f04e3e"}, {"color": "#f58518"}, {"color": "#f5bc18"}], "color.data.point": "system.cpu.percent", "icon": {"name": "cpu"}}}}}, {"_type": "0", "id": 10000000001976, "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.name": "Memory Used ", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.memory.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "system.memory.used.percent"}]}], "visualization.properties": {"gauge": {"header": {"title": "Memory", "style": {"font.size": "medium", "icon": "memory"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "system.memory.used.percent.last", "type": "gauge"}]}, "style": {"type": "number", "font.size": "small", "color.conditions": [{"color": "#f04e3e"}, {"color": "#f58518"}, {"color": "#f5bc18"}], "color.data.point": "system.memory.used.percent", "icon": {"name": "memory"}}}}}, {"_type": "0", "id": 10000000001977, "visualization.name": "Connection", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"header": {"title": "Connection", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": ""}]}, "style": {"icon": {"name": "active-connections"}, "color.data.point": ""}}}}, {"_type": "0", "id": 10000000001978, "visualization.name": "Exceptions", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"header": {"title": "Connection", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": ""}]}, "style": {"icon": {"name": "expectations"}, "color.data.point": ""}}}}, {"_type": "0", "id": 10000000001979, "visualization.name": "Queue Length", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "iis.asp.net.native.queue.length", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "iis.asp.net.native.queue.length"}]}], "visualization.properties": {"gauge": {"header": {"title": "Connection", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": "iis.asp.net.native.queue.length"}]}, "style": {"icon": {"name": "queue"}, "color.data.point": "iis.asp.net.native.queue.length"}}}}, {"_type": "0", "id": 10000000001980, "visualization.name": "Threads", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"header": {"title": "Connection", "style": {"font.size": "medium"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"value": ""}]}, "style": {"icon": {"name": "queue"}, "color.data.point": ""}}}}, {"id": 10000000001981, "_type": "0", "visualization.name": "Physical Thread", "visualization.description": "Physical Thread DontNet", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001982, "_type": "0", "visualization.name": "Logical Thread", "visualization.description": "Logical Thread DontNet", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001983, "_type": "0", "visualization.name": "Recognised <PERSON><PERSON><PERSON>", "visualization.description": "Recognised Thread DontNet", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001984, "visualization.name": "Loader heap", "visualization.description": "Loader heap Dotnet", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000001985, "visualization.name": "Assemblies", "visualization.description": "Assemblies Dotnet", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001986, "_type": "0", "visualization.name": "Contention", "visualization.description": "Contention DontNet", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000001987, "_type": "0", "visualization.name": "Request Failed", "visualization.description": "Contention DontNet", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}]