[{"_type": "0", "id": 10000000008080, "visualization.name": "Cisco Meraki Controller Devices", "visualization.description": "Cisco Meraki Controller Devices", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.meraki.online.devices", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.offline.devices", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.meraki.online.devices"}, {"type": "metric", "data.point": "cisco.meraki.offline.devices"}]}], "visualization.properties": {"gauge": {"header": {"title": "Devices", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Online", "value": "cisco.meraki.online.devices.last"}, {"label": "Offline", "value": "cisco.meraki.offline.devices.last"}]}, "style": {"icon": {"name": "cisco-meraki-devices"}}}}}, {"_type": "0", "id": 10000000008081, "visualization.name": "Cisco Meraki Controller Network", "visualization.description": "Cisco Meraki Controller Network", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.meraki.networks", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.meraki.networks"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "network", "placement": "prefix"}}, "header": {"title": "Network", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "cisco.meraki.networks.last"}]}}}}, {"_type": "0", "id": 10000000008082, "visualization.name": "Cisco Meraki Controller Clients", "visualization.description": "Cisco Meraki Controller Clients", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.meraki.clients", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.meraki.clients"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "users", "placement": "prefix"}}, "header": {"title": "Clients", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "cisco.meraki.clients.last"}]}}}}, {"_type": "0", "id": 10000000008083, "visualization.name": "Cisco Meraki Controller API Response", "visualization.description": "Cisco Meraki Controller API Response", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.meraki.client.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.server.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.meraki.client.errors"}, {"type": "metric", "data.point": "cisco.meraki.server.errors"}]}], "visualization.properties": {"gauge": {"header": {"title": "API Response", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "4XX", "value": "cisco.meraki.client.errors.last"}, {"label": "5XX", "value": "cisco.meraki.server.errors.last"}]}, "style": {"icon": {"name": "transactions"}}}}}, {"_type": "0", "id": 10000000008084, "visualization.name": "Cisco Meraki Controller Uplinks", "visualization.description": "Cisco Meraki Controller Uplinks", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.meraki.connected.uplinks", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.disconnected.uplinks", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.meraki.connected.uplinks"}, {"type": "metric", "data.point": "cisco.meraki.disconnected.uplinks"}]}], "visualization.properties": {"gauge": {"header": {"title": "Uplinks", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "Connected", "value": "cisco.meraki.connected.uplinks.last"}, {"label": "Not Connected", "value": "cisco.meraki.disconnected.uplinks.last"}]}, "style": {"icon": {"name": "wifi"}}}}}, {"_type": "0", "id": 10000000008085, "visualization.name": "Cisco Meraki Controller License Count", "visualization.description": "Cisco Meraki Controller License Count", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.meraki.licenses", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "cisco.meraki.licenses"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "log-license", "placement": "prefix"}}, "header": {"title": "License Count", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "cisco.meraki.licenses.last"}]}}}}, {"_type": "0", "id": 10000000008086, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000008087, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000008088, "visualization.name": "Response Time vs Packet Lost", "visualization.category": "Chart", "visualization.type": "StackedArea", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ping.packet.lost.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ping.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000008089, "visualization.name": "API 4XX Response", "visualization.description": "Cisco Meraki Controller API 4XX Response", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.meraki~client.errors", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000008090, "visualization.name": "API 5XX Response", "visualization.description": "Cisco Meraki Controller API 5XX Response", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.meraki~api.response.5xx", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000008091, "visualization.name": "", "visualization.description": "", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.meraki.network~devices", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.network~offline.devices", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.network~online.devices", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.network~dormant.devices", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.network~alert.devices", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.network~clients", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.network~heavy.usage.clients", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "cisco.meraki.network", "title": "Network", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.meraki.network~devices.last", "title": "Total Devices", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.meraki.network~online.devices.last", "title": "Online Devices", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.meraki.network~alert.devices.last", "title": "Alerting Devices", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.meraki.network~offline.devices.last", "title": "Offline Devices", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.meraki.network~dormant.devices.last", "title": "<PERSON>rmant Devices", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.meraki.network~clients.last", "title": "Total Clients", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.meraki.network~heavy.usage.clients.last", "title": "Clients With Heavy Usage", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000008092, "visualization.name": "Client Details", "visualization.description": "Client Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.meraki.client~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.client~mac.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.client~ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.client~user", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.client~manufacturer", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.client~os", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.client~ssid", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.client~vlan", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.client~sent.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.client~recieved.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "cisco.meraki.client", "title": "Client", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.meraki.client~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "style": {}}, {"name": "cisco.meraki.client~mac.address.last", "title": "<PERSON>dress", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.meraki.client~ip.address.last", "title": "IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.meraki.client~user.last", "title": "User", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.meraki.client~manufacturer.last", "title": "Manufacture", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.meraki.client~os.last", "title": "OS", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.meraki.client~ssid.last", "title": "SSID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.meraki.client~vlan.last", "title": "VLAN", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.meraki.client~received.bytes.per.sec.last", "title": "RX", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.meraki.client~sent.bytes.per.sec.last", "title": "TX", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000008093, "visualization.name": "VPN By Loss", "visualization.description": "Cisco Meraki Controller VPN By Loss", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.meraki.vpn~loss.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000008094, "visualization.name": "VPN By Jitter", "visualization.description": "Cisco Meraki Controller VPN By Jitter", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.meraki.vpn~average.jitter.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000008095, "visualization.name": "VPN By Latency", "visualization.description": "Cisco Meraki Controller VPN By Latency", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.meraki.vpn~average.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000008096, "visualization.name": "VPN Details", "visualization.description": "VPN Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "cisco.meraki.vpn~peer.network.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.vpn~sender.uplink", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.vpn~receiver.uplink", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.vpn~average.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.vpn~loss.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.vpn~average.jitter.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "cisco.meraki.vpn~average.mos", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "cisco.meraki.vpn.network.name", "title": "Network Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.meraki.vpn~peer.network.name.last", "title": "Peer Network Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.meraki.vpn~sender.uplink.last", "title": "Spender Uplink", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.meraki.vpn~receiver.uplink.last", "title": "Received Uplink", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.meraki.vpn~average.latency.ms.last", "title": "Avg Latency", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.meraki.vpn~loss.percent.last", "title": "Loss %", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.meraki.vpn~average.jitter.ms.last", "title": "Avg <PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "cisco.meraki.vpn~average.mos.last", "title": "Avg <PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000008097, "visualization.name": "Event Summary", "visualization.description": "Event Summary", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "", "title": "Event Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": ".last", "title": "Host Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": ".last", "title": "System IP", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": ".last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": ".last", "title": "Reason", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": ".last", "title": "Severity", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": ".last", "title": "Component", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}]