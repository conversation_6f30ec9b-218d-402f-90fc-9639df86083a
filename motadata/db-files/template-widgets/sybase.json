[{"_type": "0", "id": 10000000000735, "visualization.name": "Connection", "visualization.description": "Sybase Connection", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sybase.active.remote.logins", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.active.user.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.active.remote.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "sybase.active.remote.logins"}, {"type": "metric", "data.point": "sybase.active.user.connections"}, {"type": "metric", "data.point": "sybase.active.remote.connections"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "sybase.active.remote.logins", "icon": {"name": "active-connections", "placement": "prefix"}}, "header": {"title": "Connection", "style": {"font.size": "medium"}, "data.points": [{"label": "Active Remote Logins", "value": "sybase.active.remote.logins.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "User", "value": "sybase.active.user.connections.last"}, {"label": "Remote", "value": "sybase.active.remote.connections.last"}]}}}}, {"_type": "0", "id": 10000000000736, "visualization.name": "Memory", "visualization.description": "Sybase Memory", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sybase.heap.memory.per.user.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.active.heap.memory.per.user", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.free.heap.memory.per.user", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "sybase.active.kernel.resource.memory.bytes"}, {"type": "metric", "data.point": "sybase.free.kernel.resource.memory.bytes"}, {"type": "metric", "data.point": "sybase.kernel.resource.memory.used.percent"}, {"type": "metric", "data.point": "sybase.active.memory.per.worker.process"}, {"type": "metric", "data.point": "system.memory.used.percent"}, {"type": "metric", "data.point": "sybase.heap.memory.per.user.used.percent"}, {"type": "metric", "data.point": "sybase.active.heap.memory.per.user"}, {"type": "metric", "data.point": "sybase.free.heap.memory.per.user"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "sybase.heap.memory.per.user.used.percent", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "Memory", "style": {"font.size": "medium"}, "data.points": [{"label": "sybase.heap.memory.per.user.used", "value": "sybase.heap.memory.per.user.used.percent.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Active Heap", "value": "sybase.active.heap.memory.per.user.last"}, {"label": "Free Heap", "value": "sybase.free.heap.memory.per.user.last"}]}}}}, {"_type": "0", "id": 10000000000737, "visualization.name": "Active Worker", "visualization.description": "Sybase Active Worker", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sybase.active.worker.processes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "sybase.active.worker.processes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "sybase.active.worker.processes", "icon": {"name": "process", "placement": "prefix"}}, "header": {"title": "Active Worker", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "sybase.active.worker.processes.last"}]}}}}, {"_type": "0", "id": 10000000000738, "visualization.name": "DB Throughput", "visualization.description": "Sybase DB Throughput", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sybase.io.errors", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.read.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.write.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "sybase.io.errors"}, {"type": "metric", "data.point": "sybase.read.bytes.per.sec"}, {"type": "metric", "data.point": "sybase.write.bytes.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "sybase.io.errors", "icon": {"name": "tacho-meter", "placement": "prefix"}}, "header": {"title": "DB Throughput", "style": {"font.size": "medium"}, "data.points": [{"label": "sybase.io.errors", "value": "sybase.io.errors.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Write", "value": "sybase.write.bytes.per.sec.last"}, {"label": "Read", "value": "sybase.read.bytes.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000000739, "visualization.name": "Database", "visualization.description": "Sybase Database", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sybase.active.databases", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "sybase.active.databases"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "sybase.active.databases", "icon": {"name": "database", "placement": "prefix"}}, "header": {"title": "Database", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Active", "value": "sybase.active.databases.last"}]}}}}, {"_type": "0", "id": 10000000000740, "visualization.name": "Locks", "visualization.description": "Sybase Locks", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sybase.deadlocks", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.active.locks", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "sybase.deadlocks"}, {"type": "metric", "data.point": "sybase.active.locks"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "sybase.deadlocks", "icon": {"name": "locks", "placement": "prefix"}}, "header": {"title": "Locks", "style": {"font.size": "medium"}, "data.points": [{"label": "sybase.deadlocks", "value": "sybase.deadlocks.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Active", "value": "sybase.active.locks.last"}]}}}}, {"_type": "0", "id": 10000000000741, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Application Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000742, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Application Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "system.process~uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000000743, "visualization.name": "Database Backup Details", "visualization.description": "Sybase Database Backup Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sybase.database~backup.last.checkpoint.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.database~backup.suspended.processes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.database~backup.instance.id", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sybase.database", "title": "Database", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sybase.database~backup.last.checkpoint.time.last", "title": "Last Checkpoint Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sybase.database~backup.suspended.processes.last", "title": "Backup Processes", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sybase.database~backup.instance.id.last", "title": "Instance ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000744, "visualization.name": "Connection Time", "visualization.description": "Sybase Connection Time", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sybase.active.user.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.active.remote.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000745, "visualization.name": "Heap Memory Utilization", "visualization.description": "Sybase Heap Memory Utilization", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sybase.active.heap.memory.per.user", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.free.heap.memory.per.user", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000746, "visualization.name": "Database I/O", "visualization.description": "Sybase Database I/O", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sybase.write.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.read.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.io.errors", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000747, "visualization.name": "Worker Process Details", "visualization.description": "Sybase Worker Process Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sybase.active.memory.per.worker.process", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.active.worker.processes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000748, "visualization.name": "<PERSON><PERSON>", "visualization.description": "Sybase Cache Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sybase.active.procedure.cache.size", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.buffer.cache.hit.ratio", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000749, "visualization.name": "Locks", "visualization.description": "Sybase Locks Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sybase.deadlocks", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.active.locks", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000750, "visualization.name": "Kernel Memory", "visualization.description": "Sybase Kernel Memory", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sybase.active.kernel.resource.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.free.kernel.resource.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000751, "visualization.name": "Process Details", "visualization.description": "Sybase Process Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sybase.process.id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.process.hostname", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.process.client", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.process.priority", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.process.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.process.memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.process.io.ops", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sybase.process.id.last", "title": "ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sybase.process.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sybase.process.hostname.last", "title": "Host Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sybase.process.client.last", "title": "Client", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sybase.process.priority.last", "title": "Priority", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sybase.process.status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sybase.process.memory.bytes.last", "title": "Memory", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sybase.process.io.ops", "title": "IO", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000752, "visualization.name": "Database Details", "visualization.description": "Sybase Database Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "sybase.database~id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.database~memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.database~used.memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "sybase.database~last.dump.transaction", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sybase.database", "title": "Database", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sybase.database~id.last", "title": "ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sybase.database~memory.bytes.last", "title": "Memory", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sybase.database~used.memory.bytes.last", "title": "Used Memory", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "sybase.database~last.dump.transaction.last", "title": "Last Dump Transaction", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}]