[{"_type": "0", "id": 10000000002265, "visualization.name": "RPC Latency", "visualization.description": "Client Access Role RPC Latency", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.client.access.role.rpc.latency.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "exchange.client.access.role.rpc.latency.ms"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "exchange.client.access.role.rpc.latency.ms", "icon": {"name": "response-time", "placement": "prefix"}}, "header": {"title": "RPC Latency", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "exchange.client.access.role.rpc.latency.ms.last"}]}}}}, {"_type": "0", "id": 10000000002266, "visualization.name": "Sync Active Requests", "visualization.description": "Client Access Role Sync Active Requests", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.client.access.role.sync.active.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "exchange.client.access.role.sync.active.requests"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "exchange.client.access.role.sync.active.requests", "icon": {"name": "active-flow", "placement": "prefix"}}, "header": {"title": "Sync Active Requests", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "exchange.client.access.role.sync.active.requests.last"}]}}}}, {"_type": "0", "id": 10000000002267, "visualization.name": "RPC Users", "visualization.description": "Client Access Role RPC Users", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.client.access.role.rpc.active.users", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.client.access.role.rpc.users", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "exchange.client.access.role.rpc.active.users"}, {"type": "metric", "data.point": "exchange.client.access.role.rpc.users"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "exchange.client.access.role.rpc.users", "icon": {"name": "users", "placement": "prefix"}}, "header": {"title": "RPC Users", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Total", "value": "exchange.client.access.role.rpc.users.last"}, {"label": "Active", "value": "exchange.client.access.role.rpc.active.users.last"}]}}}}, {"_type": "0", "id": 10000000002268, "visualization.name": "Sync Requests", "visualization.description": "Client Access Role Sync Requests", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.client.access.role.sync.requests.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "exchange.client.access.role.sync.requests.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "exchange.client.access.role.sync.requests.per.sec", "icon": {"name": "requests", "placement": "prefix"}}, "header": {"title": "Sync Requests", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "exchange.client.access.role.sync.requests.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000002269, "visualization.name": "Client Connections", "visualization.description": "Client Access Role Client Connections", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.client.access.role.rpc.client.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "exchange.client.access.role.rpc.client.connections"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "exchange.client.access.role.rpc.client.connections", "icon": {"name": "active-connections", "placement": "prefix"}}, "header": {"title": "Client Connections", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "exchange.client.access.role.rpc.client.connections.last"}]}}}}, {"_type": "0", "id": 10000000002270, "visualization.name": "Web Connections", "visualization.description": "Client Access Role Web Connections", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.client.access.role.web.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "exchange.client.access.role.web.connections"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "exchange.client.access.role.web.connections", "icon": {"name": "globe", "placement": "prefix"}}, "header": {"title": "Web Connections", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "exchange.client.access.role.web.connections.last"}]}}}}, {"_type": "0", "id": 10000000002271, "visualization.name": "RPC Latency", "visualization.description": "Client Access Role RPC Latency", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.client.access.role.rpc.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002272, "visualization.name": "RPC Client Connections", "visualization.description": "Client Access Role RPC Client Connections", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.client.access.role.rpc.client.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002273, "visualization.name": "RPC Requests", "visualization.description": "Client Access Role RPC Requests", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.client.access.role.rpc.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002274, "visualization.name": "Client Access Role - Requests", "visualization.description": "Client Access Role Client Access Role - Requests", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.client.access.role.webservice.requests.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.client.access.role.owa.requests.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.client.access.role.auto.discovery.requests.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.client.access.role.sync.requests.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002275, "visualization.name": "Web Connections Attempts", "visualization.description": "Client Access Role Connections Attempts", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.client.access.role.web.connection.attempts.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002276, "visualization.name": "Control Panel Request Latency", "visualization.description": "Client Access Role Control Panel Request Latency", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.client.access.role.control.panel.request.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002277, "visualization.name": "OWA Search Time", "visualization.description": "Client Access Role OWA Search Time", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.client.access.role.owa.search.time.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002278, "visualization.name": "Sync Active Requests", "visualization.description": "Client Access Sync Active ", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.client.access.role.sync.active.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000002279, "visualization.name": "NSPI RPC Latency", "visualization.description": "Client Access NSPI RPC Latency", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "exchange.client.access.role.nspi.rpc.request.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "exchange.client.access.role.nspi.rpc.browse.request.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}]