[{"_type": "0", "id": 10000000002689, "visualization.name": "Uptime", "visualization.description": "Uptime", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "started.time.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"header": {"title": "Uptime", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": " ", "value": "started.time.seconds.last"}]}, "style": {"icon": {"name": "list"}, "color.data.point": "started.time.seconds"}}}}, {"_type": "0", "id": 10000000002690, "visualization.name": "Processor", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "processor.device.instances", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "processor.device.instances"}]}], "visualization.properties": {"gauge": {"header": {"title": "Processor", "style": {"font.size": "medium", "icon": "cpu"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "processor.device.instances.last"}]}, "style": {"icon": {"name": "cpu"}, "color.data.point": "processor.device.instances"}}}}, {"_type": "0", "id": 10000000002691, "visualization.name": "Memory", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "memory.device.instances", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "memory.device.instances"}]}], "visualization.properties": {"gauge": {"header": {"title": "Memory", "style": {"font.size": "medium", "icon": "memory"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "memory.device.instances.last"}]}, "style": {"icon": {"name": "memory"}, "color.data.point": "memory.device.instances"}}}}, {"_type": "0", "id": 10000000002692, "visualization.name": "Disk", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "physical.drive.instances", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "physical.drive.instances"}]}], "visualization.properties": {"gauge": {"header": {"title": "Disk", "style": {"font.size": "medium", "icon": "disk"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "physical.drive.instances.last"}]}, "style": {"icon": {"name": "disk"}, "color.data.point": "physical.drive.instances"}}}}, {"_type": "0", "id": 10000000002693, "visualization.name": "PSU", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "power.supply.sensor.instances", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "power.supply.sensor.instances"}]}], "visualization.properties": {"gauge": {"header": {"title": "Power Supply", "style": {"font.size": "medium", "icon": "rejected-connections"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "power.supply.sensor.instances.last"}]}, "style": {"icon": {"name": "power-supply"}, "color.data.point": "power.supply.sensor.instances"}}}}, {"_type": "0", "id": 10000000002694, "visualization.name": "Fan", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "fan.sensor.instances", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "fan.sensor.instances"}]}], "visualization.properties": {"gauge": {"header": {"title": "Fan", "style": {"font.size": "medium", "icon": "rejected-connections"}}, "footer": {"style": {"font.size": "large"}, "data.points": [{"label": "", "value": "fan.sensor.instances.last"}]}, "style": {"icon": {"name": "fan"}, "color.data.point": "fan.sensor.instances"}}}}, {"_type": "0", "id": 10000000002695, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Pie", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "monitor.unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000002696, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "monitor.uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "monitor.unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "monitor.uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000002697, "visualization.name": "Power Supply Details", "visualization.description": "HPE iLO Power Supply Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "power.supply.sensor~model", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "power.supply.sensor~serial.number", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "power.supply.sensor~capacity.used.mill.watts", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "power.supply.sensor~chassis.number", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "power.supply.sensor~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "power.supply.sensor~main.mill.volts", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "power.supply.sensor~hardware.location", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small", "css.classes": [], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "power.supply.sensor", "title": "Power Supply", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold"]}}, {"name": "power.supply.sensor~chassis.number.last", "title": "<PERSON><PERSON><PERSON> Number", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold"]}}, {"name": "power.supply.sensor~hardware.location.last", "title": "Hardware Location", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"classes": ["font-bold"]}}, {"name": "power.supply.sensor~model.last", "title": "Model", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"classes": ["font-bold"]}}, {"name": "power.supply.sensor~serial.number.last", "title": "Serial Number", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"classes": ["font-bold"]}}, {"name": "power.supply.sensor~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 6}, {"name": "power.supply.sensor~main.mill.volts.last", "title": "Main Voltage", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {"classes": ["font-bold"]}}, {"name": "power.supply.sensor~capacity.used.mill.watts.last", "title": "Capacity Used(Watts)  ", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {"classes": ["font-bold"]}}]}}}, {"_type": "0", "id": 10000000002699, "visualization.name": "physical Drive Details", "visualization.description": "HPE iLO physical Drive Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "physical.drive~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "physical.drive~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "physical.drive~condition", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "physical.drive~configuration.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "physical.drive~capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small", "css.classes": [], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "physical.drive", "title": "Physical Drive", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold"]}}, {"name": "physical.drive~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 3}, {"name": "physical.drive~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"classes": ["font-bold"]}}, {"name": "physical.drive~condition.last", "title": "Condition", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 5}, {"name": "physical.drive~configuration.status.last", "title": "Configuration Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 6}, {"name": "physical.drive~capacity.bytes.last", "title": "Capacity", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {"classes": ["font-bold"]}}]}}}, {"_type": "0", "id": 10000000002700, "visualization.name": "Temperature Sensor Details", "visualization.description": "HPE iLO Temperature Sensor Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "temperature.sensor~system.location", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "temperature.sensor~condition", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "temperature.sensor~current.temperature.celsius", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small", "css.classes": [], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "temperature.sensor", "title": "Temperature Sensor", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold"]}}, {"name": "temperature.sensor~system.location.last", "title": "System Location", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"classes": ["font-bold"]}}, {"name": "temperature.sensor~current.temperature.celsius.last", "title": "Current Temperature", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"classes": ["font-bold"]}}, {"name": "temperature.sensor~condition.last", "title": "Condition", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 5}]}}}, {"_type": "0", "id": 10000000002702, "visualization.name": "Processor Device Details", "visualization.description": "HPE iLO Processor Device Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "processor.device~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "processor.device~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "processor.device~serial.number", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "processor.device~core", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "processor.device~max.speed.mhz", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small", "css.classes": [], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "processor.device", "title": "processor Index", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2}, {"name": "processor.device~name.last", "title": "processor", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"classes": ["font-bold"]}}, {"name": "processor.device~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 4}, {"name": "processor.device~serial.number.last", "title": "Serial Number", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"classes": ["font-bold"]}}, {"name": "processor.device~core.last", "title": "Core", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"classes": ["font-bold"]}}, {"name": "processor.device~max.speed.mhz.last", "title": "Max Speed(MHZ)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {"classes": ["font-bold"]}}]}}}, {"_type": "0", "id": 10000000002703, "visualization.name": "Fan Details", "visualization.description": "HPE iLO Fan Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "fan.sensor~condition", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "fan.sensor~chassis.number", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "fan.sensor~current.speed.rpm", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "fan.sensor~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "fan.sensor~system.location", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small", "css.classes": [], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "fan.sensor", "title": "Fan", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold"]}}, {"name": "fan.sensor~condition.last", "title": "Condition", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "status", "position": 3}, {"name": "fan.sensor~chassis.number.last", "title": "<PERSON><PERSON><PERSON> Number", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"classes": ["font-bold"]}}, {"name": "fan.sensor~current.speed.rpm.last", "title": "Current Speed", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"classes": ["font-bold"]}}, {"name": "fan.sensor~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"classes": ["font-bold"]}}, {"name": "fan.sensor~system.location.last", "title": "Location Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {"classes": ["font-bold"]}}]}}}, {"_type": "0", "id": 10000000002704, "visualization.name": "Memory Device Details", "visualization.description": "HPE iLO Memory Device Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "memory.device~number", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "memory.device~capacity.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "memory.device~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "memory.device~current.status", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small", "css.classes": [], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "memory.device", "title": "Memory", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {"classes": ["font-bold"]}}, {"name": "memory.device~number.last", "title": "Number", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {"classes": ["font-bold"]}}, {"name": "memory.device~capacity.bytes.last", "title": "Capacity", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"classes": ["font-bold"]}}, {"name": "memory.device~type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {"classes": ["font-bold"]}}, {"name": "memory.device~current.status.last", "title": "current status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "type": "status", "style": {"classes": ["font-bold"]}}]}}}, {"_type": "0", "id": 10000000002705, "visualization.name": "Interface Details", "visualization.category": "Grid", "visualization.type": "Interface", "publish.sub.query.progress": false, "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "interface~index", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~ip.address", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~alias", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~discard.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~error.packets", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~traffic.utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~out.traffic.utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~in.traffic.utilization.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~speed.bits.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "interface~description", "aggregator": "last", "entity.type": "monitor", "entities": []}]}, {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "vlan~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "vlan~port", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "yes", "header": "yes", "style": {"header.font.size": "medium", "css.classes": ["stripped-grid"], "row.height.px": 50}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 1}, {"name": "interface", "title": "Interface", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 2, "type": "interface", "style": {}}, {"name": "interface~alias.last", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {"classes": ["font-bold"]}}, {"name": "interface~ip.address.last", "title": "Interface IP", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {"classes": ["font-bold"]}}, {"name": "interface~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "type": "status", "formula": {"conditions": [{"result": "Up", "operator": "=", "value": "up"}, {"result": "Down", "operator": "=", "value": "down"}]}}, {"name": "interface~traffic.utilization.percent.last", "title": "Traffic Utilization", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {"classes": ["font-bold"], "inline.chart": {"type": "gauge"}}}, {"name": "interface~in.traffic.utilization.percent.last", "title": "IN (%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {"classes": ["font-bold"], "inline.chart": {"type": "gauge"}}}, {"name": "interface~out.traffic.utilization.percent.last", "title": "OUT (%)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {"classes": ["font-bold"], "inline.chart": {"type": "gauge"}}}, {"name": "interface~type.last", "title": "Port Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {"classes": ["font-bold"], "icon": {"name": "port", "classes": ["text-secondary-orange"]}}}, {"name": "interface~error.packets.last", "title": "Error", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 10, "style": {"classes": ["font-bold"], "icon": {"name": "times-circle", "classes": ["text-secondary-red"]}}}, {"name": "interface~discard.packets.last", "title": "Discarded", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "type": "number", "position": 11, "style": {"classes": ["font-bold"], "icon": {"name": "trash", "classes": ["text-secondary-orange"]}}}, {"name": "interface~index.last", "title": "Interface Index", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 12, "style": {}}, {"name": "interface~name.last", "title": "Interface Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3}, {"name": "vlan", "title": "VLAN", "show": "no", "sortable": "yes", "disable": "yes", "resizable": "yes", "selectable": "no", "orderable": "yes", "position": 13, "type": "interface", "style": {}}, {"name": "vlan~name.last", "title": "Assigned VLAN", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 14, "style": {"classes": ["font-bold"], "icon": {"name": "topology", "classes": []}}}, {"name": "vlan~port.last", "title": "VLAN Port", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "no", "orderable": "no", "position": 15, "style": {}}, {"name": "interface~speed.bits.per.sec.last", "title": "Speed", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 16, "style": {}}, {"name": "interface~description.last", "title": "Description", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 17, "style": {}}]}}}, {"_type": "0", "id": 10000000002707, "visualization.name": "Response Time vs. Packet Lost", "visualization.category": "Chart", "visualization.type": "Line", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "ping.packet.lost.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "ping.latency.ms", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}]