[{"_type": "0", "id": 10000000000335, "visualization.name": "Request", "visualization.description": "WebApp Service Request", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.webapp.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.webapp.requests"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.webapp.requests", "icon": {"name": "requests", "placement": "prefix"}}, "header": {"title": "Requests", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.webapp.requests.last"}]}}}}, {"_type": "0", "id": 10000000000336, "visualization.name": "Throughput", "visualization.description": "AppService Throughput", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.webapp.io.read.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.webapp.io.write.bytes.per.sec", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.webapp.io.read.bytes.per.sec"}, {"type": "metric", "data.point": "azure.webapp.io.write.bytes.per.sec"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.webapp.io.write.bytes.per.sec", "icon": {"name": "tacho-meter", "placement": "prefix"}}, "header": {"title": "Throughput", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "azure.webapp.io.read.bytes.per.sec.last"}, {"label": "Write", "value": "azure.webapp.io.write.bytes.per.sec.last"}]}}}}, {"_type": "0", "id": 10000000000337, "visualization.name": "CPU Time", "visualization.description": "AppService CPU Time", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.webapp.cpu.time.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.webapp.cpu.time.seconds"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.webapp.cpu.time.seconds", "icon": {"name": "cpu", "placement": "prefix"}}, "header": {"title": "CPU Time", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.webapp.cpu.time.seconds.last"}]}}}}, {"_type": "0", "id": 10000000000338, "visualization.name": "Memory", "visualization.description": "AppService Memory", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.webapp.memory.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.webapp.memory.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.webapp.memory.bytes", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "Memory", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.webapp.memory.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000339, "visualization.name": "HTTP Responses", "visualization.description": "AppService HTTP Responses", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.webapp.http2xx.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.webapp.http4xx.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "azure.webapp.http5xx.requests", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.webapp.http2xx.requests"}, {"type": "metric", "data.point": "azure.webapp.http4xx.requests"}, {"type": "metric", "data.point": "azure.webapp.http5xx.requests"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.webapp.http2xx.requests", "icon": {"name": "sessions", "placement": "prefix"}}, "header": {"title": "HTTP Responses", "style": {"font.size": "medium"}, "data.points": [{"label": "2XX Code", "value": "azure.webapp.http2xx.requests.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "4XX Code", "value": "azure.webapp.http4xx.requests.last"}, {"label": "5XX Code", "value": "azure.webapp.http5xx.requests.last"}]}}}}, {"_type": "0", "id": 10000000000340, "visualization.name": "Total Connections", "visualization.description": "AppService Total Connections", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.webapp.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "azure.webapp.connections"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "azure.webapp.connections", "icon": {"name": "sitemap", "placement": "prefix"}}, "header": {"title": "Total Connection", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "azure.webapp.connections.last"}]}}}}, {"_type": "0", "id": 10000000000341, "visualization.name": "CPU Time", "visualization.description": "AppService CPU Time Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.webapp.cpu.time.seconds", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000342, "visualization.name": "Connection Details", "visualization.description": "AppService Connection Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.webapp.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000343, "visualization.name": "Disk Queue Length", "visualization.discription": "AppService Disk Queue Length", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.webapp.disk.queue.length", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000344, "visualization.name": "Memory Utilization of the App", "visualization.description": "AppService Memory Utilization of the App", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.webapp.private.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.webapp.memory.bytes", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000345, "visualization.name": "Request Details", "visualization.description": "AppService Request Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.webapp.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000346, "visualization.name": "HTTP Requests", "visualization.description": "AppService HTTP Requests", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "StackedVerticalBar", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.webapp.http2xx.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.webapp.http3xx.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.webapp.http4xx.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.webapp.http5xx.requests", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000347, "visualization.name": "Throughput", "visualization.description": "AppService Throughput Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.webapp.io.read.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.webapp.io.write.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.webapp.io.other.bytes.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000348, "visualization.name": "IOPS", "visualization.description": "AppService IOPS", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "azure.webapp.io.other.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.webapp.io.read.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "azure.webapp.io.write.ops.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}]