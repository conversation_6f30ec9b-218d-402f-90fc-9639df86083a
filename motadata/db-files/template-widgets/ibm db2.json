[{"_type": "0", "id": 10000000000391, "visualization.name": "Connection", "visualization.description": "IBMDB2 Connection", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.local.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.remote.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "db2.executing.remote.connections"}, {"type": "metric", "data.point": "db2.executing.local.connections"}, {"type": "metric", "data.point": "db2.connections"}, {"type": "metric", "data.point": "db2.local.connections"}, {"type": "metric", "data.point": "db2.remote.connections"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "active-connections", "placement": "prefix"}}, "header": {"title": "Connections", "style": {"font.size": "medium"}, "data.points": [{"label": "Connections", "value": "db2.connections.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Local", "value": "db2.local.connections.last"}, {"label": "Remote", "value": "db2.remote.connections.last"}]}}}}, {"_type": "0", "id": 10000000000392, "visualization.name": "Sessions", "visualization.description": "IBMDB2 Sessions", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.waiting.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.active.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "db2.blocked.sessions"}, {"type": "metric", "data.point": "db2.active.sessions"}, {"type": "metric", "data.point": "db2.sessions"}, {"type": "metric", "data.point": "db2.waiting.sessions"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "sessions", "placement": "prefix"}}, "header": {"title": "Sessions", "style": {"font.size": "medium"}, "data.points": [{"label": "Sessions", "value": "db2.sessions.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Waiting", "value": "db2.waiting.sessions.last"}, {"label": "Active", "value": "db2.active.sessions.last"}]}}}}, {"_type": "0", "id": 10000000000393, "visualization.name": "Memory", "visualization.description": "IBMDB2 Memory", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.sort.heap.allocation.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.sort.heap.used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "db2.private.committed.memory.bytes"}, {"type": "metric", "data.point": "db2.accepted.piped.sorts"}, {"type": "metric", "data.point": "db2.requested.piped.sorts"}, {"type": "metric", "data.point": "db2.db.active.hash.joins.rate"}, {"type": "metric", "data.point": "db2.sort.heap.allocation.bytes"}, {"type": "metric", "data.point": "db2.sort.heap.used.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "Memory", "style": {"font.size": "medium"}, "data.points": [{"label": "Heap Allocation", "value": "db2.sort.heap.allocation.bytes.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "db2.sort.heap.used.percent.last", "type": "gauge"}]}}}}, {"_type": "0", "id": 10000000000394, "visualization.name": "Database Size", "visualization.description": "IBMDB2 Database Size", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.database.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "db2.database.size.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "database", "placement": "prefix"}}, "header": {"title": "Database Size", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "db2.database.size.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000395, "visualization.name": "Agents", "visualization.description": "IBMDB2 Agents", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.stolen.agents", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.active.agents", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.idle.agents", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "db2.pool.agents"}, {"type": "metric", "data.point": "db2.stolen.agents"}, {"type": "metric", "data.point": "db2.active.agents"}, {"type": "metric", "data.point": "db2.idle.agents"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "agent", "placement": "prefix"}}, "header": {"title": "Agents", "style": {"font.size": "medium"}, "data.points": [{"label": "Agents", "value": "db2.stolen.agents.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Active", "value": "db2.active.agents.last"}, {"label": "Idle", "value": "db2.idle.agents.last"}]}}}}, {"_type": "0", "id": 10000000000396, "visualization.name": "Gateway Connections", "visualization.description": "IBMDB2 Gateway Connections", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.active.gateway.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.gateway.connections", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "db2.gateway.client.connection.waits"}, {"type": "metric", "data.point": "db2.active.gateway.connections"}, {"type": "metric", "data.point": "db2.gateway.connections"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "router", "placement": "prefix"}}, "header": {"title": "Gateway Connections", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Active", "value": "db2.active.gateway.connections.last"}, {"label": "Total", "value": "db2.gateway.connections.last"}]}}}}, {"_type": "0", "id": 10000000000397, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Application Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000398, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Application Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "system.process~uptime.percent.avg"}}}}, {"_type": "0", "id": 10000000000399, "visualization.name": "Sessions Chart", "visualization.description": "IBMDB2 Sessions Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "db2.blocked.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "db2.waiting.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "db2.active.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000400, "visualization.name": "Connection", "visualization.description": "IBMDB2 Connection Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "db2.local.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "db2.remote.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000401, "visualization.name": "Memory", "visualization.description": "IBMDB2 Memory Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.sort.heap.used.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000402, "visualization.name": "Agents", "visualization.description": "IBMDB2 Agents Chart", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.active.agents", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "db2.idle.agents", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000403, "visualization.name": "Database Memory Pool", "visualization.description": "IBMDB2 Database Memory Pool", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.memory.pool~size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.memory.pool~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.memory.pool", "title": "Pool Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.memory.pool~size.bytes.last", "title": "<PERSON> Size (MB)", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.memory.pool~used.bytes.last", "title": "Pool Used", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000404, "visualization.name": "Database Buffer Pool", "visualization.description": "IBMDB2 Database Buffer Pool", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.buffer.pool~hit.ratio.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.buffer.pool", "title": "Pool Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.buffer.pool~hit.ratio.percent.last", "title": "Pool Hit Ratio", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000405, "visualization.name": "Query", "visualization.description": "IBMDB2 Query", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.db.dynamic.sql.queries", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.db.successful.sql.queries", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.db.failed.sql.queries", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "db2.db.dynamic.sql.queries"}, {"type": "metric", "data.point": "db2.db.successful.sql.queries"}, {"type": "metric", "data.point": "db2.db.failed.sql.queries"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "file-search", "placement": "prefix"}}, "header": {"title": "Query", "style": {"font.size": "medium"}, "data.points": [{"label": "db2.db.dynamic.sql.queries", "value": "db2.db.dynamic.sql.queries.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Successful", "value": "db2.db.successful.sql.queries.last"}, {"label": "Failed", "value": "db2.db.failed.sql.queries.last"}]}}}}, {"_type": "0", "id": 10000000000406, "visualization.name": "<PERSON>", "visualization.description": "IBMDB2 DB Cache", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.db.catalog.cache.hit.ratio.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.db.package.cache.hit.ratio.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.db.database.buffer.pool.hit.ratio.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "db2.db.catalog.cache.hit.ratio.percent"}, {"type": "metric", "data.point": "db2.db.package.cache.hit.ratio.percent"}, {"type": "metric", "data.point": "db2.db.database.buffer.pool.hit.ratio.percent"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "cache", "placement": "prefix"}}, "header": {"title": "<PERSON>", "style": {"font.size": "medium"}, "data.points": [{"label": "db2.db.catalog.cache.hit.ratio", "value": "db2.db.catalog.cache.hit.ratio.percent.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Package", "value": "db2.db.package.cache.hit.ratio.percent.last"}, {"label": "<PERSON>uff<PERSON>", "value": "db2.db.database.buffer.pool.hit.ratio.percent.last"}]}}}}, {"_type": "0", "id": 10000000000407, "visualization.name": "Lock", "visualization.description": "IBMDB2 Lock", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.db.lock.waits", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.db.deadlocks", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.db.locks", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "db2.db.lock.waits"}, {"type": "metric", "data.point": "db2.db.deadlocks"}, {"type": "metric", "data.point": "db2.db.locks"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "locks", "placement": "prefix"}}, "header": {"title": "Lock", "style": {"font.size": "medium"}, "data.points": [{"label": "db2.db.lock.waits", "value": "db2.db.lock.waits.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Deadlocks", "value": "db2.db.deadlocks.last"}, {"label": "Locks", "value": "db2.db.locks.last"}]}}}}, {"_type": "0", "id": 10000000000408, "visualization.name": "DB Commits and Rollbacks", "visualization.description": "IBMDB2 DB Commits and Rollbacks", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.db.commits", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.db.rollbacks", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "db2.db.commits"}, {"type": "metric", "data.point": "db2.db.rollbacks"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "database", "placement": "prefix"}}, "header": {"title": "DB Commits and Rollbacks", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Commit", "value": "db2.db.commits.last"}, {"label": "Rollbacks", "value": "db2.db.rollbacks.last"}]}}}}, {"_type": "0", "id": 10000000000409, "visualization.name": "Log Storage", "visualization.description": "IBMDB2 Log Storage", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.db.log.space.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.db.used.log.space.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.db.free.log.space.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "db2.db.log.space.bytes"}, {"type": "metric", "data.point": "db2.db.used.log.space.bytes"}, {"type": "metric", "data.point": "db2.db.free.log.space.bytes"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "disk", "placement": "prefix"}}, "header": {"title": "Log Storage", "style": {"font.size": "medium"}, "data.points": [{"label": "db2.db.log.space", "value": "db2.db.log.space.bytes.last"}]}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Used", "value": "db2.db.used.log.space.bytes.last"}, {"label": "Free", "value": "db2.db.free.log.space.bytes.last"}]}}}}, {"_type": "0", "id": 10000000000410, "visualization.name": "Log IOPS", "visualization.description": "IBMDB2 Log IOPS", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.db.log.read.ops", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.db.log.write.ops", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "db2.db.log.read.ops"}, {"type": "metric", "data.point": "db2.db.log.write.ops"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "icon": {"name": "rtt", "placement": "prefix"}}, "header": {"title": "Log IOPS", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "Read", "value": "db2.db.log.read.ops.last"}, {"label": "Write", "value": "db2.db.log.write.ops.last"}]}}}}, {"_type": "0", "id": 10000000000411, "visualization.name": "Database Details", "visualization.description": "IBMDB2 Database Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.db.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.db.alias", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.db.location", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "layout": "column", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.db.status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.db.alias.last", "title": "<PERSON><PERSON>", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.db.location.last", "title": "Location", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000412, "visualization.name": "Query Details", "visualization.description": "IBMDB2 Query Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.db.successful.sql.queries", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "db2.db.failed.sql.queries", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "db2.db.dynamic.sql.queries", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000413, "visualization.name": "Database Operations", "visualization.description": "IBMDB2 Database Operations", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.db.deleted.rows", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "db2.db.inserted.rows", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "db2.db.updated.rows", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "db2.db.row.reads", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000414, "visualization.name": "Database Connections", "visualization.description": "IBMDB2 Database Connections", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.db.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "db2.db.secondary.connections", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000415, "visualization.name": "Database Cache Performance", "visualization.description": "IBMDB2 Database Cache Performance", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.db.catalog.cache.hit.ratio.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "db2.db.package.cache.hit.ratio.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000416, "visualization.name": "Sorts", "visualization.description": "IBMDB2 Sorts", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.db.sorts", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "db2.db.active.sorts", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000417, "visualization.name": "Lock Waits", "visualization.description": "IBMDB2 Lock Waits", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.db.lock.waits", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000418, "visualization.name": "Database Buffer Pool Hit Ratio", "visualization.description": "IBMDB2 Database Buffer Pool Hit Ratio", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.db.database.buffer.pool.hit.ratio.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000419, "visualization.name": "Select Statement", "visualization.description": "IBMDB2 Select Statement", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.db.select.sql.queries", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"_type": "0", "id": 10000000000424, "visualization.name": "Database Backup Details", "visualization.description": "IBMDB2 Database Backup Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.backup~id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.backup~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.backup~location", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.backup~duration.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.backup~operation.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.backup~object.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.backup~table.spaces", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.backup~table.space", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.backup~start.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.backup~end.time", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.backup~id.last", "title": "Backup ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "db2.backup", "title": "EID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "db2.backup~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "db2.backup~location.last", "title": "Location", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "db2.backup~duration.seconds.last", "title": "Duration", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "db2.backup~operation.type.last", "title": "Operation Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "db2.backup~object.type.last", "title": "Object Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "db2.backup~table.spaces.last", "title": "Tablespaces", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}, {"name": "db2.backup~table.space.last", "title": "Tablespace Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {}}, {"name": "db2.backup~start.time.last", "title": "Start Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 10, "style": {}}, {"name": "db2.backup~end.time.last", "title": "End Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 11, "style": {}}]}}}, {"_type": "0", "id": 10000000000420, "visualization.name": "Database Session Details", "visualization.description": "IBMDB2 Database Session Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.session.application", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.session.user.id", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.session.remote.client", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.session.cpu.time.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.session.lock.held", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.session.lock.wait", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.session.lock.wait.time.ms", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.session.statement.execution.elapsed.time.seconds", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.session", "title": "Session Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.session.application.last", "title": "Application", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.session.user.id.last", "title": "User ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.session.remote.client.last", "title": "Remote Client", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.session.cpu.time.seconds.last", "title": "CPU Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.session.lock.held.last", "title": "Lock Held", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.session.lock.wait.last", "title": "Lock Wait", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.session.lock.wait.time.ms.last", "title": "Lock Wait Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.session.statement.execution.elapsed.time.seconds.last", "title": "Execution Elapsed Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000421, "visualization.name": "Session Locks Held", "visualization.description": "IBMDB2 Session Locks Held", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.session.application", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.session.table.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.session.lock.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.session.lock.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.session.lock.mode", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.session.lock.held", "title": "Lock Held", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.session.application.last", "title": "Application", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.session.table.name.last", "title": "Table Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.session.lock.type.last", "title": "Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.session.lock.status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.session.lock.mode.last", "title": "Mode", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"_type": "0", "id": 10000000000422, "visualization.name": "Session Locks Wait Details", "visualization.description": "IBMDB2 Session Locks Wait Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Grid", "visualization.data.sources": [{"type": "correlated.metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.session.lock.wait", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.session.agent.id.holding.lock", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.session.application", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.session.table.name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.session.lock.type", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.session.lock.status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.session.lock.mode", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.session.lock.wait.start.time", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.session.lock.mode.requested", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "db2.session.lock.wait.last", "title": "Lock Wait", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "db2.session.agent.id.holding.lock.last", "title": "Agent ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "db2.session.application.last", "title": "Application", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 3, "style": {}}, {"name": "db2.session.table.name.last", "title": "Table Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "db2.session.lock.type.last", "title": "Lock Type", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "db2.session.lock.status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "db2.session.lock.mode.last", "title": "Lock Mode", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "db2.session.lock.wait.start.time.last", "title": "Lock Wait Start Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}, {"name": "db2.session.lock.mode.requested.last", "title": "Mode Request", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {}}]}}}, {"_type": "0", "id": 10000000000423, "visualization.name": "Tablespace Details", "visualization.description": "IBMDB2 Tablespace Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "db2.table.space~name", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.table.space~status", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.table.space~created", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.table.space~size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.table.space~used.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "db2.table.space~used.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "medium"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 1, "style": {}}, {"name": "db2.table.space", "title": "ID", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 2, "style": {}}, {"name": "db2.table.space~name.last", "title": "Name", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 4, "style": {}}, {"name": "db2.table.space~status.last", "title": "Status", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 5, "style": {}}, {"name": "db2.table.space~created.last", "title": "Created Time", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 6, "style": {}}, {"name": "db2.table.space~size.bytes.last", "title": "Size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 7, "style": {}}, {"name": "db2.table.space~used.bytes.last", "title": "Used", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 8, "style": {}}, {"name": "db2.table.space~used.percent.last", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "position": 9, "style": {}}]}}}]