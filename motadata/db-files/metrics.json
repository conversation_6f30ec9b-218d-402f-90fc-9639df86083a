[{"metric.name": "Active Directory", "metric.polling.time": 240, "metric.plugin": "activedirectory", "metric.state": "ENABLE", "metric.type": "Active Directory", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 1, "metric.polling.min.time": 240, "timeout": 60}}, {"metric.name": "Active Directory Role", "metric.polling.time": 1800, "metric.plugin": "activedirectoryrole", "metric.state": "ENABLE", "metric.type": "Active Directory", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 2, "metric.polling.min.time": 1800, "timeout": 60}}, {"metric.name": "Apache HTTP", "metric.polling.time": 120, "metric.plugin": "apache", "metric.state": "ENABLE", "metric.type": "Apache HTTP", "metric.context": {"plugin.engine": "go", "plugin.id": 3, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "Amazon Cloud", "metric.polling.time": 600, "metric.plugin": "aws", "metric.state": "ENABLE", "metric.type": "AWS Cloud", "metric.context": {"plugin.engine": "go", "plugin.id": 4, "metric.polling.min.time": 600, "timeout": 120}}, {"metric.name": "Amazon Cloud Billing", "metric.polling.time": 86400, "metric.plugin": "awsbilling", "metric.state": "ENABLE", "metric.type": "AWS Cloud", "metric.context": {"plugin.engine": "go", "plugin.id": 5, "metric.polling.min.time": 43200, "timeout": 120}}, {"metric.name": "Amazon DynamoDB", "metric.polling.time": 600, "metric.plugin": "amazondynamodb", "metric.state": "ENABLE", "metric.type": "Amazon DynamoDB", "metric.context": {"plugin.engine": "go", "plugin.id": 6, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Amazon EBS", "metric.polling.time": 600, "metric.plugin": "amazonebs", "metric.state": "ENABLE", "metric.type": "Amazon EBS", "metric.context": {"plugin.engine": "go", "plugin.id": 7, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Amazon EC2", "metric.polling.time": 300, "metric.plugin": "amazonec2", "metric.state": "ENABLE", "metric.type": "Amazon EC2", "metric.context": {"plugin.engine": "go", "plugin.id": 8, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Amazon ELB", "metric.polling.time": 300, "metric.plugin": "a<PERSON><PERSON><PERSON>", "metric.state": "ENABLE", "metric.type": "AWS ELB", "metric.context": {"plugin.engine": "go", "plugin.id": 9, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Amazon RDS", "metric.polling.time": 300, "metric.plugin": "amazonrds", "metric.state": "ENABLE", "metric.type": "Amazon RDS", "metric.context": {"plugin.engine": "go", "plugin.id": 10, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Amazon S3", "metric.polling.time": 3600, "metric.plugin": "amazons3", "metric.state": "ENABLE", "metric.type": "Amazon S3", "metric.context": {"plugin.engine": "go", "plugin.id": 11, "metric.polling.min.time": 1800, "timeout": 60}}, {"metric.name": "Amazon SNS", "metric.polling.time": 300, "metric.plugin": "amazonsns", "metric.state": "ENABLE", "metric.type": "Amazon SNS", "metric.context": {"plugin.engine": "go", "plugin.id": 12, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Azure Cloud", "metric.polling.time": 600, "metric.plugin": "azure", "metric.state": "ENABLE", "metric.type": "Azure Cloud", "metric.context": {"plugin.engine": "go", "plugin.id": 13, "metric.polling.min.time": 600, "timeout": 120}}, {"metric.name": "Azure Cloud Billing", "metric.polling.time": 86400, "metric.plugin": "azurebilling", "metric.state": "ENABLE", "metric.type": "Azure Cloud", "metric.context": {"plugin.engine": "go", "plugin.id": 14, "metric.polling.min.time": 43200, "timeout": 120}}, {"metric.name": "Azure Cosmos DB", "metric.polling.time": 300, "metric.plugin": "azurecosmosdb", "metric.state": "ENABLE", "metric.type": "Azure Cosmos DB", "metric.context": {"plugin.engine": "go", "plugin.id": 15, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Azure SQL Database", "metric.polling.time": 300, "metric.plugin": "azuresqldatabase", "metric.state": "ENABLE", "metric.type": "Azure SQL Database", "metric.context": {"plugin.engine": "go", "plugin.id": 16, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Azure Storage", "metric.polling.time": 3600, "metric.plugin": "azurestorage", "metric.state": "ENABLE", "metric.type": "Azure Storage", "metric.context": {"plugin.engine": "go", "plugin.id": 17, "metric.polling.min.time": 3600, "timeout": 120}}, {"metric.name": "Azure VM", "metric.polling.time": 300, "metric.plugin": "azurevm", "metric.state": "ENABLE", "metric.type": "Azure VM", "metric.context": {"plugin.engine": "go", "plugin.id": 18, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Azure WebApp", "metric.polling.time": 300, "metric.plugin": "azurewebapp", "metric.state": "ENABLE", "metric.type": "Azure WebApp", "metric.context": {"plugin.engine": "go", "plugin.id": 19, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Bind9", "metric.polling.time": 180, "metric.plugin": "bind9", "metric.state": "ENABLE", "metric.type": "Bind9", "metric.context": {"plugin.engine": "go", "plugin.id": 20, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Bind9 Netstat", "metric.polling.time": 300, "metric.plugin": "bind9netstat", "metric.state": "ENABLE", "metric.type": "Bind9", "metric.context": {"plugin.engine": "go", "plugin.id": 21, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Bind9 Sockstat", "metric.polling.time": 180, "metric.plugin": "bind9sockstat", "metric.state": "ENABLE", "metric.type": "Bind9", "metric.context": {"plugin.engine": "go", "plugin.id": 22, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "SSL Certificate", "metric.polling.time": 3600, "metric.plugin": "certificate", "metric.state": "ENABLE", "metric.type": "SSL Certificate", "metric.context": {"plugin.engine": "go", "plugin.id": 23, "metric.polling.min.time": 1800, "timeout": 60}}, {"metric.name": "Cisco UCS Chassis", "metric.polling.time": 300, "metric.plugin": "ciscochassis", "metric.state": "ENABLE", "metric.type": "Cisco UCS", "metric.context": {"plugin.engine": "python", "plugin.id": 24, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Cisco UCS Server", "metric.polling.time": 300, "metric.plugin": "ciscoucs", "metric.state": "ENABLE", "metric.type": "Cisco UCS", "metric.context": {"plugin.engine": "python", "plugin.id": 25, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Cisco UCS Fabric Interconnect", "metric.polling.time": 600, "metric.plugin": "ciscoucsfabricinterconnect", "metric.state": "ENABLE", "metric.type": "Cisco UCS", "metric.context": {"plugin.engine": "python", "plugin.id": 26, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Cisco UCS Rack Mount", "metric.polling.time": 600, "metric.plugin": "ciscoucsrackmount", "metric.state": "ENABLE", "metric.type": "Cisco UCS", "metric.context": {"plugin.engine": "python", "plugin.id": 27, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Cisco Wireless", "metric.polling.time": 300, "metric.plugin": "ciscowireless", "metric.state": "ENABLE", "metric.type": "Cisco Wireless", "metric.context": {"plugin.engine": "go", "plugin.id": 28, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Cisco Wireless Access Point", "metric.polling.time": 300, "metric.plugin": "ciscowirelessap", "metric.state": "ENABLE", "metric.type": "Cisco Wireless", "metric.context": {"plugin.engine": "go", "plugin.id": 242, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Cisco Wireless Client", "metric.polling.time": 600, "metric.plugin": "ciscowirelessclient", "metric.state": "ENABLE", "metric.type": "Cisco Wireless", "metric.context": {"plugin.engine": "go", "plugin.id": 29, "metric.polling.min.time": 600, "timeout": 120}}, {"metric.name": "Cisco Wireless Rogue AP", "metric.polling.time": 1200, "metric.plugin": "ciscowirelessrogueap", "metric.state": "ENABLE", "metric.type": "Cisco Wireless", "metric.context": {"plugin.engine": "go", "plugin.id": 30, "metric.polling.min.time": 600, "timeout": 120}}, {"metric.name": "Citrix <PERSON>", "metric.polling.time": 300, "metric.plugin": "citrixxen", "metric.state": "ENABLE", "metric.type": "Citrix <PERSON>", "metric.context": {"plugin.engine": "go", "plugin.id": 31, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Citrix Xen Cluster", "metric.polling.time": 600, "metric.plugin": "citrixxencluster", "metric.state": "ENABLE", "metric.type": "Citrix Xen Cluster", "metric.context": {"plugin.engine": "go", "plugin.id": 32, "metric.polling.min.time": 600, "timeout": 120}}, {"metric.name": "Citrix Xen Cluster Config", "metric.polling.time": 3600, "metric.plugin": "citrixxenclusterconfig", "metric.state": "ENABLE", "metric.type": "Citrix Xen Cluster", "metric.context": {"plugin.engine": "go", "plugin.id": 33, "metric.polling.min.time": 3600, "timeout": 60}}, {"metric.name": "Citrix <PERSON> Config", "metric.polling.time": 3600, "metric.plugin": "citrixxenconfig", "metric.state": "ENABLE", "metric.type": "Citrix <PERSON>", "metric.context": {"plugin.engine": "go", "plugin.id": 34, "metric.polling.min.time": 3600, "timeout": 60}}, {"metric.name": "Citrix Xen Network", "metric.polling.time": 600, "metric.plugin": "citrixxennetwork", "metric.state": "ENABLE", "metric.type": "Citrix <PERSON>", "metric.context": {"plugin.engine": "go", "plugin.id": 35, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Citrix Xen Storage", "metric.polling.time": 1800, "metric.plugin": "citrixxenstorage", "metric.state": "ENABLE", "metric.type": "Citrix <PERSON>", "metric.context": {"plugin.engine": "go", "plugin.id": 36, "metric.polling.min.time": 1800, "timeout": 120}}, {"metric.name": "Citrix Xen VM", "metric.polling.time": 600, "metric.plugin": "citrixxenvm", "metric.state": "ENABLE", "metric.type": "Citrix <PERSON>", "metric.context": {"plugin.engine": "go", "plugin.id": 37, "metric.polling.min.time": 600, "timeout": 120}}, {"metric.name": "IBM Db2", "metric.polling.time": 300, "metric.plugin": "db2", "metric.state": "ENABLE", "metric.type": "IBM Db2", "metric.context": {"plugin.engine": "python", "plugin.id": 38, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "IBM Db2 Database", "metric.polling.time": 600, "metric.plugin": "db2database", "metric.state": "ENABLE", "metric.type": "IBM Db2", "metric.context": {"plugin.engine": "python", "plugin.id": 39, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "IBM Db2 Pool", "metric.polling.time": 400, "metric.plugin": "db2pool", "metric.state": "ENABLE", "metric.type": "IBM Db2", "metric.context": {"plugin.engine": "python", "plugin.id": 40, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "IBM Db2 Session", "metric.polling.time": 600, "metric.plugin": "db2session", "metric.state": "ENABLE", "metric.type": "IBM Db2", "metric.context": {"plugin.engine": "python", "plugin.id": 41, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "IBM Db2 Tablespace", "metric.polling.time": 600, "metric.plugin": "db2tablespace", "metric.state": "ENABLE", "metric.type": "IBM Db2", "metric.context": {"plugin.engine": "python", "plugin.id": 42, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "DNS", "metric.polling.time": 180, "metric.plugin": "dns", "metric.state": "ENABLE", "metric.type": "DNS", "metric.context": {"plugin.engine": "python", "plugin.id": 43, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Domain", "metric.polling.time": 180, "metric.plugin": "domain", "metric.state": "ENABLE", "metric.type": "Domain", "metric.context": {"plugin.engine": "go", "plugin.id": 44, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": ".NET App", "metric.polling.time": 240, "metric.plugin": "dotnet", "metric.state": "ENABLE", "metric.type": "Windows", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 45, "metric.polling.min.time": 180, "timeout": 60}}, {"metric.name": "Email", "metric.polling.time": 180, "metric.plugin": "email", "metric.state": "ENABLE", "metric.type": "Email", "metric.context": {"plugin.engine": "go", "plugin.id": 46, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "VMware ESXi", "metric.polling.time": 300, "metric.plugin": "esxi", "metric.state": "ENABLE", "metric.type": "VMware ESXi", "metric.context": {"plugin.engine": "go", "plugin.id": 47, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "VMware ESXi Config", "metric.polling.time": 3600, "metric.plugin": "esxiconfig", "metric.state": "ENABLE", "metric.type": "VMware ESXi", "metric.context": {"plugin.engine": "go", "plugin.id": 48, "metric.polling.min.time": 3600, "timeout": 60}}, {"metric.name": "VMware ESXi Hardware Sensor", "metric.polling.time": 600, "metric.plugin": "esxihardwaresensor", "metric.state": "ENABLE", "metric.type": "VMware ESXi", "metric.context": {"plugin.engine": "go", "plugin.id": 49, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "VMware ESXi Network", "metric.polling.time": 600, "metric.plugin": "esxinetwork", "metric.state": "ENABLE", "metric.type": "VMware ESXi", "metric.context": {"plugin.engine": "go", "plugin.id": 50, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "VMware ESXi Storage", "metric.polling.time": 1800, "metric.plugin": "esxistorage", "metric.state": "ENABLE", "metric.type": "VMware ESXi", "metric.context": {"plugin.engine": "go", "plugin.id": 51, "metric.polling.min.time": 1800, "timeout": 60}}, {"metric.name": "VMware ESXi VM", "metric.polling.time": 600, "metric.plugin": "esxivm", "metric.state": "ENABLE", "metric.type": "VMware ESXi", "metric.context": {"plugin.engine": "go", "plugin.id": 52, "metric.polling.min.time": 600, "timeout": 120}}, {"metric.name": "Exchange Client Access Role", "metric.polling.time": 600, "metric.plugin": "exchangeclientaccessrole", "metric.state": "ENABLE", "metric.type": "Exchange Client Access Role", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 53, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Exchange Edge Transport Role", "metric.polling.time": 600, "metric.plugin": "exchangeedgetransportrole", "metric.state": "ENABLE", "metric.type": "Exchange Edge Transport Role", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 54, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Exchange Mailbox", "metric.polling.time": 259200, "metric.plugin": "exchangemailbox", "metric.state": "DISABLE", "metric.type": "Exchange Mailbox", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 55, "metric.polling.min.time": 86400, "timeout": 1800}}, {"metric.name": "Exchange Mailbox Role", "metric.polling.time": 600, "metric.plugin": "exchangemailboxrole", "metric.state": "ENABLE", "metric.type": "Exchange Mailbox Role", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 56, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Exchange Online Mailbox", "metric.polling.time": 86400, "metric.plugin": "exchangeonlinemailbox", "metric.state": "ENABLE", "metric.type": "Exchange Online", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 57, "metric.polling.min.time": 86400, "timeout": 1800}}, {"metric.name": "FTP", "metric.polling.time": 180, "metric.plugin": "ftp", "metric.state": "ENABLE", "metric.type": "FTP", "metric.context": {"plugin.engine": "go", "plugin.id": 58, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "HAProxy", "metric.polling.time": 180, "metric.plugin": "haproxy", "metric.state": "ENABLE", "metric.type": "HAProxy", "metric.context": {"plugin.engine": "go", "plugin.id": 59, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "HAProxy Session", "metric.polling.time": 600, "metric.plugin": "haproxysession", "metric.state": "ENABLE", "metric.type": "HAProxy", "metric.context": {"plugin.engine": "go", "plugin.id": 60, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "HP-UX", "metric.polling.time": 300, "metric.plugin": "hpux", "metric.state": "ENABLE", "metric.type": "HP-UX", "metric.context": {"plugin.engine": "go", "plugin.id": 61, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "HP-UX CPU Core", "metric.polling.time": 600, "metric.plugin": "hpuxcpucore", "metric.state": "DISABLE", "metric.type": "HP-UX", "metric.context": {"plugin.engine": "go", "plugin.id": 62, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "HP-UX Disk", "metric.polling.time": 1800, "metric.plugin": "hpuxdisk", "metric.state": "ENABLE", "metric.type": "HP-UX", "metric.context": {"plugin.engine": "go", "plugin.id": 63, "metric.polling.min.time": 1800, "timeout": 60}}, {"metric.name": "HP-UX Process", "metric.polling.time": 300, "metric.plugin": "hpuxprocess", "metric.state": "ENABLE", "metric.type": "HP-UX", "metric.context": {"plugin.engine": "go", "plugin.id": 64, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "HyperV", "metric.polling.time": 600, "metric.plugin": "hyperv", "metric.state": "ENABLE", "metric.type": "Hyper-V", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 65, "metric.polling.min.time": 600, "timeout": 180}}, {"metric.name": "HyperV Cluster", "metric.polling.time": 600, "metric.plugin": "hypervcluster", "metric.state": "ENABLE", "metric.type": "Hyper-V Cluster", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 66, "metric.polling.min.time": 600, "timeout": 180}}, {"metric.name": "HyperV Cluster Storage", "metric.polling.time": 1800, "metric.plugin": "hypervclusterstorage", "metric.state": "ENABLE", "metric.type": "Hyper-V Cluster", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 67, "metric.polling.min.time": 1800, "timeout": 60}}, {"metric.name": "HyperV Config", "metric.polling.time": 3600, "metric.plugin": "hypervconfig", "metric.state": "ENABLE", "metric.type": "Hyper-V", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 68, "metric.polling.min.time": 3600, "timeout": 60}}, {"metric.name": "HyperV Network", "metric.polling.time": 600, "metric.plugin": "hypervnetwork", "metric.state": "ENABLE", "metric.type": "Hyper-V", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 69, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "HyperV Service", "metric.polling.time": 600, "metric.plugin": "hypervservice", "metric.state": "ENABLE", "metric.type": "Hyper-V", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 70, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "HyperV Storage", "metric.polling.time": 1800, "metric.plugin": "hypervstorage", "metric.state": "ENABLE", "metric.type": "Hyper-V", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 71, "metric.polling.min.time": 1800, "timeout": 60}}, {"metric.name": "HyperV VM", "metric.polling.time": 600, "metric.plugin": "hypervvm", "metric.state": "ENABLE", "metric.type": "Hyper-V", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 72, "metric.polling.min.time": 600, "timeout": 120}}, {"metric.name": "IBM AIX", "metric.polling.time": 300, "metric.plugin": "ibmaix", "metric.state": "ENABLE", "metric.type": "IBM AIX", "metric.context": {"plugin.engine": "go", "plugin.id": 73, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "IBM AIX CPU Core", "metric.polling.time": 600, "metric.plugin": "ibmaixcpucore", "metric.state": "DISABLE", "metric.type": "IBM AIX", "metric.context": {"plugin.engine": "go", "plugin.id": 74, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "IBM AIX Disk", "metric.polling.time": 1800, "metric.plugin": "ibmaixdisk", "metric.state": "ENABLE", "metric.type": "IBM AIX", "metric.context": {"plugin.engine": "go", "plugin.id": 75, "metric.polling.min.time": 1800, "timeout": 60}}, {"metric.name": "IBM AIX Network Interface", "metric.polling.time": 600, "metric.plugin": "ibmaixnetworkinterface", "metric.state": "ENABLE", "metric.type": "IBM AIX", "metric.context": {"plugin.engine": "go", "plugin.id": 76, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "IBM AIX Process", "metric.polling.time": 300, "metric.plugin": "ibmaixprocess", "metric.state": "ENABLE", "metric.type": "IBM AIX", "metric.context": {"plugin.engine": "go", "plugin.id": 77, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Microsoft IIS", "metric.polling.time": 240, "metric.plugin": "iis", "metric.state": "ENABLE", "metric.type": "Microsoft IIS", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 83, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "Microsoft IIS Application", "metric.polling.time": 600, "metric.plugin": "iisapplication", "metric.state": "DISABLE", "metric.type": "Microsoft IIS", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 84, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Microsoft IIS App Pool", "metric.polling.time": 300, "metric.plugin": "iisapppool", "metric.state": "ENABLE", "metric.type": "Microsoft IIS", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 85, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "IIS ASP.NET", "metric.polling.time": 300, "metric.plugin": "iisaspnet", "metric.state": "DISABLE", "metric.type": "Microsoft IIS", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 86, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "lighttpd", "metric.polling.time": 180, "metric.plugin": "lighthttpd", "metric.state": "ENABLE", "metric.type": "Light Httpd", "metric.context": {"plugin.engine": "go", "plugin.id": 87, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "Linux", "metric.polling.time": 300, "metric.plugin": "linux", "metric.state": "ENABLE", "metric.type": "Linux", "metric.context": {"plugin.engine": "go", "plugin.id": 88, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Linux CPU Core", "metric.polling.time": 600, "metric.plugin": "linuxcpucore", "metric.state": "DISABLE", "metric.type": "Linux", "metric.context": {"plugin.engine": "go", "plugin.id": 89, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Linux DHCP", "metric.polling.time": 600, "metric.plugin": "linuxdhcp", "metric.state": "ENABLE", "metric.type": "Linux DHCP", "metric.context": {"plugin.engine": "go", "plugin.id": 90, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Linux Directory", "metric.polling.time": 1800, "metric.plugin": "linuxdirectory", "metric.state": "ENABLE", "metric.type": "Linux", "metric.context": {"plugin.engine": "go", "plugin.id": 91, "metric.polling.min.time": 1800, "timeout": 60}}, {"metric.name": "Linux Disk", "metric.polling.time": 1800, "metric.plugin": "linuxdisk", "metric.state": "ENABLE", "metric.type": "Linux", "metric.context": {"plugin.engine": "go", "plugin.id": 92, "metric.polling.min.time": 1800, "timeout": 60}}, {"metric.name": "Linux File", "metric.polling.time": 1800, "metric.plugin": "linuxfile", "metric.state": "ENABLE", "metric.type": "Linux", "metric.context": {"plugin.engine": "go", "plugin.id": 93, "metric.polling.min.time": 1800, "timeout": 60}}, {"metric.name": "Linux Network Interface", "metric.polling.time": 600, "metric.plugin": "linuxnetworkinterface", "metric.state": "ENABLE", "metric.type": "Linux", "metric.context": {"plugin.engine": "go", "plugin.id": 94, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Linux Process", "metric.polling.time": 300, "metric.plugin": "linuxprocess", "metric.state": "ENABLE", "metric.type": "Linux", "metric.context": {"plugin.engine": "go", "plugin.id": 95, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "MariaDB", "metric.polling.time": 240, "metric.plugin": "ma<PERSON>b", "metric.state": "ENABLE", "metric.type": "MariaDB", "metric.context": {"plugin.engine": "go", "plugin.id": 96, "metric.polling.min.time": 240, "timeout": 60}}, {"metric.name": "MariaDB Command", "metric.polling.time": 400, "metric.plugin": "ma<PERSON><PERSON><PERSON>mand", "metric.state": "ENABLE", "metric.type": "MariaDB", "metric.context": {"plugin.engine": "go", "plugin.id": 97, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "MariaDB Innodb Engine", "metric.polling.time": 300, "metric.plugin": "mariadbinnodb", "metric.state": "ENABLE", "metric.type": "MariaDB", "metric.context": {"plugin.engine": "go", "plugin.id": 98, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "MariaDB Process", "metric.polling.time": 600, "metric.plugin": "mariadbprocess", "metric.state": "ENABLE", "metric.type": "MariaDB", "metric.context": {"plugin.engine": "go", "plugin.id": 99, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "MSMQ", "metric.polling.time": 300, "metric.plugin": "msmq", "metric.state": "ENABLE", "metric.type": "MSMQ", "metric.context": {"plugin.engine": "go", "plugin.id": 100, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "SQL Server", "metric.polling.time": 300, "metric.plugin": "mssql", "metric.state": "ENABLE", "metric.type": "SQL Server", "metric.context": {"plugin.engine": "go", "plugin.id": 101, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "SQL Server Database", "metric.polling.time": 600, "metric.plugin": "mssqldatabase", "metric.state": "ENABLE", "metric.type": "SQL Server", "metric.context": {"plugin.engine": "go", "plugin.id": 102, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "SQL Server Job", "metric.polling.time": 600, "metric.plugin": "mssqljob", "metric.state": "DISABLE", "metric.type": "SQL Server", "metric.context": {"plugin.engine": "go", "plugin.id": 103, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "SQL Server Session", "metric.polling.time": 600, "metric.plugin": "mssqlsession", "metric.state": "ENABLE", "metric.type": "SQL Server", "metric.context": {"plugin.engine": "go", "plugin.id": 104, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "SQL Server Slow Query", "metric.polling.time": 3600, "metric.plugin": "mssqlslowquery", "metric.state": "ENABLE", "metric.type": "SQL Server", "metric.context": {"plugin.engine": "go", "plugin.id": 105, "metric.polling.min.time": 1800, "timeout": 300}}, {"metric.name": "Multicast", "metric.polling.time": 600, "metric.state": "DISABLE", "metric.plugin": "multicast", "metric.context": {"plugin.engine": "go", "plugin.id": 106, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "MySQL", "metric.polling.time": 300, "metric.plugin": "mysql", "metric.state": "ENABLE", "metric.type": "MySQL", "metric.context": {"plugin.engine": "go", "plugin.id": 107, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "MySQL Command", "metric.polling.time": 400, "metric.plugin": "mysqlcommand", "metric.state": "ENABLE", "metric.type": "MySQL", "metric.context": {"plugin.engine": "go", "plugin.id": 108, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "MySQL Innodb", "metric.polling.time": 400, "metric.plugin": "mysqlinnodb", "metric.state": "ENABLE", "metric.type": "MySQL", "metric.context": {"plugin.engine": "go", "plugin.id": 109, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "MySQL Process", "metric.polling.time": 300, "metric.plugin": "mysqlprocess", "metric.state": "ENABLE", "metric.type": "MySQL", "metric.context": {"plugin.engine": "go", "plugin.id": 110, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "<PERSON><PERSON><PERSON>", "metric.polling.time": 240, "metric.plugin": "nginx", "metric.state": "ENABLE", "metric.type": "<PERSON><PERSON><PERSON>", "metric.context": {"plugin.engine": "go", "plugin.id": 111, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "NTP", "metric.polling.time": 180, "metric.plugin": "ntp", "metric.state": "ENABLE", "metric.type": "NTP", "metric.context": {"plugin.engine": "go", "plugin.id": 112, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Office 365", "metric.polling.time": 600, "metric.plugin": "office365", "metric.state": "ENABLE", "metric.type": "Office 365", "metric.context": {"plugin.engine": "go", "plugin.id": 113, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "One Drive", "metric.polling.time": 600, "metric.plugin": "onedrive", "metric.state": "ENABLE", "metric.type": "OneDrive", "metric.context": {"plugin.engine": "go", "plugin.id": 114, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Oracle Database", "metric.polling.time": 300, "metric.plugin": "oracle", "metric.state": "ENABLE", "metric.type": "Oracle Database", "metric.context": {"plugin.engine": "go", "plugin.id": 115, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Oracle Database ASM Disk Group", "metric.polling.time": 600, "metric.plugin": "oracleasmdisk", "metric.state": "DISABLE", "metric.type": "Oracle Database", "metric.context": {"plugin.engine": "go", "plugin.id": 116, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Oracle Database Job", "metric.polling.time": 600, "metric.plugin": "oraclejob", "metric.state": "DISABLE", "metric.type": "Oracle Database", "metric.context": {"plugin.engine": "go", "plugin.id": 117, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Oracle Database Rollback Segment", "metric.polling.time": 400, "metric.plugin": "oraclerollbacksegment", "metric.state": "ENABLE", "metric.type": "Oracle Database", "metric.context": {"plugin.engine": "go", "plugin.id": 118, "metric.polling.min.time": 400, "timeout": 60}}, {"metric.name": "Oracle Session", "metric.polling.time": 300, "metric.plugin": "oraclesession", "metric.state": "ENABLE", "metric.type": "Oracle Database", "metric.context": {"plugin.engine": "go", "plugin.id": 119, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Oracle Slow Query", "metric.polling.time": 3600, "metric.plugin": "oracleslowquery", "metric.state": "ENABLE", "metric.type": "Oracle Database", "metric.context": {"plugin.engine": "go", "plugin.id": 120, "metric.polling.min.time": 1800, "timeout": 300}}, {"metric.name": "Oracle Tablespace", "metric.polling.time": 600, "metric.plugin": "oracletablespace", "metric.state": "ENABLE", "metric.type": "Oracle Database", "metric.context": {"plugin.engine": "go", "plugin.id": 121, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "<PERSON>", "metric.polling.time": 60, "metric.plugin": "ping", "metric.state": "ENABLE", "metric.type": "<PERSON>", "metric.context": {"plugin.engine": "go", "plugin.id": 122, "metric.polling.min.time": 30, "timeout": 3}}, {"metric.name": "Port", "metric.polling.time": 60, "metric.plugin": "port", "metric.state": "ENABLE", "metric.type": "Port", "metric.context": {"plugin.engine": "go", "plugin.id": 123, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "PostgreSQL", "metric.polling.time": 300, "metric.plugin": "postgresql", "metric.state": "ENABLE", "metric.type": "PostgreSQL", "metric.context": {"plugin.engine": "go", "plugin.id": 124, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "PostgreSQL Database", "metric.polling.time": 600, "metric.plugin": "postgresqldatabase", "metric.state": "ENABLE", "metric.type": "PostgreSQL", "metric.context": {"plugin.engine": "go", "plugin.id": 125, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "PostgreSQL Session", "metric.polling.time": 300, "metric.plugin": "postgresqlsession", "metric.state": "ENABLE", "metric.type": "PostgreSQL", "metric.context": {"plugin.engine": "go", "plugin.id": 126, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "RabbitMQ", "metric.polling.time": 300, "metric.plugin": "rabbitmq", "metric.state": "ENABLE", "metric.type": "RabbitMQ", "metric.context": {"plugin.engine": "go", "plugin.id": 127, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "RabbitMQ Channel", "metric.polling.time": 600, "metric.plugin": "rabbitmqchannel", "metric.state": "DISABLE", "metric.type": "RabbitMQ", "metric.context": {"plugin.engine": "go", "plugin.id": 128, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "RabbitMQ Connection", "metric.polling.time": 300, "metric.plugin": "rabbitmqconnection", "metric.state": "ENABLE", "metric.type": "RabbitMQ", "metric.context": {"plugin.engine": "go", "plugin.id": 129, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Rabbitmq Exchange", "metric.polling.time": 400, "metric.plugin": "rabbitmqexchange", "metric.state": "DISABLE", "metric.type": "RabbitMQ", "metric.context": {"plugin.engine": "go", "plugin.id": 130, "metric.polling.min.time": 400, "timeout": 60}}, {"metric.name": "Rabbitmq Queue", "metric.polling.time": 300, "metric.plugin": "rabbitmqqueue", "metric.state": "ENABLE", "metric.type": "RabbitMQ", "metric.context": {"plugin.engine": "go", "plugin.id": 131, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "RADIUS", "metric.polling.time": 180, "metric.plugin": "radius", "metric.state": "ENABLE", "metric.type": "RADIUS", "metric.context": {"plugin.engine": "go", "plugin.id": 132, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Remote Access VPN", "metric.polling.time": 600, "metric.state": "DISABLE", "metric.plugin": "remotevpnconnection", "metric.context": {"plugin.engine": "go", "plugin.id": 133, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Routing Protocol", "metric.polling.time": 900, "metric.state": "ENABLE", "metric.plugin": "routing", "metric.context": {"plugin.engine": "go", "plugin.id": 134, "metric.polling.min.time": 600, "timeout": 120}}, {"metric.name": "Ruckus Wireless", "metric.polling.time": 300, "metric.plugin": "ruckuswireless", "metric.state": "ENABLE", "metric.type": "Ruckus Wireless", "metric.context": {"plugin.engine": "go", "plugin.id": 135, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Ruckus Wireless Access Point", "metric.polling.time": 300, "metric.plugin": "ruckuswirelessap", "metric.state": "ENABLE", "metric.type": "Ruckus Wireless", "metric.context": {"plugin.engine": "go", "plugin.id": 244, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Ruckus Wireless Client", "metric.polling.time": 600, "metric.plugin": "ruckuswirelessclient", "metric.state": "ENABLE", "metric.type": "Ruckus Wireless", "metric.context": {"plugin.engine": "go", "plugin.id": 136, "metric.polling.min.time": 600, "timeout": 120}}, {"metric.name": "Ruckus Wireless Rogue AP", "metric.polling.time": 600, "metric.plugin": "ruckuswirelessrogueap", "metric.state": "ENABLE", "metric.type": "Ruckus Wireless", "metric.context": {"plugin.engine": "go", "plugin.id": 137, "metric.polling.min.time": 600, "timeout": 120}}, {"metric.name": "Ruckus Wireless WLAN", "metric.polling.time": 600, "metric.plugin": "ruckus<PERSON><PERSON><PERSON><PERSON>", "metric.state": "ENABLE", "metric.type": "Ruckus Wireless", "metric.context": {"plugin.engine": "go", "plugin.id": 138, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "SAP HANA", "metric.polling.time": 300, "metric.plugin": "saphana", "metric.state": "ENABLE", "metric.type": "SAP HANA", "metric.context": {"plugin.engine": "go", "plugin.id": 139, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "SAP HANA Backup", "metric.polling.time": 600, "metric.plugin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metric.state": "ENABLE", "metric.type": "SAP HANA", "metric.context": {"plugin.engine": "go", "plugin.id": 140, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "SAP HANA Disk", "metric.polling.time": 1800, "metric.plugin": "saphanadisk", "metric.state": "ENABLE", "metric.type": "SAP HANA", "metric.context": {"plugin.engine": "go", "plugin.id": 141, "metric.polling.min.time": 1800, "timeout": 60}}, {"metric.name": "SAP HANA Host", "metric.polling.time": 400, "metric.plugin": "sapha<PERSON><PERSON>", "metric.state": "ENABLE", "metric.type": "SAP HANA", "metric.context": {"plugin.engine": "go", "plugin.id": 142, "metric.polling.min.time": 400, "timeout": 60}}, {"metric.name": "SAP HANA Service", "metric.polling.time": 400, "metric.plugin": "saphanaservice", "metric.state": "ENABLE", "metric.type": "SAP HANA", "metric.context": {"plugin.engine": "go", "plugin.id": 143, "metric.polling.min.time": 400, "timeout": 60}}, {"metric.name": "SAP HANA Session", "metric.polling.time": 300, "metric.plugin": "saphanasession", "metric.state": "ENABLE", "metric.type": "SAP HANA", "metric.context": {"plugin.engine": "go", "plugin.id": 144, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "SAP HANA Transaction", "metric.polling.time": 300, "metric.plugin": "saphanatransaction", "metric.state": "ENABLE", "metric.type": "SAP HANA", "metric.context": {"plugin.engine": "go", "plugin.id": 145, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "SAP HANA Workload", "metric.polling.time": 300, "metric.plugin": "saphanaworkload", "metric.state": "ENABLE", "metric.type": "SAP HANA", "metric.context": {"plugin.engine": "go", "plugin.id": 146, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "SAP MaxDB", "metric.polling.time": 300, "metric.plugin": "sapmaxdb", "metric.state": "ENABLE", "metric.type": "SAP MaxDB", "metric.context": {"plugin.engine": "python", "plugin.id": 147, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "SAP MaxDB Cache", "metric.polling.time": 400, "metric.plugin": "sapmaxdbcache", "metric.state": "ENABLE", "metric.type": "SAP MaxDB", "metric.context": {"plugin.engine": "python", "plugin.id": 148, "metric.polling.min.time": 400, "timeout": 60}}, {"metric.name": "SAP MaxDB Command Monitor", "metric.polling.time": 400, "metric.plugin": "sapmaxdbcommandmonitor", "metric.state": "ENABLE", "metric.type": "SAP MaxDB", "metric.context": {"plugin.engine": "python", "plugin.id": 149, "metric.polling.min.time": 400, "timeout": 60}}, {"metric.name": "SAP MaxDB Data Volume", "metric.polling.time": 1800, "metric.plugin": "sapmaxdbdatavolume", "metric.state": "ENABLE", "metric.type": "SAP MaxDB", "metric.context": {"plugin.engine": "python", "plugin.id": 150, "metric.polling.min.time": 1800, "timeout": 60}}, {"metric.name": "SAP MaxDB Log Volume", "metric.polling.time": 600, "metric.plugin": "sapmaxdblogvolume", "metric.state": "ENABLE", "metric.type": "SAP MaxDB", "metric.context": {"plugin.engine": "python", "plugin.id": 151, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "SAP MaxDB OMS Heap", "metric.polling.time": 300, "metric.plugin": "sapmaxdbomsheap", "metric.state": "ENABLE", "metric.type": "SAP MaxDB", "metric.context": {"plugin.engine": "python", "plugin.id": 152, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "SAP MaxDB Resource Monitor", "metric.polling.time": 300, "metric.plugin": "sapmaxdbresourcemonitor", "metric.state": "ENABLE", "metric.type": "SAP MaxDB", "metric.context": {"plugin.engine": "python", "plugin.id": 153, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "SAP MaxDB Schema", "metric.polling.time": 600, "metric.plugin": "sapmaxdbschema", "metric.state": "ENABLE", "metric.type": "SAP MaxDB", "metric.context": {"plugin.engine": "python", "plugin.id": 154, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "SharePoint Online", "metric.polling.time": 600, "metric.plugin": "sharepointonline", "metric.state": "ENABLE", "metric.type": "SharePoint Online", "metric.context": {"plugin.engine": "go", "plugin.id": 155, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Site VPN", "metric.polling.time": 600, "metric.state": "ENABLE", "metric.plugin": "sitevpn", "metric.context": {"plugin.engine": "go", "plugin.id": 156, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Network Device Config", "metric.polling.time": 600, "metric.state": "ENABLE", "metric.plugin": "snmp", "metric.context": {"plugin.engine": "go", "plugin.id": 157, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Solar<PERSON>", "metric.polling.time": 300, "metric.plugin": "solaris", "metric.state": "ENABLE", "metric.type": "Solar<PERSON>", "metric.context": {"plugin.engine": "go", "plugin.id": 158, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Solaris CPU Core", "metric.polling.time": 600, "metric.plugin": "solariscpucore", "metric.state": "DISABLE", "metric.type": "Solar<PERSON>", "metric.context": {"plugin.engine": "go", "plugin.id": 159, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Solaris <PERSON>", "metric.polling.time": 1800, "metric.plugin": "solarisdisk", "metric.state": "ENABLE", "metric.type": "Solar<PERSON>", "metric.context": {"plugin.engine": "go", "plugin.id": 160, "metric.polling.min.time": 1800, "timeout": 60}}, {"metric.name": "Solaris Network Interface", "metric.polling.time": 600, "metric.plugin": "solarisnetworkinterface", "metric.state": "ENABLE", "metric.type": "Solar<PERSON>", "metric.context": {"plugin.engine": "go", "plugin.id": 161, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Solaris Process", "metric.polling.time": 300, "metric.plugin": "solarisprocess", "metric.state": "ENABLE", "metric.type": "Solar<PERSON>", "metric.context": {"plugin.engine": "go", "plugin.id": 162, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Spanning Tree Protocol (STP)", "metric.polling.time": 1800, "metric.state": "DISABLE", "metric.plugin": "stp", "metric.context": {"plugin.engine": "go", "plugin.id": 163, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Switch Port Mapper", "metric.polling.time": 1800, "metric.state": "ENABLE", "metric.plugin": "switchportmapper", "metric.context": {"plugin.engine": "go", "plugin.id": 164, "metric.polling.min.time": 1200, "timeout": 240}}, {"metric.name": "Sybase", "metric.polling.time": 300, "metric.plugin": "sybase", "metric.state": "ENABLE", "metric.type": "Sybase", "metric.context": {"plugin.engine": "python", "plugin.id": 165, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Sybase Database", "metric.polling.time": 600, "metric.plugin": "sybasedatabase", "metric.state": "ENABLE", "metric.type": "Sybase", "metric.context": {"plugin.engine": "python", "plugin.id": 166, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Sybase Process", "metric.polling.time": 300, "metric.plugin": "sybaseprocess", "metric.state": "ENABLE", "metric.type": "Sybase", "metric.context": {"plugin.engine": "python", "plugin.id": 167, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Sybase Transaction", "metric.polling.time": 300, "metric.plugin": "sybasetransaction", "metric.state": "ENABLE", "metric.type": "Sybase", "metric.context": {"plugin.engine": "python", "plugin.id": 168, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Symantec Messaging Gateway", "metric.polling.time": 300, "metric.plugin": "symantecemailgateway", "metric.state": "ENABLE", "metric.context": {"plugin.engine": "go", "plugin.id": 169, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "IBM Tape Library", "metric.polling.time": 300, "metric.plugin": "ibmtapelibrary", "metric.state": "ENABLE", "metric.type": "IBM Tape Library", "metric.context": {"plugin.engine": "go", "plugin.id": 252, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Microsoft Teams", "metric.polling.time": 600, "metric.plugin": "teams", "metric.state": "ENABLE", "metric.type": "Microsoft Teams", "metric.context": {"plugin.engine": "go", "plugin.id": 170, "metric.polling.min.time": 600, "timeout": 120}}, {"metric.name": "Apache Tomcat", "metric.polling.time": 300, "metric.plugin": "tomcat", "metric.state": "ENABLE", "metric.type": "Apache Tomcat", "metric.context": {"plugin.engine": "go", "plugin.id": 171, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "URL", "metric.polling.time": 180, "metric.plugin": "url", "metric.state": "ENABLE", "metric.type": "URL", "metric.context": {"plugin.engine": "go", "plugin.id": 172, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "VMware vCenter", "metric.polling.time": 600, "metric.plugin": "vcenter", "metric.state": "ENABLE", "metric.type": "vCenter", "metric.context": {"plugin.engine": "go", "plugin.id": 173, "metric.polling.min.time": 600, "timeout": 90}}, {"metric.name": "VMware vCenter Cluster", "metric.polling.time": 600, "metric.plugin": "vcentercluster", "metric.state": "ENABLE", "metric.type": "vCenter", "metric.context": {"plugin.engine": "go", "plugin.id": 174, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "VMware vCenter Data Center", "metric.polling.time": 300, "metric.plugin": "vcenterdatacenter", "metric.state": "ENABLE", "metric.type": "vCenter", "metric.context": {"plugin.engine": "go", "plugin.id": 175, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "VMware vCenter Data Store", "metric.polling.time": 300, "metric.plugin": "vcenterdatastore", "metric.state": "ENABLE", "metric.type": "vCenter", "metric.context": {"plugin.engine": "go", "plugin.id": 176, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "VLAN", "metric.polling.time": 1800, "metric.state": "ENABLE", "metric.plugin": "vlan", "metric.context": {"plugin.engine": "go", "plugin.id": 177, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Virtual Routing & Forwarding (VRF)", "metric.polling.time": 1800, "metric.state": "DISABLE", "metric.plugin": "vrf", "metric.context": {"plugin.engine": "go", "plugin.id": 178, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "IBM WebSphere", "metric.polling.time": 300, "metric.plugin": "websphere", "metric.state": "ENABLE", "metric.type": "IBM WebSphere", "metric.context": {"plugin.engine": "go", "plugin.id": 179, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "<PERSON><PERSON><PERSON>", "metric.polling.time": 300, "metric.plugin": "wildfly", "metric.state": "ENABLE", "metric.type": "<PERSON><PERSON><PERSON>", "metric.context": {"plugin.engine": "go", "plugin.id": 180, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Windows", "metric.polling.time": 300, "metric.plugin": "windows", "metric.state": "ENABLE", "metric.type": "Windows", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 181, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Windows Cluster", "metric.polling.time": 300, "metric.plugin": "windowscluster", "metric.state": "ENABLE", "metric.type": "Windows Cluster", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 182, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Windows Cluster Disk", "metric.polling.time": 1800, "metric.plugin": "windowsclusterdisk", "metric.state": "ENABLE", "metric.type": "Windows Cluster", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 183, "metric.polling.min.time": 1800, "timeout": 60}}, {"metric.name": "Windows CPU Core", "metric.polling.time": 600, "metric.plugin": "windowscpucore", "metric.state": "DISABLE", "metric.type": "Windows", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 184, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Windows DHCP", "metric.polling.time": 600, "metric.plugin": "windowsdhcp", "metric.state": "ENABLE", "metric.type": "Windows DHCP", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 185, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Windows DHCP Scope", "metric.polling.time": 600, "metric.plugin": "windowsdhcpscope", "metric.state": "ENABLE", "metric.type": "Windows DHCP", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 186, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Windows Directory", "metric.polling.time": 1800, "metric.plugin": "windowsdirectory", "metric.state": "ENABLE", "metric.type": "Windows", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 187, "metric.polling.min.time": 1800, "timeout": 60}}, {"metric.name": "Windows Disk", "metric.polling.time": 1800, "metric.plugin": "windowsdisk", "metric.state": "ENABLE", "metric.type": "Windows", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 188, "metric.polling.min.time": 1800, "timeout": 60}}, {"metric.name": "Windows DNS", "metric.polling.time": 300, "metric.plugin": "windowsdns", "metric.state": "ENABLE", "metric.type": "Windows DNS", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 189, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Windows File", "metric.polling.time": 1800, "metric.plugin": "windowsfile", "metric.state": "ENABLE", "metric.type": "Windows", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 190, "metric.polling.min.time": 1800, "timeout": 60}}, {"metric.name": "Windows Network Interface", "metric.polling.time": 600, "metric.plugin": "windowsnetworkinterface", "metric.state": "ENABLE", "metric.type": "Windows", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 191, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Windows Process", "metric.polling.time": 300, "metric.plugin": "windowsprocess", "metric.state": "ENABLE", "metric.type": "Windows", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 192, "metric.polling.min.time": 300, "timeout": 90}}, {"metric.name": "Windows RDP", "metric.polling.time": 300, "metric.plugin": "windowsrdp", "metric.state": "ENABLE", "metric.type": "Windows RDP", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 193, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Windows Service", "metric.polling.time": 600, "metric.plugin": "windowsservice", "metric.state": "ENABLE", "metric.type": "Windows", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 194, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Windows Task Scheduler", "metric.polling.time": 1800, "metric.plugin": "windowstaskscheduler", "metric.state": "DISABLE", "metric.type": "Windows", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 195, "metric.polling.min.time": 1800, "timeout": 60}}, {"metric.name": "VMware ESXi Disk", "metric.polling.time": 1800, "metric.plugin": "esxidisk", "metric.state": "ENABLE", "metric.type": "VMware ESXi", "metric.context": {"plugin.engine": "go", "plugin.id": 197, "metric.polling.min.time": 1800, "timeout": 60}}, {"metric.name": "VMware ESXi Data Store", "metric.polling.time": 1800, "metric.plugin": "esxidatastore", "metric.state": "ENABLE", "metric.type": "VMware ESXi", "metric.context": {"plugin.engine": "go", "plugin.id": 198, "metric.polling.min.time": 1800, "timeout": 60}}, {"metric.name": "Availability", "metric.polling.time": 60, "metric.state": "ENABLE", "metric.plugin": "availability", "metric.context": {"timeout": 1, "plugin.id": 199, "metric.polling.min.time": 30}}, {"metric.name": "Network Service", "metric.polling.time": 60, "metric.state": "ENABLE", "metric.plugin": "networkservice", "metric.context": {"timeout": 1, "plugin.id": 200, "metric.polling.min.time": 60}}, {"metric.name": "Network Interface", "metric.polling.time": 600, "metric.state": "ENABLE", "metric.plugin": "snmpinterface", "metric.context": {"plugin.engine": "go", "plugin.id": 201, "metric.polling.min.time": 90, "timeout": 120}}, {"metric.name": "SNMP <PERSON>", "metric.polling.time": 300, "metric.state": "ENABLE", "metric.plugin": "snmpscalarmetric", "metric.context": {"plugin.engine": "go", "metric.polling.min.time": 150, "timeout": 60}}, {"metric.name": "SNMP Tabular", "metric.polling.time": 300, "metric.state": "ENABLE", "metric.plugin": "snmptabularmetric", "metric.context": {"plugin.engine": "go", "metric.polling.min.time": 150, "timeout": 60}}, {"metric.name": "Office 365 Status", "metric.polling.time": 300, "metric.plugin": "office365status", "metric.state": "ENABLE", "metric.type": "Office 365", "metric.context": {"plugin.id": 202, "metric.polling.min.time": 300, "plugin.engine": "go", "timeout": 60}}, {"metric.name": "Exchange Online Status", "metric.polling.time": 300, "metric.plugin": "exchangeonlinestatus", "metric.state": "ENABLE", "metric.type": "Exchange Online", "metric.context": {"plugin.id": 203, "metric.polling.min.time": 300, "plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "timeout": 60}}, {"metric.name": "SharePoint Online Status", "metric.polling.time": 300, "metric.plugin": "sharepointonlinestatus", "metric.state": "ENABLE", "metric.type": "SharePoint Online", "metric.context": {"plugin.id": 204, "metric.polling.min.time": 300, "plugin.engine": "go", "timeout": 60}}, {"metric.name": "Microsoft Team Status", "metric.polling.time": 300, "metric.plugin": "microsoftteamsstatus", "metric.state": "ENABLE", "metric.type": "Microsoft Teams", "metric.context": {"plugin.id": 205, "metric.polling.min.time": 300, "plugin.engine": "go", "timeout": 60}}, {"metric.name": "One Drive Status", "metric.polling.time": 300, "metric.plugin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metric.state": "ENABLE", "metric.type": "OneDrive", "metric.context": {"plugin.id": 206, "metric.polling.min.time": 300, "plugin.engine": "go", "timeout": 60}}, {"metric.name": "SAP MaxDB Lock", "metric.polling.time": 400, "metric.plugin": "sapmaxdblock", "metric.state": "ENABLE", "metric.type": "SAP MaxDB", "metric.context": {"plugin.id": 207, "plugin.engine": "python", "metric.polling.min.time": 400, "timeout": 60}}, {"metric.name": "Network Connection", "metric.polling.time": 600, "metric.state": "DISABLE", "metric.plugin": "snmpnetworkconnection", "metric.context": {"plugin.id": 208, "plugin.engine": "go", "metric.polling.min.time": 600, "timeout": 120}}, {"metric.name": "Amazon Cloud Front", "metric.polling.time": 600, "metric.plugin": "amazoncloudfront", "metric.state": "ENABLE", "metric.type": "Amazon CloudFront", "metric.context": {"plugin.id": 209, "plugin.engine": "go", "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Amazon Auto Scaling", "metric.polling.time": 600, "metric.plugin": "awsautoscaling", "metric.state": "ENABLE", "metric.type": "AWS Auto Scaling", "metric.context": {"plugin.id": 210, "plugin.engine": "go", "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Amazon Lambda", "metric.polling.time": 600, "metric.plugin": "awslambda", "metric.state": "ENABLE", "metric.type": "AWS Lambda", "metric.context": {"plugin.id": 211, "plugin.engine": "go", "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Amazon Simple Queue Service", "metric.polling.time": 600, "metric.plugin": "amazonsqs", "metric.state": "ENABLE", "metric.type": "Amazon SQS", "metric.context": {"plugin.id": 212, "plugin.engine": "go", "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Amazon Elastic BeansTalk", "metric.polling.time": 600, "metric.plugin": "awselasticbeanstalk", "metric.state": "ENABLE", "metric.type": "AWS Elastic Beanstalk", "metric.context": {"plugin.id": 213, "plugin.engine": "go", "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Amazon DocumentDB", "metric.polling.time": 600, "metric.plugin": "amazondocumentdb", "metric.state": "ENABLE", "metric.type": "Amazon DocumentDB", "metric.context": {"plugin.id": 214, "plugin.engine": "go", "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Azure Service Bus", "metric.polling.time": 600, "metric.plugin": "azureservicebus", "metric.state": "ENABLE", "metric.type": "Azure Service Bus", "metric.context": {"plugin.id": 215, "plugin.engine": "go", "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Azure Application Gateway", "metric.polling.time": 600, "metric.plugin": "azureapplicationgateway", "metric.state": "ENABLE", "metric.type": "Azure Application Gateway", "metric.context": {"plugin.id": 216, "plugin.engine": "go", "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Azure Function", "metric.polling.time": 600, "metric.plugin": "azurefunction", "metric.state": "ENABLE", "metric.type": "Azure Function", "metric.context": {"plugin.id": 217, "plugin.engine": "go", "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Azure Load Balancer", "metric.polling.time": 600, "metric.plugin": "azureloadbalancer", "metric.state": "ENABLE", "metric.type": "Azure Load Balancer", "metric.context": {"plugin.id": 218, "plugin.engine": "go", "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Azure Virtual Machine Scale Sets", "metric.polling.time": 600, "metric.plugin": "azurevmscaleset", "metric.state": "ENABLE", "metric.type": "Azure VM Scale Set", "metric.context": {"plugin.id": 219, "plugin.engine": "go", "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Azure CDN", "metric.polling.time": 600, "metric.plugin": "azurecdn", "metric.state": "ENABLE", "metric.type": "Azure CDN", "metric.context": {"plugin.engine": "go", "plugin.id": 220, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "IPSLA", "metric.polling.time": 600, "metric.state": "DISABLE", "metric.plugin": "ipsla", "metric.context": {"plugin.engine": "go", "plugin.id": 221, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Aruba Wireless", "metric.polling.time": 300, "metric.plugin": "arubawireless", "metric.state": "ENABLE", "metric.type": "Aruba Wireless", "metric.context": {"plugin.engine": "go", "plugin.id": 222, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Aruba Wireless Access Point", "metric.polling.time": 300, "metric.plugin": "arubawirelessap", "metric.state": "ENABLE", "metric.type": "Aruba Wireless", "metric.context": {"plugin.engine": "go", "plugin.id": 243, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Aruba Wireless Client", "metric.polling.time": 600, "metric.plugin": "arubawirelessclient", "metric.state": "ENABLE", "metric.type": "Aruba Wireless", "metric.context": {"plugin.engine": "go", "plugin.id": 223, "metric.polling.min.time": 600, "timeout": 120}}, {"metric.name": "Aruba Wireless Rogue AP", "metric.polling.time": 1200, "metric.plugin": "arubawirelessrogueap", "metric.state": "ENABLE", "metric.type": "Aruba Wireless", "metric.context": {"plugin.engine": "go", "plugin.id": 224, "metric.polling.min.time": 600, "timeout": 120}}, {"metric.name": "IBM WebSphere Servlet", "metric.polling.time": 600, "metric.plugin": "websphereservlet", "metric.state": "ENABLE", "metric.type": "IBM WebSphere", "metric.context": {"plugin.engine": "go", "plugin.id": 225, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "IBM Db2 Backup", "metric.polling.time": 600, "metric.plugin": "db2backup", "metric.state": "ENABLE", "metric.type": "IBM Db2", "metric.context": {"plugin.engine": "python", "plugin.id": 226, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Oracle Database Index", "metric.polling.time": 600, "metric.plugin": "oracleindex", "metric.state": "ENABLE", "metric.type": "Oracle Database", "metric.context": {"plugin.engine": "go", "plugin.id": 227, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Oracle Database RAC", "metric.polling.time": 600, "metric.plugin": "oraclerac", "metric.state": "ENABLE", "metric.type": "Oracle Database", "metric.context": {"plugin.engine": "go", "plugin.id": 228, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "PostgreSQL Index", "metric.polling.time": 600, "metric.plugin": "postgresqlindex", "metric.state": "ENABLE", "metric.type": "PostgreSQL", "metric.context": {"plugin.engine": "go", "plugin.id": 229, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "PostgreSQL Tablespace", "metric.polling.time": 600, "metric.plugin": "postgresqltablespace", "metric.state": "ENABLE", "metric.type": "PostgreSQL", "metric.context": {"plugin.engine": "go", "plugin.id": 230, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "MySQL Index", "metric.polling.time": 600, "metric.plugin": "mysqlindex", "metric.state": "ENABLE", "metric.type": "MySQL", "metric.context": {"plugin.engine": "go", "plugin.id": 231, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "SQL Server Process", "metric.polling.time": 300, "metric.plugin": "mssqlprocess", "metric.state": "ENABLE", "metric.type": "SQL Server", "metric.context": {"plugin.engine": "go", "plugin.id": 232, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Active Directory Replication", "metric.polling.time": 600, "metric.plugin": "activedirectoryreplication", "metric.state": "ENABLE", "metric.type": "Active Directory", "metric.context": {"plugin.engine": "go", "plugin.engine.runtimes": ["go", "python"], "plugin.id": 233, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "SQL Server Backup", "metric.polling.time": 21600, "metric.plugin": "mssqlbackup", "metric.state": "ENABLE", "metric.type": "SQL Server", "metric.context": {"plugin.engine": "go", "plugin.id": 234, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "SQL Server Index", "metric.polling.time": 600, "metric.plugin": "mssqlindex", "metric.state": "ENABLE", "metric.type": "SQL Server", "metric.context": {"plugin.engine": "go", "plugin.id": 235, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "SQL Server Replication", "metric.polling.time": 600, "metric.plugin": "mssqlreplication", "metric.state": "ENABLE", "metric.type": "SQL Server", "metric.context": {"plugin.engine": "go", "plugin.id": 236, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Cisco Hardware Sensor", "metric.polling.time": 600, "metric.state": "ENABLE", "metric.plugin": "ciscohardwaresensor", "metric.context": {"plugin.engine": "go", "plugin.id": 237, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Azure MySQL Server", "metric.polling.time": 1200, "metric.plugin": "azuremysql", "metric.state": "ENABLE", "metric.type": "Azure MySQL Server", "metric.context": {"plugin.engine": "go", "plugin.id": 238, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Azure PostgreSQL Server", "metric.polling.time": 1200, "metric.plugin": "azurepostgresql", "metric.state": "ENABLE", "metric.type": "Azure PostgreSQL Server", "metric.context": {"plugin.engine": "go", "plugin.id": 239, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Custom SSH Script", "metric.polling.time": 300, "metric.plugin": "ssh", "metric.state": "ENABLE", "metric.type": "SSH", "metric.context": {"plugin.engine": "go", "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Custom Powershell Script", "metric.polling.time": 300, "metric.plugin": "powershell", "metric.state": "ENABLE", "metric.type": "Powershell", "metric.context": {"plugin.engine": "go", "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Custom Database Query", "metric.polling.time": 300, "metric.plugin": "database", "metric.state": "ENABLE", "metric.type": "Database", "metric.context": {"plugin.engine": "python", "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "HTTP/HTTPS URL", "metric.polling.time": 300, "metric.plugin": "http", "metric.state": "ENABLE", "metric.type": "HTTP", "metric.context": {"plugin.engine": "go", "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Custom", "metric.polling.time": 300, "metric.plugin": "custom", "metric.state": "ENABLE", "metric.type": "Custom", "metric.context": {"plugin.engine": "python", "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "<PERSON><PERSON>", "metric.polling.time": 600, "metric.state": "ENABLE", "metric.plugin": "ciscostack", "metric.context": {"plugin.engine": "go", "plugin.id": 240, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Object Status", "metric.polling.time": 60, "metric.state": "DISABLE", "metric.plugin": "objectstatus", "metric.context": {"plugin.engine": "go", "plugin.id": 241, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Border Gateway Protocol", "metric.polling.time": 900, "metric.state": "ENABLE", "metric.plugin": "bgp", "metric.context": {"plugin.engine": "go", "plugin.id": 245, "metric.polling.min.time": 600, "timeout": 120}}, {"metric.name": "OSPF Protocol", "metric.polling.time": 900, "metric.state": "ENABLE", "metric.plugin": "ospf", "metric.context": {"plugin.engine": "go", "plugin.id": 246, "metric.polling.min.time": 600, "timeout": 120}}, {"metric.name": "ISIS Protocol", "metric.polling.time": 900, "metric.state": "ENABLE", "metric.plugin": "isis", "metric.context": {"plugin.engine": "go", "plugin.id": 247, "metric.polling.min.time": 600, "timeout": 120}}, {"metric.name": "IPSLA ICMP Echo", "metric.polling.time": 600, "metric.state": "ENABLE", "metric.plugin": "ipslaicmpecho", "metric.context": {"plugin.engine": "go", "plugin.id": 248, "metric.polling.min.time": 300, "timeout": 120}}, {"metric.name": "IPSLA ICMP Jitter", "metric.polling.time": 600, "metric.state": "ENABLE", "metric.plugin": "ipslaicmpjitter", "metric.context": {"plugin.engine": "go", "plugin.id": 249, "metric.polling.min.time": 300, "timeout": 120}}, {"metric.name": "IPSLA Path Echo", "metric.polling.time": 600, "metric.state": "ENABLE", "metric.plugin": "ipslapathecho", "metric.context": {"plugin.engine": "go", "plugin.id": 250, "metric.polling.min.time": 300, "timeout": 120}}, {"metric.name": "Prism", "metric.polling.time": 300, "metric.plugin": "prism", "metric.state": "ENABLE", "metric.type": "Prism", "metric.context": {"plugin.engine": "go", "plugin.id": 253, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "Prism Cluster", "metric.polling.time": 3600, "metric.plugin": "prismcluster", "metric.state": "ENABLE", "metric.type": "Prism", "metric.context": {"plugin.engine": "go", "plugin.id": 254, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Prism Disk", "metric.polling.time": 300, "metric.plugin": "prismdisk", "metric.state": "ENABLE", "metric.type": "Prism", "metric.context": {"plugin.engine": "go", "plugin.id": 255, "metric.polling.min.time": 150, "timeout": 60}}, {"metric.name": "Prism Storage", "metric.polling.time": 300, "metric.plugin": "prismstorage", "metric.state": "ENABLE", "metric.type": "Prism", "metric.context": {"plugin.engine": "go", "plugin.id": 256, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "Nutanix", "metric.polling.time": 300, "metric.plugin": "nutanix", "metric.state": "ENABLE", "metric.type": "Nutanix", "metric.context": {"plugin.engine": "go", "plugin.id": 257, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "Nutanix VM", "metric.polling.time": 300, "metric.plugin": "nutanixvm", "metric.state": "ENABLE", "metric.type": "Nutanix", "metric.context": {"plugin.engine": "go", "plugin.id": 258, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "Nutanix Disk", "metric.polling.time": 300, "metric.plugin": "nutanixdisk", "metric.state": "ENABLE", "metric.type": "Nutanix", "metric.context": {"plugin.engine": "go", "plugin.id": 259, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "Cisco vManage", "metric.polling.time": 120, "metric.plugin": "ciscovmanage", "metric.state": "ENABLE", "metric.type": "Cisco vManage", "metric.context": {"plugin.engine": "go", "plugin.id": 260, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Cisco vSmart", "metric.polling.time": 120, "metric.plugin": "ciscovsmart", "metric.state": "ENABLE", "metric.type": "Cisco vSmart", "metric.context": {"plugin.engine": "go", "plugin.id": 261, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Cisco vBond", "metric.polling.time": 120, "metric.plugin": "ciscovbond", "metric.state": "ENABLE", "metric.type": "Cisco vBond", "metric.context": {"plugin.engine": "go", "plugin.id": 262, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Cisco vEdge", "metric.polling.time": 120, "metric.plugin": "ciscovedge", "metric.state": "ENABLE", "metric.type": "Cisco vEdge", "metric.context": {"plugin.engine": "go", "plugin.id": 263, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Cisco vEdge BGP Route", "metric.polling.time": 600, "metric.plugin": "ciscovedgebgproute", "metric.state": "ENABLE", "metric.type": "Cisco vEdge", "metric.context": {"plugin.engine": "go", "plugin.id": 264, "metric.polling.min.time": 150, "timeout": 60}}, {"metric.name": "Cisco vEdge BGP Neighbor", "metric.polling.time": 600, "metric.plugin": "ciscovedgebgpneighbor", "metric.state": "ENABLE", "metric.type": "Cisco vEdge", "metric.context": {"plugin.engine": "go", "plugin.id": 265, "metric.polling.min.time": 150, "timeout": 60}}, {"metric.name": "Cisco vEdge Interface", "metric.polling.time": 300, "metric.plugin": "ciscovedgeinterface", "metric.state": "ENABLE", "metric.type": "Cisco vEdge", "metric.context": {"plugin.engine": "go", "plugin.id": 266, "metric.polling.min.time": 150, "timeout": 120}}, {"metric.name": "Cisco vEdge Tunnel", "metric.polling.time": 120, "metric.plugin": "ciscovedgetunnel", "metric.state": "ENABLE", "metric.type": "Cisco vEdge", "metric.context": {"plugin.engine": "go", "plugin.id": 267, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Cisco vEdge TLOC", "metric.polling.time": 120, "metric.plugin": "ciscovedgetloc", "metric.state": "ENABLE", "metric.type": "Cisco vEdge", "metric.context": {"plugin.engine": "go", "plugin.id": 268, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Cisco vManage Site", "metric.polling.time": 300, "metric.plugin": "ciscovmanagesite", "metric.state": "ENABLE", "metric.type": "Cisco vManage", "metric.context": {"plugin.engine": "go", "plugin.id": 269, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "Cisco vEdge Hardware Sensor", "metric.polling.time": 300, "metric.plugin": "ciscovedgehardwaresensor", "metric.state": "ENABLE", "metric.type": "Cisco vEdge", "metric.context": {"plugin.engine": "go", "plugin.id": 270, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "<PERSON><PERSON>", "metric.polling.time": 120, "metric.plugin": "cisco<PERSON><PERSON>", "metric.state": "ENABLE", "metric.type": "<PERSON><PERSON>", "metric.context": {"plugin.engine": "go", "plugin.id": 271, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Cisco Meraki Network", "metric.polling.time": 120, "metric.plugin": "ciscomerakinetwork", "metric.state": "ENABLE", "metric.type": "<PERSON><PERSON>", "metric.context": {"plugin.engine": "go", "plugin.id": 272, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Cisco Meraki Client", "metric.polling.time": 120, "metric.plugin": "ciscomerakiclient", "metric.state": "ENABLE", "metric.type": "<PERSON><PERSON>", "metric.context": {"plugin.engine": "go", "plugin.id": 273, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Cisco Meraki Security", "metric.polling.time": 120, "metric.plugin": "ciscomerakisecurity", "metric.state": "ENABLE", "metric.type": "Cisco Meraki Security", "metric.context": {"plugin.engine": "go", "plugin.id": 274, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Cisco Meraki Subnet", "metric.polling.time": 120, "metric.plugin": "ciscomerakisubnet", "metric.state": "ENABLE", "metric.type": "Cisco Meraki Security", "metric.context": {"plugin.engine": "go", "plugin.id": 275, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Cisco Meraki VLAN", "metric.polling.time": 120, "metric.plugin": "ciscomerakivlan", "metric.state": "ENABLE", "metric.type": "Cisco Meraki Security", "metric.context": {"plugin.engine": "go", "plugin.id": 276, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Cisco Meraki VPN", "metric.polling.time": 120, "metric.plugin": "ciscomerakivpn", "metric.state": "ENABLE", "metric.type": "<PERSON><PERSON>", "metric.context": {"plugin.engine": "go", "plugin.id": 277, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Cisco Meraki Interface", "metric.polling.time": 60, "metric.plugin": "ciscomerakiinterface", "metric.state": "ENABLE", "metric.type": "Cisco Meraki Security", "metric.context": {"plugin.engine": "go", "plugin.id": 278, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Cisco Meraki Switch", "metric.polling.time": 60, "metric.plugin": "c<PERSON><PERSON><PERSON><PERSON>witch", "metric.state": "ENABLE", "metric.type": "Cisco Meraki Switch", "metric.context": {"plugin.engine": "go", "plugin.id": 279, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Cisco Meraki STP", "metric.polling.time": 60, "metric.plugin": "ciscomerakistp", "metric.state": "ENABLE", "metric.type": "Cisco Meraki Switch", "metric.context": {"plugin.engine": "go", "plugin.id": 280, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Cisco Meraki Radio", "metric.polling.time": 120, "metric.plugin": "ciscomerakiradio", "metric.state": "ENABLE", "metric.type": "Cisco Meraki Radio", "metric.context": {"plugin.engine": "go", "plugin.id": 281, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Cisco Meraki Channel", "metric.polling.time": 120, "metric.plugin": "ciscomerakichannel", "metric.state": "ENABLE", "metric.type": "Cisco Meraki Radio", "metric.context": {"plugin.engine": "go", "plugin.id": 282, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Cisco Meraki <PERSON>net", "metric.polling.time": 120, "metric.plugin": "ciscomerakiethernet", "metric.state": "ENABLE", "metric.type": "Cisco Meraki Radio", "metric.context": {"plugin.engine": "go", "plugin.id": 283, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Cisco Meraki SSID", "metric.polling.time": 120, "metric.plugin": "ciscomerakissid", "metric.state": "ENABLE", "metric.type": "Cisco Meraki Radio", "metric.context": {"plugin.engine": "go", "plugin.id": 284, "metric.polling.min.time": 60, "timeout": 60}}, {"metric.name": "Extreme Stack", "metric.polling.time": 600, "metric.state": "ENABLE", "metric.plugin": "extremestack", "metric.context": {"plugin.engine": "go", "plugin.id": 285, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "SNMP Metric", "metric.polling.time": 300, "metric.state": "ENABLE", "metric.plugin": "snmpmetric", "metric.context": {"plugin.engine": "go", "metric.polling.min.time": 150, "timeout": 60}}, {"metric.name": "NetApp ONTAP Cluster", "metric.polling.time": 300, "metric.plugin": "netappontapcluster", "metric.state": "ENABLE", "metric.type": "NetApp ONTAP Cluster", "metric.context": {"plugin.engine": "go", "plugin.id": 286, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "HPE StoreOnce", "metric.polling.time": 300, "metric.plugin": "hpestoreonce", "metric.state": "ENABLE", "metric.type": "HPE StoreOnce", "metric.context": {"plugin.engine": "go", "plugin.id": 287, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "NetApp ONTAP Cluster Node", "metric.polling.time": 300, "metric.plugin": "netappontapclusternode", "metric.state": "ENABLE", "metric.type": "NetApp ONTAP Cluster", "metric.context": {"plugin.engine": "go", "plugin.id": 288, "metric.polling.min.time": 180, "timeout": 60}}, {"metric.name": "NetApp ONTAP Cluster Hardware Sensor", "metric.polling.time": 300, "metric.plugin": "netappontapclusterhardwaresensor", "metric.state": "ENABLE", "metric.type": "NetApp ONTAP Cluster", "metric.context": {"plugin.engine": "go", "plugin.id": 289, "metric.polling.min.time": 180, "timeout": 120}}, {"metric.name": "NetApp ONTAP Cluster Interface", "metric.polling.time": 300, "metric.plugin": "netappontapclusterinterface", "metric.state": "ENABLE", "metric.type": "NetApp ONTAP Cluster", "metric.context": {"plugin.engine": "go", "plugin.id": 290, "metric.polling.min.time": 180, "timeout": 120}}, {"metric.name": "NetApp ONTAP Cluster Disk", "metric.polling.time": 300, "metric.plugin": "netappontapclusterdisk", "metric.state": "ENABLE", "metric.type": "NetApp ONTAP Cluster", "metric.context": {"plugin.engine": "go", "plugin.id": 291, "metric.polling.min.time": 180, "timeout": 120}}, {"metric.name": "NetApp ONTAP Cluster Storage", "metric.polling.time": 300, "metric.plugin": "netappontapclusterstorage", "metric.state": "ENABLE", "metric.type": "NetApp ONTAP Cluster", "metric.context": {"plugin.engine": "go", "plugin.id": 292, "metric.polling.min.time": 180, "timeout": 120}}, {"metric.name": "NetApp ONTAP Cluster Port", "metric.polling.time": 300, "metric.plugin": "netappontapclusterport", "metric.state": "ENABLE", "metric.type": "NetApp ONTAP Cluster", "metric.context": {"plugin.engine": "go", "plugin.id": 293, "metric.polling.min.time": 180, "timeout": 120}}, {"metric.name": "NetApp ONTAP Cluster SVM", "metric.polling.time": 300, "metric.plugin": "netappontapclustersvm", "metric.state": "ENABLE", "metric.type": "NetApp ONTAP Cluster", "metric.context": {"plugin.engine": "go", "plugin.id": 294, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "HPE StoreOnce Disk", "metric.polling.time": 300, "metric.plugin": "hpestoreoncedisk", "metric.state": "ENABLE", "metric.type": "HPE StoreOnce", "metric.context": {"plugin.engine": "go", "plugin.id": 295, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "HPE StoreOnce Service Set", "metric.polling.time": 300, "metric.plugin": "hpestoreonceserviceset", "metric.state": "ENABLE", "metric.type": "HPE StoreOnce", "metric.context": {"plugin.engine": "go", "plugin.id": 296, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "HPE StoreOnce Port", "metric.polling.time": 300, "metric.plugin": "hpestoreonceport", "metric.state": "ENABLE", "metric.type": "HPE StoreOnce", "metric.context": {"plugin.engine": "go", "plugin.id": 297, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "Cisco ACI", "metric.polling.time": 300, "metric.plugin": "c<PERSON><PERSON><PERSON>", "metric.state": "ENABLE", "metric.type": "Cisco ACI", "metric.context": {"plugin.engine": "go", "plugin.id": 298, "metric.polling.min.time": 180, "timeout": 120}}, {"metric.name": "Cisco ACI Tenant", "metric.polling.time": 900, "metric.plugin": "ciscoacitenant", "metric.state": "ENABLE", "metric.type": "Cisco ACI", "metric.context": {"plugin.engine": "go", "plugin.id": 299, "metric.polling.min.time": 600, "timeout": 300}}, {"metric.name": "Cisco ACI Fabric", "metric.polling.time": 300, "metric.plugin": "ciscoacifabric", "metric.state": "ENABLE", "metric.type": "Cisco ACI", "metric.context": {"plugin.engine": "go", "plugin.id": 300, "metric.polling.min.time": 180, "timeout": 120}}, {"metric.name": "Cisco ACI Endpoint", "metric.polling.time": 300, "metric.plugin": "ciscoaciendpoint", "metric.state": "ENABLE", "metric.type": "Cisco ACI", "metric.context": {"plugin.engine": "go", "plugin.id": 301, "metric.polling.min.time": 180, "timeout": 120}}, {"metric.name": "<PERSON>er", "metric.polling.time": 300, "metric.state": "ENABLE", "metric.plugin": "docker", "metric.type": "Docker Container", "metric.context": {"plugin.engine": "go", "plugin.id": 302, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Docker Container", "metric.polling.time": 600, "metric.state": "ENABLE", "metric.plugin": "dockercontainer", "metric.type": "Docker Container", "metric.context": {"plugin.engine": "go", "plugin.id": 303, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Docker Container Process", "metric.polling.time": 600, "metric.state": "ENABLE", "metric.plugin": "dockercontainerprocess", "metric.type": "Docker Container", "metric.context": {"plugin.engine": "go", "plugin.id": 304, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "Docker Container <PERSON>", "metric.polling.time": 600, "metric.state": "ENABLE", "metric.plugin": "dockercontainerdisk", "metric.type": "Docker Container", "metric.context": {"plugin.engine": "go", "plugin.id": 305, "metric.polling.min.time": 600, "timeout": 60}}, {"metric.name": "HPE Primera", "metric.polling.time": 300, "metric.plugin": "hpeprimera", "metric.state": "ENABLE", "metric.type": "HPE Primera", "metric.context": {"plugin.engine": "go", "plugin.id": 306, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "HPE Primera Disk", "metric.polling.time": 300, "metric.plugin": "hpeprimeradisk", "metric.state": "ENABLE", "metric.type": "HPE Primera", "metric.context": {"plugin.engine": "go", "plugin.id": 307, "metric.polling.min.time": 120, "timeout": 120}}, {"metric.name": "HPE Primera Host", "metric.polling.time": 300, "metric.plugin": "hpeprimerahost", "metric.state": "ENABLE", "metric.type": "HPE Primera", "metric.context": {"plugin.engine": "go", "plugin.id": 308, "metric.polling.min.time": 120, "timeout": 120}}, {"metric.name": "HPE Primera Node", "metric.polling.time": 300, "metric.plugin": "hpeprimeranode", "metric.state": "ENABLE", "metric.type": "HPE Primera", "metric.context": {"plugin.engine": "go", "plugin.id": 309, "metric.polling.min.time": 120, "timeout": 120}}, {"metric.name": "HPE Primera Volume", "metric.polling.time": 300, "metric.plugin": "hpeprimeravolume", "metric.state": "ENABLE", "metric.type": "HPE Primera", "metric.context": {"plugin.engine": "go", "plugin.id": 310, "metric.polling.min.time": 120, "timeout": 120}}, {"metric.name": "HPE Primera Port", "metric.polling.time": 300, "metric.plugin": "hpeprimeraport", "metric.state": "ENABLE", "metric.type": "HPE Primera", "metric.context": {"plugin.engine": "go", "plugin.id": 311, "metric.polling.min.time": 120, "timeout": 120}}, {"metric.name": "HPE 3PAR", "metric.polling.time": 300, "metric.plugin": "hpe3par", "metric.state": "ENABLE", "metric.type": "HPE 3PAR", "metric.context": {"plugin.engine": "go", "plugin.id": 312, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "HPE 3PAR Disk", "metric.polling.time": 300, "metric.plugin": "hpe3pardisk", "metric.state": "ENABLE", "metric.type": "HPE 3PAR", "metric.context": {"plugin.engine": "go", "plugin.id": 313, "metric.polling.min.time": 120, "timeout": 120}}, {"metric.name": "HPE 3PAR Host", "metric.polling.time": 300, "metric.plugin": "hpe3parhost", "metric.state": "ENABLE", "metric.type": "HPE 3PAR", "metric.context": {"plugin.engine": "go", "plugin.id": 314, "metric.polling.min.time": 120, "timeout": 120}}, {"metric.name": "HPE 3PAR Node", "metric.polling.time": 300, "metric.plugin": "hpe3parnode", "metric.state": "ENABLE", "metric.type": "HPE 3PAR", "metric.context": {"plugin.engine": "go", "plugin.id": 315, "metric.polling.min.time": 120, "timeout": 120}}, {"metric.name": "HPE 3PAR Volume", "metric.polling.time": 300, "metric.plugin": "hpe3parvolume", "metric.state": "ENABLE", "metric.type": "HPE 3PAR", "metric.context": {"plugin.engine": "go", "plugin.id": 316, "metric.polling.min.time": 120, "timeout": 120}}, {"metric.name": "HPE 3PAR Port", "metric.polling.time": 300, "metric.plugin": "hpe3parport", "metric.state": "ENABLE", "metric.type": "HPE 3PAR", "metric.context": {"plugin.engine": "go", "plugin.id": 317, "metric.polling.min.time": 120, "timeout": 120}}, {"metric.name": "IBM AS/400", "metric.polling.time": 300, "metric.plugin": "ibmas400", "metric.state": "ENABLE", "metric.type": "IBM AS/400", "metric.context": {"plugin.engine": "go", "plugin.id": 318, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "IBM AS/400 Pool", "metric.polling.time": 300, "metric.plugin": "ibmas400pool", "metric.state": "ENABLE", "metric.type": "IBM AS/400", "metric.context": {"plugin.engine": "go", "plugin.id": 319, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "IBM AS/400 Job", "metric.polling.time": 300, "metric.plugin": "ibmas400job", "metric.state": "ENABLE", "metric.type": "IBM AS/400", "metric.context": {"plugin.engine": "go", "plugin.id": 320, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "IBM AS/400 Message", "metric.polling.time": 300, "metric.plugin": "ibmas400message", "metric.state": "ENABLE", "metric.type": "IBM AS/400", "metric.context": {"plugin.engine": "go", "plugin.id": 321, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "IBM AS/400 Spool", "metric.polling.time": 300, "metric.plugin": "ibmas400spool", "metric.state": "ENABLE", "metric.type": "IBM AS/400", "metric.context": {"plugin.engine": "go", "plugin.id": 322, "metric.polling.min.time": 300, "timeout": 60}}, {"metric.name": "Dell EMC Unity", "metric.polling.time": 300, "metric.plugin": "dellemcunity", "metric.state": "ENABLE", "metric.type": "Dell EMC Unity", "metric.context": {"plugin.engine": "go", "plugin.id": 323, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "Dell EMC Unity Enclosure", "metric.polling.time": 300, "metric.plugin": "dellemcunityenclosure", "metric.state": "ENABLE", "metric.type": "Dell EMC Unity", "metric.context": {"plugin.engine": "go", "plugin.id": 324, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "Dell EMC Unity File Share", "metric.polling.time": 300, "metric.plugin": "dellemcunityfileshare", "metric.state": "ENABLE", "metric.type": "Dell EMC Unity", "metric.context": {"plugin.engine": "go", "plugin.id": 325, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "Dell EMC Unity File System", "metric.polling.time": 300, "metric.plugin": "dellemcunityfilesystem", "metric.state": "ENABLE", "metric.type": "Dell EMC Unity", "metric.context": {"plugin.engine": "go", "plugin.id": 326, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "Dell EMC Unity Storage", "metric.polling.time": 300, "metric.plugin": "dellemcunitystorage", "metric.state": "ENABLE", "metric.type": "Dell EMC Unity", "metric.context": {"plugin.engine": "go", "plugin.id": 327, "metric.polling.min.time": 120, "timeout": 60}}, {"metric.name": "Dell EMC Unity Storage Processor", "metric.polling.time": 300, "metric.plugin": "dellemcunitystorageprocessor", "metric.state": "ENABLE", "metric.type": "Dell EMC Unity", "metric.context": {"plugin.engine": "go", "plugin.id": 328, "metric.polling.min.time": 120, "timeout": 60}}]