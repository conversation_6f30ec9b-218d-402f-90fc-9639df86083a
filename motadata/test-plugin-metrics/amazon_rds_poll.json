{"127.0.0.1": {"10": {"metadata.fields": {}}, "_type": "1", "cloud.access.id": "********************", "cloud.secret.key": "pu6mxO0hJxJHQ3/ee5d4rNmErG/sKvlfVoYh+uvv", "cloud.type": "AWS Cloud", "credential.profile.name": "AWS-Cloud-Test1655203749779", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 41, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "AWS-Cloud-Test1655203749780", "discovery.object.type": "AWS Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:19:50.876 pm 14/06/2022", "discovery.total.objects": 41, "errors": [], "event.id": 1769***********, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Amazon RDS", "metric.object": ***************, "metric.plugin": "amazonrds", "metric.polling.min.time": 600, "metric.polling.time": 300, "metric.state": "ENABLE", "metric.type": "Amazon RDS", "object.account.id": "************", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:28.74 pm 14/06/2022", "object.custom.fields": {}, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 15, "object.name": "motadata(ap-south-1)", "object.region": "ap-south-1", "object.state": "ENABLE", "object.target": "motadata(ap-south-1)", "object.type": "Amazon RDS", "object.vendor": "AWS Cloud", "plugin.engine": "go", "plugin.id": 10, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"10": {"metadata.fields": {}}, "aws.availability.zone": "ap-south-1b", "aws.rds.allocated.storage.bytes": ***********, "aws.rds.backup.retention.period": 2, "aws.rds.cpu.credit.balance": 144, "aws.rds.cpu.credit.usage": 0.226651, "aws.rds.cpu.percent": 3.*************, "aws.rds.database.connections": 0, "aws.rds.db.instance.arn": "arn:aws:rds:ap-south-1:************:db:motadata", "aws.rds.db.name": "motadata", "aws.rds.disk.io.bytes.per.sec": 1369.*************, "aws.rds.disk.io.latency.ms": 0, "aws.rds.disk.io.ops.per.sec": 0.11701576369502349, "aws.rds.disk.io.read.bytes.per.sec": 0, "aws.rds.disk.io.read.latency.ms": 0, "aws.rds.disk.io.read.ops.per.sec": 0, "aws.rds.disk.io.write.bytes.per.sec": 1369.*************, "aws.rds.disk.io.write.latency.ms": 0, "aws.rds.disk.io.write.ops.per.sec": 0.11701576369502349, "aws.rds.endpoint.address": "motadata.c9jmmb1g40bk.ap-south-1.rds.amazonaws.com", "aws.rds.endpoint.port": 3306, "aws.rds.engine": "mysql", "aws.rds.engine.version": "5.7.37", "aws.rds.instance.created.time": " 1195 days 23 hours 11 minutes 47 seconds", "aws.rds.instance.created.time.sec": 103331507, "aws.rds.instance.id": "motadata", "aws.rds.latest.restore.time": " 0 day 0 hour 5 minutes 37 seconds", "aws.rds.license.model": "general-public-license", "aws.rds.master.username": "motadata", "aws.rds.memory.free.bytes": 346775552, "aws.rds.multi(a-z).deployment": "false", "aws.rds.network.in.traffic.bytes.per.sec": 891.4428043663596, "aws.rds.network.out.traffic.bytes.per.sec": 5982.130021230003, "aws.rds.network.traffic.bytes.per.sec": 6873.572825596362, "aws.rds.preferred.backup.window": "19:33-20:03", "aws.rds.preferred.maintenance.window": "tue:09:52-tue:10:22", "aws.rds.publicly.accessible": "true", "aws.rds.region": "ap-south-1", "aws.rds.storage.encrypted": "false", "aws.rds.storage.free.bytes": 20380844032, "aws.rds.storage.type": "gp2", "aws.rds.swap.memory.used.bytes": 1048576, "aws.status": "available", "status": "Up"}, "status": "succeed", "timeout": 60}}