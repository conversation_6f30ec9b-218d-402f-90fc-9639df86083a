{"fd00:1:1:1::132": {"result": {}, "errors": [{"error": "ibm_db_dbi::OperationalError: Exception('[IBM][CLI Driver] SQL30081N  A communication error has been detected. Communication protocol being used: \"TCP/IP\".  Communication API being used: \"SOCKETS\".  Location where the error was detected: \"fd00:1:1:1::132\".  Communication function detecting the error: \"connect\".  Protocol specific error code(s): \"111\", \"*\", \"*\".  SQLSTATE=08001 SQLCODE=-30081')\n\tat /home/<USER>/workspace-new/motadata/Plugin%20Engine%20(Python)/pluginengine/clients/databaseclient.py at line 592\n\tat /home/<USER>/workspace-new/motadata/Plugin%20Engine%20(Python)/pluginengine/clients/databaseclient.py at line 90\n\tat /home/<USER>/workspace-new/motadata/Plugin%20Engine%20(Python)/pluginengine/plugins/metric/db2backup/plugin.py at line 55\n\tat /home/<USER>/workspace-new/motadata/Plugin%20Engine%20(Python)/tests/unit/plugins/metric/test_db2backupplugin.py at line 26\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/python.py at line 183\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/python.py at line 1641\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 162\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 255\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 311\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 255\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 215\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 126\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 109\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/main.py at line 348\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/main.py at line 323\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/main.py at line 269\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/main.py at line 316\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/config/__init__.py at line 163\n\tat /home/<USER>/pycharm/helpers/pycharm/_jb_pytest_runner.py at line 45\n", "message": "Invalid port 50000, Please verify that port 50000 is up and Motadata is able to connect", "error.code": "MD002"}]}}