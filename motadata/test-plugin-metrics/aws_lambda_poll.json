{"127.0.0.1": {"_type": "1", "cloud.access.id": "********************", "cloud.secret.key": "pu6mxO0hJxJHQ3/ee5d4rNmErG/sKvlfVoYh+uvv", "cloud.type": "AWS Cloud", "credential.profile.name": "AWS-Cloud-Test1655203749779", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 41, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "AWS-Cloud-Test1655203749780", "discovery.object.type": "AWS Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:19:50.876 pm 14/06/2022", "discovery.total.objects": 41, "errors": [], "event.id": 1769937********, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Amazon Lambda", "metric.object": ***************, "metric.plugin": "awslambda", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "AWS Lambda", "object.account.id": "************", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:28.151 pm 14/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 18, "object.name": "aaalamda(us-east-1)", "object.region": "us-east-1", "object.state": "ENABLE", "object.target": "aaalamda(us-east-1)", "object.type": "AWS Lambda", "object.vendor": "AWS Cloud", "plugin.engine": "go", "plugin.id": 211, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"aws.lambda.code.size.bytes": 269, "aws.lambda.last.modified": "2021-06-04T06:29:43.473+0000", "aws.lambda.memory.size.bytes": *********, "aws.lambda.runtime.environment": "python3.7", "aws.lambda.version": "$LATEST"}, "status": "succeed", "timeout": 60}}