{"************": {"result": {"dhcp.scope": [{"dhcp.scope": "70.0.0.0", "dhcp.scope.name": "test", "dhcp.scope.description": "", "dhcp.scope.superscope": "", "dhcp.scope.subnet.mask": "*********", "dhcp.scope.start.range": "**********", "dhcp.scope.end.range": "************", "dhcp.scope.lease.duration": "8.00:00:00", "dhcp.scope.nap.profile": "", "dhcp.scope.nap.enabled": "False", "dhcp.scope.delay.ms": 0.0, "dhcp.scope.state": "Active", "dhcp.scope.type": "Dhcp", "dhcp.scope.maxbootp.clients": 4294967295.0, "dhcp.scope.activated.policies": "True", "dhcp.scope.available.ip.addresses": 200.0, "dhcp.scope.used.ip.addresses": 0.0, "dhcp.scope.pending.offers": 0.0, "dhcp.scope.reserved.ip.addresses": 0, "dhcp.scope.utilization.percent": 0.0, "dhcp.scope.available.ip.addresses.of.current.server": 0.0, "dhcp.scope.available.ip.addresses.of.partner.server": 0.0, "dhcp.scope.used.ip.addresses.of.current.server": 0.0, "dhcp.scope.used.ip.addresses.of.partner.server": 0.0, "dhcp.scope.free.percent": 100.0}]}, "errors": []}, "************": {"result": {"dhcp.scope": [{"dhcp.scope": "70.0.0.0", "dhcp.scope.name": "test", "dhcp.scope.description": "", "dhcp.scope.superscope": "", "dhcp.scope.subnet.mask": "*********", "dhcp.scope.start.range": "**********", "dhcp.scope.end.range": "************", "dhcp.scope.lease.duration": "8.00:00:00", "dhcp.scope.nap.profile": "", "dhcp.scope.nap.enabled": "False", "dhcp.scope.delay.ms": 0.0, "dhcp.scope.state": "Active", "dhcp.scope.type": "Dhcp", "dhcp.scope.maxbootp.clients": 4294967295.0, "dhcp.scope.activated.policies": "True", "dhcp.scope.available.ip.addresses": 200.0, "dhcp.scope.used.ip.addresses": 0.0, "dhcp.scope.pending.offers": 0.0, "dhcp.scope.reserved.ip.addresses": 0, "dhcp.scope.utilization.percent": 0.0, "dhcp.scope.available.ip.addresses.of.current.server": 0.0, "dhcp.scope.available.ip.addresses.of.partner.server": 0.0, "dhcp.scope.used.ip.addresses.of.current.server": 0.0, "dhcp.scope.used.ip.addresses.of.partner.server": 0.0, "dhcp.scope.free.percent": 100.0}]}, "errors": []}, "************": {"result": {"dhcp.client": [{"dhcp.client.scope": "**********", "dhcp.client.ip.address": "**********", "dhcp.client.type": "None", "dhcp.client": "0f"}], "dhcp.scope": [{"dhcp.scope": "**********", "dhcp.scope.subnet.mask": "*************", "dhcp.scope.state": "Active", "dhcp.scope.name": "testingscope", "dhcp.scope.description": "", "dhcp.scope.start.range": "**********", "dhcp.scope.end.range": "***********", "dhcp.scope.type": "DHCP ONLY", "dhcp.scope.reserved.ip.addresses": 1}]}, "errors": []}}