{"127.0.0.1": {"15": {"metadata.fields": {"CosmosAccountType": "Non-Production", "defaultExperience": "Core (SQL)", "hidden-cosmos-mmspecial": ""}}, "_type": "1", "cloud.client.id": "aaa130f3-5ed7-404b-87f5-90a5a42750ea", "cloud.secret.key": "**********************************", "cloud.tenant.id": "5b4acec3-2592-4187-9489-98c654cc6c87", "cloud.type": "Azure Cloud", "credential.profile.name": "Azure-Cloud-Test1655203790900", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 24, "discovery.event.processors": [***************], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "Azure-Cloud-Test-*************", "discovery.object.type": "Azure Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:20:25.670 pm 14/06/2022", "discovery.total.objects": 24, "errors": [], "event.id": ***************, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Azure Cosmos DB", "metric.object": ***************, "metric.plugin": "azurecosmosdb", "metric.polling.min.time": 300, "metric.polling.time": 300, "metric.state": "ENABLE", "metric.type": "Azure Cosmos DB", "object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:35.747 pm 14/06/2022", "object.custom.fields": {"***************": "Core (SQL)", "***************": "Non-Production"}, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 49, "object.name": "motadatacosmosdb(motadata-freetier)", "object.resource.group": "motadata-freetier", "object.state": "ENABLE", "object.target": "motadatacosmosdb(motadata-freetier)", "object.type": "Azure Cosmos DB", "object.vendor": "Azure Cloud", "plugin.engine": "go", "plugin.id": 15, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"15": {"metadata.fields": {"CosmosAccountType": "Non-Production", "defaultExperience": "Core (SQL)", "hidden-cosmos-mmspecial": ""}}, "azure.cosmos.db.available.storage.bytes": ***********, "azure.cosmos.db.data.usage.bytes": 0, "azure.cosmos.db.document.endpoint": "https://motadatacosmosdb.documents.azure.com:443/", "azure.cosmos.db.document.quota.bytes": ***********, "azure.cosmos.db.documents": 3, "azure.cosmos.db.index.usage.bytes": 0, "azure.cosmos.db.metadata.requests": 150, "azure.cosmos.db.mongo.request.charge": 0, "azure.cosmos.db.mongo.requests": 0, "azure.cosmos.db.normalized.ru.consumption.percent": 0, "azure.cosmos.db.provisioned.throughput": 400, "azure.cosmos.db.read.locations": "West US", "azure.cosmos.db.region.id": "motadatacosmosdb-westus", "azure.cosmos.db.request.units": 0, "azure.cosmos.db.requests.rate": 0, "azure.cosmos.db.resource.group": "motadata-freetier", "azure.cosmos.db.resource.id": "/subscriptions/5807cfb0-41a6-4da6-b920-71d934d4a2af/resourceGroups/motadata-freetier/providers/Microsoft.DocumentDB/databaseAccounts/motadatacosmosdb", "azure.cosmos.db.resource.name": "motadatacosmosdb", "azure.cosmos.db.service.availability.percent": 100, "azure.cosmos.db.write.locations": "West US", "azure.location": "West US", "azure.provisioning.state": "Succeeded", "azure.type": "Microsoft.DocumentDB/databaseAccounts", "status": "Up"}, "status": "succeed", "timeout": 60}}