{"fd00:1:1:1::132": {"errors": [], "metric.timeout": 20, "object.ip": "fd00:1:1:1::132", "objects": [{"object.name": "httpd.exe|\"C:\\Program Files\\Apache Software Foundation\\Apache2.4\\bin\\httpd.exe\" -k runservice", "object.type": "system.process"}, {"object.name": "mysqld.exe|\"C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin\\mysqld.exe\" --defaults-file=\"C:\\ProgramData\\MySQL\\MySQL Server 8.0\\my.ini\" MySQL80", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkboot\" \"10788\" \"-x4\"", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkboot\" \"10800\" \"-x3\"", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkboot\" \"10768\" \"-x5\"", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkavlauncher\" \"10760\"", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkcol\" \"10752\"", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkbgworker=0\" \"10736\"", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkboot\" \"10840\" \"-x4\"", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkboot\" \"10948\" \"-x3\"", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkboot\" \"10944\" \"-x5\"", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkavlauncher\" \"10828\"", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkcol\" \"10820\"", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkbgworker=0\" \"10804\"", "object.type": "system.process"}, {"object.name": "cmd.exe|C:\\WINDOWS\\system32\\cmd.exe /C powershell -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);Get-WmiObject Win32_Process | where-object {$_.name -eq 'postgres.exe'} | select name, commandline, processid, @{Name='username';Expression={$_.GetOwner().User}} | Format-List;(Get-Counter '\\Process(postgres*)\\ID Process','\\Process(postgres*)\\% Processor Time','\\Process(postgres*)\\Thread Count','\\Process(postgres*)\\Working Set','\\Process(postgres*)\\IO Data", "object.type": "system.process"}, {"object.name": "powershell.exe|powershell -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);Get-WmiObject Win32_Process | where-object {$_.name -eq 'postgres.exe'} | select name, commandline, processid, @{Name='username';Expression={$_.GetOwner().User}} | Format-List;(Get-Counter '\\Process(postgres*)\\ID Process','\\Process(postgres*)\\% Processor Time','\\Process(postgres*)\\Thread Count','\\Process(postgres*)\\Working Set','\\Process(postgres*)\\IO Data Bytes/sec','\\Process(postgres*)\\IO", "object.type": "system.process"}], "password": "Mind@123", "port": 5985, "result": {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Up", "system.process": "httpd.exe|\"C:\\Program Files\\Apache Software Foundation\\Apache2.4\\bin\\httpd.exe\" -k runservice", "system.process.command": "\"C:\\Program Files\\Apache Software Foundation\\Apache2.4\\bin\\httpd.exe\" -k runservice", "system.process.cpu.percent": 0, "system.process.handles": 243, "system.process.id": "3316", "system.process.io.bytes.per.sec": 0, "system.process.io.ops.per.sec": 0, "system.process.memory.used.bytes": 2301952, "system.process.name": "httpd.exe", "system.process.threads": 4, "system.process.started.time": "11/10/2021 6:03:39 PM", "system.process.virtual.memory.bytes": 4389052416}, {"status": "Up", "system.process": "mysqld.exe|\"C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin\\mysqld.exe\" --defaults-file=\"C:\\ProgramData\\MySQL\\MySQL Server 8.0\\my.ini\" MySQL80", "system.process.command": "\"C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin\\mysqld.exe\" --defaults-file=\"C:\\ProgramData\\MySQL\\MySQL Server 8.0\\my.ini\" MySQL80", "system.process.cpu.percent": 0, "system.process.handles": 134, "system.process.id": "3492", "system.process.io.bytes.per.sec": 0, "system.process.io.ops.per.sec": 0, "system.process.memory.used.bytes": 2576384, "system.process.name": "mysqld.exe", "system.process.threads": 3, "system.process.started.time": "11/10/2021 6:03:39 PM", "system.process.virtual.memory.bytes": 4432797696}, {"status": "Up", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkboot\" \"10788\" \"-x4\"", "system.process.command": "\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkboot\" \"10788\" \"-x4\"", "system.process.cpu.percent": 0, "system.process.handles": 1718, "system.process.id": "6700", "system.process.io.bytes.per.sec": 0, "system.process.io.ops.per.sec": 0, "system.process.memory.used.bytes": 806912, "system.process.name": "postgres.exe", "system.process.threads": 2, "system.process.started.time": "11/10/2021 6:03:49 PM", "system.process.virtual.memory.bytes": 4592381952}, {"status": "Down", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkboot\" \"10800\" \"-x3\"", "system.process.command": "\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkboot\" \"10800\" \"-x3\"", "system.process.name": "postgres.exe"}, {"status": "Down", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkboot\" \"10768\" \"-x5\"", "system.process.command": "\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkboot\" \"10768\" \"-x5\"", "system.process.name": "postgres.exe"}, {"status": "Down", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkavlauncher\" \"10760\"", "system.process.command": "\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkavlauncher\" \"10760\"", "system.process.name": "postgres.exe"}, {"status": "Down", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkcol\" \"10752\"", "system.process.command": "\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkcol\" \"10752\"", "system.process.name": "postgres.exe"}, {"status": "Down", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkbgworker=0\" \"10736\"", "system.process.command": "\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkbgworker=0\" \"10736\"", "system.process.name": "postgres.exe"}, {"status": "Down", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkboot\" \"10840\" \"-x4\"", "system.process.command": "\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkboot\" \"10840\" \"-x4\"", "system.process.name": "postgres.exe"}, {"status": "Down", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkboot\" \"10948\" \"-x3\"", "system.process.command": "\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkboot\" \"10948\" \"-x3\"", "system.process.name": "postgres.exe"}, {"status": "Down", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkboot\" \"10944\" \"-x5\"", "system.process.command": "\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkboot\" \"10944\" \"-x5\"", "system.process.name": "postgres.exe"}, {"status": "Down", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkavlauncher\" \"10828\"", "system.process.command": "\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkavlauncher\" \"10828\"", "system.process.name": "postgres.exe"}, {"status": "Down", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkcol\" \"10820\"", "system.process.command": "\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkcol\" \"10820\"", "system.process.name": "postgres.exe"}, {"status": "Down", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkbgworker=0\" \"10804\"", "system.process.command": "\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkbgworker=0\" \"10804\"", "system.process.name": "postgres.exe"}, {"status": "Down", "system.process": "cmd.exe|C:\\WINDOWS\\system32\\cmd.exe /C powershell -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);Get-WmiObject Win32_Process", "system.process.command": "C:\\WINDOWS\\system32\\cmd.exe /C powershell -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);Get-WmiObject Win32_Process", "system.process.name": "cmd.exe"}, {"status": "Down", "system.process": "powershell.exe|powershell -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);Get-WmiObject Win32_Process", "system.process.command": "powershell -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);Get-WmiObject Win32_Process", "system.process.name": "powershell.exe"}], "system.process.network.connection": [{"system.process": "httpd.exe|\"C:\\Program Files\\Apache Software Foundation\\Apache2.4\\bin\\httpd.exe\" -k runservice", "system.process.destination.ip": "0", "system.process.destination.port": "0.0.0.0", "system.process.source.ip": "fd00:1:1:1::132", "system.process.source.port": "80"}, {"system.process": "httpd.exe|\"C:\\Program Files\\Apache Software Foundation\\Apache2.4\\bin\\httpd.exe\" -k runservice", "system.process.destination.ip": "fd00:1:1:1::132", "system.process.destination.port": "0", "system.process.source.ip": "fd00:1:1:1::132", "system.process.source.port": "80"}]}, "username": "admin"}, "************": {"errors": [], "metric.timeout": 20, "object.ip": "************", "objects": [{"object.name": "httpd.exe|\"C:\\Program Files\\Apache Software Foundation\\Apache2.4\\bin\\httpd.exe\" -k runservice", "object.type": "system.process"}, {"object.name": "mysqld.exe|\"C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin\\mysqld.exe\" --defaults-file=\"C:\\ProgramData\\MySQL\\MySQL Server 8.0\\my.ini\" MySQL80", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkboot\" \"10788\" \"-x4\"", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkboot\" \"10800\" \"-x3\"", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkboot\" \"10768\" \"-x5\"", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkavlauncher\" \"10760\"", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkcol\" \"10752\"", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkbgworker=0\" \"10736\"", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkboot\" \"10840\" \"-x4\"", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkboot\" \"10948\" \"-x3\"", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkboot\" \"10944\" \"-x5\"", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkavlauncher\" \"10828\"", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkcol\" \"10820\"", "object.type": "system.process"}, {"object.name": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkbgworker=0\" \"10804\"", "object.type": "system.process"}, {"object.name": "cmd.exe|C:\\WINDOWS\\system32\\cmd.exe /C powershell -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);Get-WmiObject Win32_Process | where-object {$_.name -eq 'postgres.exe'} | select name, commandline, processid, @{Name='username';Expression={$_.GetOwner().User}} | Format-List;(Get-Counter '\\Process(postgres*)\\ID Process','\\Process(postgres*)\\% Processor Time','\\Process(postgres*)\\Thread Count','\\Process(postgres*)\\Working Set','\\Process(postgres*)\\IO Data", "object.type": "system.process"}, {"object.name": "powershell.exe|powershell -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);Get-WmiObject Win32_Process | where-object {$_.name -eq 'postgres.exe'} | select name, commandline, processid, @{Name='username';Expression={$_.GetOwner().User}} | Format-List;(Get-Counter '\\Process(postgres*)\\ID Process','\\Process(postgres*)\\% Processor Time','\\Process(postgres*)\\Thread Count','\\Process(postgres*)\\Working Set','\\Process(postgres*)\\IO Data Bytes/sec','\\Process(postgres*)\\IO", "object.type": "system.process"}], "password": "mind@123", "port": 5985, "result": {"correlation.metrics": ["system.process.network.connection"], "system.process": [{"status": "Down", "system.process": "httpd.exe|\"C:\\Program Files\\Apache Software Foundation\\Apache2.4\\bin\\httpd.exe\" -k runservice", "system.process.command": "\"C:\\Program Files\\Apache Software Foundation\\Apache2.4\\bin\\httpd.exe\" -k runservice", "system.process.name": "httpd.exe"}, {"status": "Down", "system.process": "mysqld.exe|\"C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin\\mysqld.exe\" --defaults-file=\"C:\\ProgramData\\MySQL\\MySQL Server 8.0\\my.ini\" MySQL80", "system.process.command": "\"C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin\\mysqld.exe\" --defaults-file=\"C:\\ProgramData\\MySQL\\MySQL Server 8.0\\my.ini\" MySQL80", "system.process.name": "mysqld.exe"}, {"status": "Down", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkboot\" \"10788\" \"-x4\"", "system.process.command": "\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkboot\" \"10788\" \"-x4\"", "system.process.name": "postgres.exe"}, {"status": "Down", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkboot\" \"10800\" \"-x3\"", "system.process.command": "\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkboot\" \"10800\" \"-x3\"", "system.process.name": "postgres.exe"}, {"status": "Down", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkboot\" \"10768\" \"-x5\"", "system.process.command": "\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkboot\" \"10768\" \"-x5\"", "system.process.name": "postgres.exe"}, {"status": "Down", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkavlauncher\" \"10760\"", "system.process.command": "\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkavlauncher\" \"10760\"", "system.process.name": "postgres.exe"}, {"status": "Down", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkcol\" \"10752\"", "system.process.command": "\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkcol\" \"10752\"", "system.process.name": "postgres.exe"}, {"status": "Down", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkbgworker=0\" \"10736\"", "system.process.command": "\"C:/Program Files/PostgreSQL/10/bin/postgres.exe\" \"--forkbgworker=0\" \"10736\"", "system.process.name": "postgres.exe"}, {"status": "Down", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkboot\" \"10840\" \"-x4\"", "system.process.command": "\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkboot\" \"10840\" \"-x4\"", "system.process.name": "postgres.exe"}, {"status": "Down", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkboot\" \"10948\" \"-x3\"", "system.process.command": "\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkboot\" \"10948\" \"-x3\"", "system.process.name": "postgres.exe"}, {"status": "Down", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkboot\" \"10944\" \"-x5\"", "system.process.command": "\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkboot\" \"10944\" \"-x5\"", "system.process.name": "postgres.exe"}, {"status": "Down", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkavlauncher\" \"10828\"", "system.process.command": "\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkavlauncher\" \"10828\"", "system.process.name": "postgres.exe"}, {"status": "Down", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkcol\" \"10820\"", "system.process.command": "\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkcol\" \"10820\"", "system.process.name": "postgres.exe"}, {"status": "Down", "system.process": "postgres.exe|\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkbgworker=0\" \"10804\"", "system.process.command": "\"C:/Program Files/PostgreSQL/11/bin/postgres.exe\" \"--forkbgworker=0\" \"10804\"", "system.process.name": "postgres.exe"}, {"status": "Down", "system.process": "cmd.exe|C:\\WINDOWS\\system32\\cmd.exe /C powershell -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);Get-WmiObject Win32_Process", "system.process.command": "C:\\WINDOWS\\system32\\cmd.exe /C powershell -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);Get-WmiObject Win32_Process", "system.process.name": "cmd.exe"}, {"status": "Down", "system.process": "powershell.exe|powershell -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);Get-WmiObject Win32_Process", "system.process.command": "powershell -command \"$Host.UI.RawUI.BufferSize = New-Object Management.Automation.Host.Size (512,50);Get-WmiObject Win32_Process", "system.process.name": "powershell.exe"}]}, "username": "readonly"}}