{"**************": {"8": {"metadata.fields": {"Name": "ServiceOps_Saas_AMI"}}, "_type": "1", "cloud.access.id": "********************", "cloud.secret.key": "pu6mxO0hJxJHQ3/ee5d4rNmErG/sKvlfVoYh+uvv", "cloud.type": "AWS Cloud", "credential.profile.name": "AWS-Cloud-Test1655203749779", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 41, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "AWS-Cloud-Test1655203749780", "discovery.object.type": "AWS Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:19:50.876 pm 14/06/2022", "discovery.total.objects": 41, "errors": [], "event.id": 1769***********, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Amazon EC2", "metric.object": ***************, "metric.plugin": "amazonec2", "metric.polling.min.time": 300, "metric.polling.time": 300, "metric.state": "ENABLE", "metric.type": "Amazon EC2", "object.account.id": "************", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:28.480 pm 14/06/2022", "object.custom.fields": {"***************": "ServiceOps_Saas_AMI"}, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 34, "object.instance.id": "i-0b9137b141eb85569", "object.ip": "**************", "object.name": "ServiceOps_Saas_AMI(ap-south-1)", "object.region": "ap-south-1", "object.state": "ENABLE", "object.target": "ServiceOps_Saas_AMI(ap-south-1)", "object.type": "Amazon EC2", "object.vendor": "AWS Cloud", "plugin.engine": "go", "plugin.id": 8, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"8": {"metadata.fields": {"Name": "ServiceOps_Saas_AMI"}}, "aws.availability.zone": "ap-south-1b", "aws.ec2": "ServiceOps_Saas_AMI(ap-south-1)", "aws.ec2.ami.launch.index": 0, "aws.ec2.architecture": "x86_64", "aws.ec2.cpu": 1, "aws.ec2.cpu.credit.balance": 161.369967, "aws.ec2.cpu.credit.usage": 0.006577, "aws.ec2.cpu.percent": 0.1339168287487284, "aws.ec2.cpu.surplus.credit.balance": 0, "aws.ec2.cpu.surplus.credit.charged": 0, "aws.ec2.disk.io.bytes.per.sec": 0, "aws.ec2.disk.io.ops.per.sec": 0, "aws.ec2.disk.io.read.bytes.per.sec": 0, "aws.ec2.disk.io.read.ops.per.sec": 0, "aws.ec2.disk.io.write.bytes.per.sec": 0, "aws.ec2.disk.io.write.ops.per.sec": 0, "aws.ec2.id": "i-0b9137b141eb85569", "aws.ec2.image.id": "ami-0675118d2eaeabbc7", "aws.ec2.key.pair.name": "serviceops-ec2", "aws.ec2.launch.time": " 4 days 4 hours 54 minutes 49 seconds", "aws.ec2.launch.time.sec": 363289, "aws.ec2.monitoring": "disabled", "aws.ec2.network.bytes.per.sec": 201400, "aws.ec2.network.in.bytes.per.sec": 118891, "aws.ec2.network.in.packets": 84.6, "aws.ec2.network.out.bytes.per.sec": 82509, "aws.ec2.network.out.packets": 79.4, "aws.ec2.network.packets": 164, "aws.ec2.private.dns.name": "ip-172-31-1-233.ap-south-1.compute.internal", "aws.ec2.private.ip.address": "************", "aws.ec2.public.dns.name": "ec2-15-206-212-226.ap-south-1.compute.amazonaws.com", "aws.ec2.public.ip.address": "**************", "aws.ec2.region": "ap-south-1", "aws.ec2.root.device.name": "/dev/sda1", "aws.ec2.root.device.type": "ebs", "aws.ec2.status.check.failed": 0, "aws.ec2.status.check.failed.instance": 0, "aws.ec2.status.check.failed.system": 0, "aws.ec2.type": "t2.micro", "aws.ec2.virtualization": "hvm", "aws.state": "running", "status": "Up"}, "status": "succeed", "timeout": 60}, "**************": {"8": {"metadata.fields": {"Name": "ServiceOps_Saas_AMI"}}, "_type": "1", "cloud.access.id": "********************", "cloud.secret.key": "pu6mxO0hJxJHQ3/ee5d4rNmErG/sKvlfVoYh+uvv", "cloud.type": "AWS Cloud", "credential.profile.name": "AWS-Cloud-Test1655203749779", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 41, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "AWS-Cloud-Test1655203749780", "discovery.object.type": "AWS Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:19:50.876 pm 14/06/2022", "discovery.total.objects": 41, "errors": [], "event.id": 1769***********, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Amazon EC2", "metric.object": ***************, "metric.plugin": "amazonec2", "metric.polling.min.time": 300, "metric.polling.time": 300, "metric.state": "ENABLE", "metric.type": "Amazon EC2", "object.account.id": "************", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:28.480 pm 14/06/2022", "object.custom.fields": {"***************": "ServiceOps_Saas_AMI"}, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 34, "object.instance.id": "i-0b9137b141eb85569", "object.ip": "**************", "object.name": "ServiceOps_Saas_AMI(ap-south-1)", "object.region": "ap-south-1", "object.state": "ENABLE", "object.target": "ServiceOps_Saas_AMI(ap-south-1)", "object.type": "Amazon EC2", "object.vendor": "AWS Cloud", "plugin.engine": "go", "plugin.id": 8, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"8": {"metadata.fields": {"Name": "ServiceOps_Saas_AMI"}}, "aws.availability.zone": "ap-south-1b", "aws.ec2": "ServiceOps_Saas_AMI(ap-south-1)", "aws.ec2.ami.launch.index": 0, "aws.ec2.architecture": "x86_64", "aws.ec2.cpu": 1, "aws.ec2.cpu.credit.balance": 161.369967, "aws.ec2.cpu.credit.usage": 0.006577, "aws.ec2.cpu.percent": 0.1339168287487284, "aws.ec2.cpu.surplus.credit.balance": 0, "aws.ec2.cpu.surplus.credit.charged": 0, "aws.ec2.disk.io.bytes.per.sec": 0, "aws.ec2.disk.io.ops.per.sec": 0, "aws.ec2.disk.io.read.bytes.per.sec": 0, "aws.ec2.disk.io.read.ops.per.sec": 0, "aws.ec2.disk.io.write.bytes.per.sec": 0, "aws.ec2.disk.io.write.ops.per.sec": 0, "aws.ec2.id": "i-0b9137b141eb85569", "aws.ec2.image.id": "ami-0675118d2eaeabbc7", "aws.ec2.key.pair.name": "serviceops-ec2", "aws.ec2.launch.time": " 4 days 4 hours 54 minutes 49 seconds", "aws.ec2.launch.time.sec": 363289, "aws.ec2.monitoring": "disabled", "aws.ec2.network.bytes.per.sec": 201400, "aws.ec2.network.in.bytes.per.sec": 118891, "aws.ec2.network.in.packets": 84.6, "aws.ec2.network.out.bytes.per.sec": 82509, "aws.ec2.network.out.packets": 79.4, "aws.ec2.network.packets": 164, "aws.ec2.private.dns.name": "ip-172-31-1-233.ap-south-1.compute.internal", "aws.ec2.private.ip.address": "************", "aws.ec2.public.dns.name": "ec2-15-206-212-226.ap-south-1.compute.amazonaws.com", "aws.ec2.public.ip.address": "**************", "aws.ec2.region": "ap-south-1", "aws.ec2.root.device.name": "/dev/sda1", "aws.ec2.root.device.type": "ebs", "aws.ec2.status.check.failed": 0, "aws.ec2.status.check.failed.instance": 0, "aws.ec2.status.check.failed.system": 0, "aws.ec2.type": "t2.micro", "aws.ec2.virtualization": "hvm", "aws.state": "running", "status": "Up"}, "status": "succeed", "timeout": 60}, "*************": {"8": {"metadata.fields": {"Name": "ServiceOps_Saas_AMI"}}, "_type": "1", "cloud.access.id": "********************", "cloud.secret.key": "pu6mxO0hJxJHQ3/ee5d4rNmErG/sKvlfVoYh+uvv", "cloud.type": "AWS Cloud", "credential.profile.name": "AWS-Cloud-Test1655203749779", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 41, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "AWS-Cloud-Test1655203749780", "discovery.object.type": "AWS Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:19:50.876 pm 14/06/2022", "discovery.total.objects": 41, "errors": [], "event.id": 1769***********, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Amazon EC2", "metric.object": ***************, "metric.plugin": "amazonec2", "metric.polling.min.time": 300, "metric.polling.time": 300, "metric.state": "ENABLE", "metric.type": "Amazon EC2", "object.account.id": "************", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:28.480 pm 14/06/2022", "object.custom.fields": {"***************": "ServiceOps_Saas_AMI"}, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 34, "object.instance.id": "i-0b9137b141eb85569", "object.ip": "*************", "object.name": "ServiceOps_Saas_AMI(ap-south-1)", "object.region": "ap-south-1", "object.state": "ENABLE", "object.target": "ServiceOps_Saas_AMI(ap-south-1)", "object.type": "Amazon EC2", "object.vendor": "AWS Cloud", "plugin.engine": "go", "plugin.id": 8, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"8": {"metadata.fields": {"Name": "ServiceOps_Saas_AMI"}}, "aws.availability.zone": "ap-south-1b", "aws.ec2": "ServiceOps_Saas_AMI(ap-south-1)", "aws.ec2.ami.launch.index": 0, "aws.ec2.architecture": "x86_64", "aws.ec2.cpu": 1, "aws.ec2.cpu.credit.balance": 161.369967, "aws.ec2.cpu.credit.usage": 0.006577, "aws.ec2.cpu.percent": 0.1339168287487284, "aws.ec2.cpu.surplus.credit.balance": 0, "aws.ec2.cpu.surplus.credit.charged": 0, "aws.ec2.disk.io.bytes.per.sec": 0, "aws.ec2.disk.io.ops.per.sec": 0, "aws.ec2.disk.io.read.bytes.per.sec": 0, "aws.ec2.disk.io.read.ops.per.sec": 0, "aws.ec2.disk.io.write.bytes.per.sec": 0, "aws.ec2.disk.io.write.ops.per.sec": 0, "aws.ec2.id": "i-0b9137b141eb85569", "aws.ec2.image.id": "ami-0675118d2eaeabbc7", "aws.ec2.key.pair.name": "serviceops-ec2", "aws.ec2.launch.time": " 4 days 4 hours 54 minutes 49 seconds", "aws.ec2.launch.time.sec": 363289, "aws.ec2.monitoring": "disabled", "aws.ec2.network.bytes.per.sec": 201400, "aws.ec2.network.in.bytes.per.sec": 118891, "aws.ec2.network.in.packets": 84.6, "aws.ec2.network.out.bytes.per.sec": 82509, "aws.ec2.network.out.packets": 79.4, "aws.ec2.network.packets": 164, "aws.ec2.private.dns.name": "ip-172-31-1-233.ap-south-1.compute.internal", "aws.ec2.private.ip.address": "************", "aws.ec2.public.dns.name": "ec2-15-206-212-226.ap-south-1.compute.amazonaws.com", "aws.ec2.public.ip.address": "*************", "aws.ec2.region": "ap-south-1", "aws.ec2.root.device.name": "/dev/sda1", "aws.ec2.root.device.type": "ebs", "aws.ec2.status.check.failed": 0, "aws.ec2.status.check.failed.instance": 0, "aws.ec2.status.check.failed.system": 0, "aws.ec2.type": "t2.micro", "aws.ec2.virtualization": "hvm", "aws.state": "running", "status": "Up"}, "status": "succeed", "timeout": 60}, "************": {"8": {"metadata.fields": {"Name": "ServiceOps_Saas_AMI"}}, "_type": "1", "cloud.access.id": "********************", "cloud.secret.key": "pu6mxO0hJxJHQ3/ee5d4rNmErG/sKvlfVoYh+uvv", "cloud.type": "AWS Cloud", "credential.profile.name": "AWS-Cloud-Test1655203749779", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 41, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "AWS-Cloud-Test1655203749780", "discovery.object.type": "AWS Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:19:50.876 pm 14/06/2022", "discovery.total.objects": 41, "errors": [], "event.id": 1769***********, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Amazon EC2", "metric.object": ***************, "metric.plugin": "amazonec2", "metric.polling.min.time": 300, "metric.polling.time": 300, "metric.state": "ENABLE", "metric.type": "Amazon EC2", "object.account.id": "************", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:28.480 pm 14/06/2022", "object.custom.fields": {"***************": "ServiceOps_Saas_AMI"}, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 34, "object.instance.id": "i-0b9137b141eb85569", "object.ip": "************", "object.name": "ServiceOps_Saas_AMI(ap-south-1)", "object.region": "ap-south-1", "object.state": "ENABLE", "object.target": "ServiceOps_Saas_AMI(ap-south-1)", "object.type": "Amazon EC2", "object.vendor": "AWS Cloud", "plugin.engine": "go", "plugin.id": 8, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"8": {"metadata.fields": {"Name": "ServiceOps_Saas_AMI"}}, "aws.availability.zone": "ap-south-1b", "aws.ec2": "ServiceOps_Saas_AMI(ap-south-1)", "aws.ec2.ami.launch.index": 0, "aws.ec2.architecture": "x86_64", "aws.ec2.cpu": 1, "aws.ec2.cpu.credit.balance": 161.369967, "aws.ec2.cpu.credit.usage": 0.006577, "aws.ec2.cpu.percent": 0.1339168287487284, "aws.ec2.cpu.surplus.credit.balance": 0, "aws.ec2.cpu.surplus.credit.charged": 0, "aws.ec2.disk.io.bytes.per.sec": 0, "aws.ec2.disk.io.ops.per.sec": 0, "aws.ec2.disk.io.read.bytes.per.sec": 0, "aws.ec2.disk.io.read.ops.per.sec": 0, "aws.ec2.disk.io.write.bytes.per.sec": 0, "aws.ec2.disk.io.write.ops.per.sec": 0, "aws.ec2.id": "i-0b9137b141eb85569", "aws.ec2.image.id": "ami-0675118d2eaeabbc7", "aws.ec2.key.pair.name": "serviceops-ec2", "aws.ec2.launch.time": " 4 days 4 hours 54 minutes 49 seconds", "aws.ec2.launch.time.sec": 363289, "aws.ec2.monitoring": "disabled", "aws.ec2.network.bytes.per.sec": 201400, "aws.ec2.network.in.bytes.per.sec": 118891, "aws.ec2.network.in.packets": 84.6, "aws.ec2.network.out.bytes.per.sec": 82509, "aws.ec2.network.out.packets": 79.4, "aws.ec2.network.packets": 164, "aws.ec2.private.dns.name": "ip-172-31-1-233.ap-south-1.compute.internal", "aws.ec2.private.ip.address": "************", "aws.ec2.public.dns.name": "ec2-15-206-212-226.ap-south-1.compute.amazonaws.com", "aws.ec2.public.ip.address": "************", "aws.ec2.region": "ap-south-1", "aws.ec2.root.device.name": "/dev/sda1", "aws.ec2.root.device.type": "ebs", "aws.ec2.status.check.failed": 0, "aws.ec2.status.check.failed.instance": 0, "aws.ec2.status.check.failed.system": 0, "aws.ec2.type": "t2.micro", "aws.ec2.virtualization": "hvm", "aws.state": "running", "status": "Up"}, "status": "succeed", "timeout": 60}, "127.0.0.1": {"8": {"metadata.fields": {"Name": "ServiceOps_Saas_AMI"}}, "_type": "1", "cloud.access.id": "********************", "cloud.secret.key": "pu6mxO0hJxJHQ3/ee5d4rNmErG/sKvlfVoYh+uvv", "cloud.type": "AWS Cloud", "credential.profile.name": "AWS-Cloud-Test1655203749779", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 41, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "AWS-Cloud-Test1655203749780", "discovery.object.type": "AWS Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:19:50.876 pm 14/06/2022", "discovery.total.objects": 41, "errors": [], "event.id": 1769***********, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Amazon EC2", "metric.object": ***************, "metric.plugin": "amazonec2", "metric.polling.min.time": 300, "metric.polling.time": 300, "metric.state": "ENABLE", "metric.type": "Amazon EC2", "object.account.id": "************", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:28.480 pm 14/06/2022", "object.custom.fields": {"***************": "ServiceOps_Saas_AMI"}, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 34, "object.instance.id": "i-0b9137b141eb85569", "object.ip": "127.0.0.1", "object.name": "ServiceOps_Saas_AMI(ap-south-1)", "object.region": "ap-south-1", "object.state": "ENABLE", "object.target": "ServiceOps_Saas_AMI(ap-south-1)", "object.type": "Amazon EC2", "object.vendor": "AWS Cloud", "plugin.engine": "go", "plugin.id": 8, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"8": {"metadata.fields": {"Name": "ServiceOps_Saas_AMI"}}, "aws.availability.zone": "ap-south-1b", "aws.ec2": "ServiceOps_Saas_AMI(ap-south-1)", "aws.ec2.ami.launch.index": 0, "aws.ec2.architecture": "x86_64", "aws.ec2.cpu": 1, "aws.ec2.cpu.credit.balance": 161.369967, "aws.ec2.cpu.credit.usage": 0.006577, "aws.ec2.cpu.percent": 0.1339168287487284, "aws.ec2.cpu.surplus.credit.balance": 0, "aws.ec2.cpu.surplus.credit.charged": 0, "aws.ec2.disk.io.bytes.per.sec": 0, "aws.ec2.disk.io.ops.per.sec": 0, "aws.ec2.disk.io.read.bytes.per.sec": 0, "aws.ec2.disk.io.read.ops.per.sec": 0, "aws.ec2.disk.io.write.bytes.per.sec": 0, "aws.ec2.disk.io.write.ops.per.sec": 0, "aws.ec2.id": "i-0b9137b141eb85569", "aws.ec2.image.id": "ami-0675118d2eaeabbc7", "aws.ec2.key.pair.name": "serviceops-ec2", "aws.ec2.launch.time": " 4 days 4 hours 54 minutes 49 seconds", "aws.ec2.launch.time.sec": 363289, "aws.ec2.monitoring": "disabled", "aws.ec2.network.bytes.per.sec": 201400, "aws.ec2.network.in.bytes.per.sec": 118891, "aws.ec2.network.in.packets": 84.6, "aws.ec2.network.out.bytes.per.sec": 82509, "aws.ec2.network.out.packets": 79.4, "aws.ec2.network.packets": 164, "aws.ec2.private.dns.name": "ip-172-31-1-233.ap-south-1.compute.internal", "aws.ec2.private.ip.address": "************", "aws.ec2.public.dns.name": "ec2-15-206-212-226.ap-south-1.compute.amazonaws.com", "aws.ec2.public.ip.address": "************", "aws.ec2.region": "ap-south-1", "aws.ec2.root.device.name": "/dev/sda1", "aws.ec2.root.device.type": "ebs", "aws.ec2.status.check.failed": 0, "aws.ec2.status.check.failed.instance": 0, "aws.ec2.status.check.failed.system": 0, "aws.ec2.type": "t2.micro", "aws.ec2.virtualization": "hvm", "aws.state": "running", "status": "Up"}, "status": "succeed", "timeout": 60}, "*********": {"8": {"metadata.fields": {"Name": "ServiceOps_Saas_AMI"}}, "_type": "1", "cloud.access.id": "********************", "cloud.secret.key": "pu6mxO0hJxJHQ3/ee5d4rNmErG/sKvlfVoYh+uvv", "cloud.type": "AWS Cloud", "credential.profile.name": "AWS-Cloud-Test1655203749779", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 41, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "AWS-Cloud-Test1655203749780", "discovery.object.type": "AWS Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:19:50.876 pm 14/06/2022", "discovery.total.objects": 41, "errors": [], "event.id": 1769***********, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Amazon EC2", "metric.object": ***************, "metric.plugin": "amazonec2", "metric.polling.min.time": 300, "metric.polling.time": 300, "metric.state": "ENABLE", "metric.type": "Amazon EC2", "object.account.id": "************", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:28.480 pm 14/06/2022", "object.custom.fields": {"***************": "ServiceOps_Saas_AMI"}, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 34, "object.instance.id": "i-0b9137b141eb85569", "object.ip": "*********", "object.name": "ServiceOps_Saas_AMI(ap-south-1)", "object.region": "ap-south-1", "object.state": "ENABLE", "object.target": "ServiceOps_Saas_AMI(ap-south-1)", "object.type": "Amazon EC2", "object.vendor": "AWS Cloud", "plugin.engine": "go", "plugin.id": 8, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"8": {"metadata.fields": {"Name": "ServiceOps_Saas_AMI"}}, "aws.availability.zone": "ap-south-1b", "aws.ec2": "ServiceOps_Saas_AMI(ap-south-1)", "aws.ec2.ami.launch.index": 0, "aws.ec2.architecture": "x86_64", "aws.ec2.cpu": 1, "aws.ec2.cpu.credit.balance": 161.369967, "aws.ec2.cpu.credit.usage": 0.006577, "aws.ec2.cpu.percent": 0.1339168287487284, "aws.ec2.cpu.surplus.credit.balance": 0, "aws.ec2.cpu.surplus.credit.charged": 0, "aws.ec2.disk.io.bytes.per.sec": 0, "aws.ec2.disk.io.ops.per.sec": 0, "aws.ec2.disk.io.read.bytes.per.sec": 0, "aws.ec2.disk.io.read.ops.per.sec": 0, "aws.ec2.disk.io.write.bytes.per.sec": 0, "aws.ec2.disk.io.write.ops.per.sec": 0, "aws.ec2.id": "i-0b9137b141eb85569", "aws.ec2.image.id": "ami-0675118d2eaeabbc7", "aws.ec2.key.pair.name": "serviceops-ec2", "aws.ec2.launch.time": " 4 days 4 hours 54 minutes 49 seconds", "aws.ec2.launch.time.sec": 363289, "aws.ec2.monitoring": "disabled", "aws.ec2.network.bytes.per.sec": 201400, "aws.ec2.network.in.bytes.per.sec": 118891, "aws.ec2.network.in.packets": 84.6, "aws.ec2.network.out.bytes.per.sec": 82509, "aws.ec2.network.out.packets": 79.4, "aws.ec2.network.packets": 164, "aws.ec2.private.dns.name": "ip-172-31-1-233.ap-south-1.compute.internal", "aws.ec2.private.ip.address": "************", "aws.ec2.public.dns.name": "ec2-15-206-212-226.ap-south-1.compute.amazonaws.com", "aws.ec2.public.ip.address": "*********", "aws.ec2.region": "ap-south-1", "aws.ec2.root.device.name": "/dev/sda1", "aws.ec2.root.device.type": "ebs", "aws.ec2.status.check.failed": 0, "aws.ec2.status.check.failed.instance": 0, "aws.ec2.status.check.failed.system": 0, "aws.ec2.type": "t2.micro", "aws.ec2.virtualization": "hvm", "aws.state": "running", "status": "Up"}, "status": "succeed", "timeout": 60}, "**************": {"8": {"metadata.fields": {"Name": "ServiceOps_Saas_AMI"}}, "_type": "1", "cloud.access.id": "********************", "cloud.secret.key": "pu6mxO0hJxJHQ3/ee5d4rNmErG/sKvlfVoYh+uvv", "cloud.type": "AWS Cloud", "credential.profile.name": "AWS-Cloud-Test1655203749779", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 41, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "AWS-Cloud-Test1655203749780", "discovery.object.type": "AWS Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:19:50.876 pm 14/06/2022", "discovery.total.objects": 41, "errors": [], "event.id": 1769***********, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Amazon EC2", "metric.object": ***************, "metric.plugin": "amazonec2", "metric.polling.min.time": 300, "metric.polling.time": 300, "metric.state": "ENABLE", "metric.type": "Amazon EC2", "object.account.id": "************", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:28.480 pm 14/06/2022", "object.custom.fields": {"***************": "ServiceOps_Saas_AMI"}, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 34, "object.instance.id": "i-0b9137b141eb85569", "object.ip": "**************", "object.name": "ServiceOps_Saas_AMI(ap-south-1)", "object.region": "ap-south-1", "object.state": "ENABLE", "object.target": "ServiceOps_Saas_AMI(ap-south-1)", "object.type": "Amazon EC2", "object.vendor": "AWS Cloud", "plugin.engine": "go", "plugin.id": 8, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"8": {"metadata.fields": {"Name": "ServiceOps_Saas_AMI"}}, "aws.availability.zone": "ap-south-1b", "aws.ec2": "ServiceOps_Saas_AMI(ap-south-1)", "aws.ec2.ami.launch.index": 0, "aws.ec2.architecture": "x86_64", "aws.ec2.cpu": 1, "aws.ec2.cpu.credit.balance": 161.369967, "aws.ec2.cpu.credit.usage": 0.006577, "aws.ec2.cpu.percent": 0.1339168287487284, "aws.ec2.cpu.surplus.credit.balance": 0, "aws.ec2.cpu.surplus.credit.charged": 0, "aws.ec2.disk.io.bytes.per.sec": 0, "aws.ec2.disk.io.ops.per.sec": 0, "aws.ec2.disk.io.read.bytes.per.sec": 0, "aws.ec2.disk.io.read.ops.per.sec": 0, "aws.ec2.disk.io.write.bytes.per.sec": 0, "aws.ec2.disk.io.write.ops.per.sec": 0, "aws.ec2.id": "i-0b9137b141eb85569", "aws.ec2.image.id": "ami-0675118d2eaeabbc7", "aws.ec2.key.pair.name": "serviceops-ec2", "aws.ec2.launch.time": " 4 days 4 hours 54 minutes 49 seconds", "aws.ec2.launch.time.sec": 363289, "aws.ec2.monitoring": "disabled", "aws.ec2.network.bytes.per.sec": 201400, "aws.ec2.network.in.bytes.per.sec": 118891, "aws.ec2.network.in.packets": 84.6, "aws.ec2.network.out.bytes.per.sec": 82509, "aws.ec2.network.out.packets": 79.4, "aws.ec2.network.packets": 164, "aws.ec2.private.dns.name": "ip-172-31-1-233.ap-south-1.compute.internal", "aws.ec2.private.ip.address": "************", "aws.ec2.public.dns.name": "ec2-15-206-212-226.ap-south-1.compute.amazonaws.com", "aws.ec2.public.ip.address": "**************", "aws.ec2.region": "ap-south-1", "aws.ec2.root.device.name": "/dev/sda1", "aws.ec2.root.device.type": "ebs", "aws.ec2.status.check.failed": 0, "aws.ec2.status.check.failed.instance": 0, "aws.ec2.status.check.failed.system": 0, "aws.ec2.type": "t2.micro", "aws.ec2.virtualization": "hvm", "aws.state": "running", "status": "Up"}, "status": "succeed", "timeout": 60}, "************": {"8": {"metadata.fields": {"Name": "ServiceOps_Saas_AMI"}}, "_type": "1", "cloud.access.id": "********************", "cloud.secret.key": "pu6mxO0hJxJHQ3/ee5d4rNmErG/sKvlfVoYh+uvv", "cloud.type": "AWS Cloud", "credential.profile.name": "AWS-Cloud-Test1655203749779", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 41, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "AWS-Cloud-Test1655203749780", "discovery.object.type": "AWS Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:19:50.876 pm 14/06/2022", "discovery.total.objects": 41, "errors": [], "event.id": 1769***********, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Amazon EC2", "metric.object": ***************, "metric.plugin": "amazonec2", "metric.polling.min.time": 300, "metric.polling.time": 300, "metric.state": "ENABLE", "metric.type": "Amazon EC2", "object.account.id": "************", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:28.480 pm 14/06/2022", "object.custom.fields": {"***************": "ServiceOps_Saas_AMI"}, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 34, "object.instance.id": "i-0b9137b141eb85569", "object.ip": "************", "object.name": "ServiceOps_Saas_AMI(ap-south-1)", "object.region": "ap-south-1", "object.state": "ENABLE", "object.target": "ServiceOps_Saas_AMI(ap-south-1)", "object.type": "Amazon EC2", "object.vendor": "AWS Cloud", "plugin.engine": "go", "plugin.id": 8, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"8": {"metadata.fields": {"Name": "ServiceOps_Saas_AMI"}}, "aws.availability.zone": "ap-south-1b", "aws.ec2": "ServiceOps_Saas_AMI(ap-south-1)", "aws.ec2.ami.launch.index": 0, "aws.ec2.architecture": "x86_64", "aws.ec2.cpu": 1, "aws.ec2.cpu.credit.balance": 161.369967, "aws.ec2.cpu.credit.usage": 0.006577, "aws.ec2.cpu.percent": 0.1339168287487284, "aws.ec2.cpu.surplus.credit.balance": 0, "aws.ec2.cpu.surplus.credit.charged": 0, "aws.ec2.disk.io.bytes.per.sec": 0, "aws.ec2.disk.io.ops.per.sec": 0, "aws.ec2.disk.io.read.bytes.per.sec": 0, "aws.ec2.disk.io.read.ops.per.sec": 0, "aws.ec2.disk.io.write.bytes.per.sec": 0, "aws.ec2.disk.io.write.ops.per.sec": 0, "aws.ec2.id": "i-0b9137b141eb85569", "aws.ec2.image.id": "ami-0675118d2eaeabbc7", "aws.ec2.key.pair.name": "serviceops-ec2", "aws.ec2.launch.time": " 4 days 4 hours 54 minutes 49 seconds", "aws.ec2.launch.time.sec": 363289, "aws.ec2.monitoring": "disabled", "aws.ec2.network.bytes.per.sec": 201400, "aws.ec2.network.in.bytes.per.sec": 118891, "aws.ec2.network.in.packets": 84.6, "aws.ec2.network.out.bytes.per.sec": 82509, "aws.ec2.network.out.packets": 79.4, "aws.ec2.network.packets": 164, "aws.ec2.private.dns.name": "ip-172-31-1-233.ap-south-1.compute.internal", "aws.ec2.private.ip.address": "************", "aws.ec2.public.dns.name": "ec2-15-206-212-226.ap-south-1.compute.amazonaws.com", "aws.ec2.public.ip.address": "************", "aws.ec2.region": "ap-south-1", "aws.ec2.root.device.name": "/dev/sda1", "aws.ec2.root.device.type": "ebs", "aws.ec2.status.check.failed": 0, "aws.ec2.status.check.failed.instance": 0, "aws.ec2.status.check.failed.system": 0, "aws.ec2.type": "t2.micro", "aws.ec2.virtualization": "hvm", "aws.state": "running", "status": "Up"}, "status": "succeed", "timeout": 60}, "*************": {"8": {"metadata.fields": {"Name": "ServiceOps_Saas_AMI"}}, "_type": "1", "cloud.access.id": "********************", "cloud.secret.key": "pu6mxO0hJxJHQ3/ee5d4rNmErG/sKvlfVoYh+uvv", "cloud.type": "AWS Cloud", "credential.profile.name": "AWS-Cloud-Test1655203749779", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 41, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "AWS-Cloud-Test1655203749780", "discovery.object.type": "AWS Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:19:50.876 pm 14/06/2022", "discovery.total.objects": 41, "errors": [], "event.id": 1769***********, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Amazon EC2", "metric.object": ***************, "metric.plugin": "amazonec2", "metric.polling.min.time": 300, "metric.polling.time": 300, "metric.state": "ENABLE", "metric.type": "Amazon EC2", "object.account.id": "************", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:28.480 pm 14/06/2022", "object.custom.fields": {"***************": "ServiceOps_Saas_AMI"}, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 34, "object.instance.id": "i-0b9137b141eb85569", "object.ip": "*************", "object.name": "ServiceOps_Saas_AMI(ap-south-1)", "object.region": "ap-south-1", "object.state": "ENABLE", "object.target": "ServiceOps_Saas_AMI(ap-south-1)", "object.type": "Amazon EC2", "object.vendor": "AWS Cloud", "plugin.engine": "go", "plugin.id": 8, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"8": {"metadata.fields": {"Name": "ServiceOps_Saas_AMI"}}, "aws.availability.zone": "ap-south-1b", "aws.ec2": "ServiceOps_Saas_AMI(ap-south-1)", "aws.ec2.ami.launch.index": 0, "aws.ec2.architecture": "x86_64", "aws.ec2.cpu": 1, "aws.ec2.cpu.credit.balance": 161.369967, "aws.ec2.cpu.credit.usage": 0.006577, "aws.ec2.cpu.percent": 0.1339168287487284, "aws.ec2.cpu.surplus.credit.balance": 0, "aws.ec2.cpu.surplus.credit.charged": 0, "aws.ec2.disk.io.bytes.per.sec": 0, "aws.ec2.disk.io.ops.per.sec": 0, "aws.ec2.disk.io.read.bytes.per.sec": 0, "aws.ec2.disk.io.read.ops.per.sec": 0, "aws.ec2.disk.io.write.bytes.per.sec": 0, "aws.ec2.disk.io.write.ops.per.sec": 0, "aws.ec2.id": "i-0b9137b141eb85569", "aws.ec2.image.id": "ami-0675118d2eaeabbc7", "aws.ec2.key.pair.name": "serviceops-ec2", "aws.ec2.launch.time": " 4 days 4 hours 54 minutes 49 seconds", "aws.ec2.launch.time.sec": 363289, "aws.ec2.monitoring": "disabled", "aws.ec2.network.bytes.per.sec": 201400, "aws.ec2.network.in.bytes.per.sec": 118891, "aws.ec2.network.in.packets": 84.6, "aws.ec2.network.out.bytes.per.sec": 82509, "aws.ec2.network.out.packets": 79.4, "aws.ec2.network.packets": 164, "aws.ec2.private.dns.name": "ip-172-31-1-233.ap-south-1.compute.internal", "aws.ec2.private.ip.address": "************", "aws.ec2.public.dns.name": "ec2-15-206-212-226.ap-south-1.compute.amazonaws.com", "aws.ec2.public.ip.address": "*************", "aws.ec2.region": "ap-south-1", "aws.ec2.root.device.name": "/dev/sda1", "aws.ec2.root.device.type": "ebs", "aws.ec2.status.check.failed": 0, "aws.ec2.status.check.failed.instance": 0, "aws.ec2.status.check.failed.system": 0, "aws.ec2.type": "t2.micro", "aws.ec2.virtualization": "hvm", "aws.state": "running", "status": "Up"}, "status": "succeed", "timeout": 60}}