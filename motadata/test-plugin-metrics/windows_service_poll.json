{"************": {"errors": [], "object.ip": "************", "objects": [{"object.name": "Dhcp"}], "password": "Mind@123", "port": 5985, "result": {"system.service": [{"status": "Up", "system.service": "Dhcp", "system.service.description": "Registers and updates IP addresses and DNS records for this computer. If this service is stopped, this computer will not receive dynamic IP addresses and DNS updates. If this service is disabled, any services that explicitly depend on it will fail to start.", "system.service.display.name": "DHCP Client", "system.service.startup.type": "Auto", "system.service.status": "Running"}]}, "timeout": 20, "username": "Administrator"}, "***********": {"errors": [], "object.ip": "***********", "objects": [{"object.name": "Dhcp"}], "password": "Mind@#123", "port": 5985, "result": {"system.service": [{"status": "Up", "system.service": "Dhcp", "system.service.description": "Registers and updates IP addresses and DNS records for this computer. If this service is stopped, this computer will not receive dynamic IP addresses and DNS updates. If this service is disabled, any services that explicitly depend on it will fail to start.", "system.service.display.name": "DHCP Client", "system.service.startup.type": "Auto", "system.service.status": "Running"}]}, "timeout": 20, "username": "Administrator"}}