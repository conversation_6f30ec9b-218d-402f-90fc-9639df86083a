{"configuration": {"config.object": 13, "config.management.status": "yes", "object.category": "Network", "object.name": "172.16.14.6", "config.context": {"port": 22, "ping.check.status": "no", "port.check.status": "no", "snmp.check.retries": 0, "interface.discovery": "yes", "topology.link.layer": ["L2", "L3"], "topology.plugin.discovery": "yes"}, "_type": "1", "id": 22959184103322, "config.work.logs": {"ping": "172.16.14.6_|@@|_succeed", "port": "22_|@@|_succeed", "connection": "succeed_|@@|_succeed", "username": "ospf1_|@@|_succeed", "password": "_|@@|_succeed", "enable prompt": "en_|@@|_succeed", "enable username": "_|@@|_[NA]", "enable password": "_|@@|_succeed", "prompt": "#_|@@|_succeed", "running.config.status": "_|@@|_succeed", "startup.config.status": "_|@@|_succeed"}, "config.credential.status": "succeed", "config.template": 22959184103906, "config.credential.profile": 22959184102669, "config.last.discovery.status": "succeed", "config.last.discovery.time": 1700819632459, "config.last.action.performed": "restore", "config.last.backup.time": 1701775218608, "config.last.backup.status": "fail", "config.last.action.status": "succeed", "config.last.action.time": 1701775220652, "config.last.action.error.message": "", "config.storage.profile": 22959184103929, "config.running.file.current.version": 2, "config.backup.file.baseline.version": 0, "config.running.baseline.file.conflict.status": "Not Applicable"}, "credential.profile": {"credential.profile.name": "************", "credential.profile.protocol": "SSH", "credential.profile.context": {"username": "motadata", "password": "motadata", "cli.enabled": "no"}, "_type": "1", "id": 43339543617663}, "discovery": {"discovery.category": "Server", "discovery.object.type": "Linux", "discovery.name": "************", "discovery.type": "ip.address", "discovery.target": "************", "discovery.exclude.target.type": "ip.address", "discovery.exclude.targets": [], "discovery.credential.profiles": [43339543617663], "discovery.config.management.status": "no", "discovery.groups": [10000000000017], "discovery.user.tags": [], "discovery.context": {"ping.check.status": "yes", "port": 22}, "discovery.status": "Last ran at 2023/12/05 23:21:01", "discovery.total.objects": 1, "discovery.discovered.objects": 1, "discovery.target.name": "************", "_type": "1", "id": 43339543617664, "discovery.failed.objects": 0, "discovery.progress": 100}, "application.mapper": {"application.mapper.name": "SFTP", "application.mapper.protocol": "TCP", "application.mapper.port": 115, "_type": "0", "id": 43339543545407}, "token": {"token_id": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.********************************************************************************************************************************************************************************************************************************.GY8cls9puKLzGgC7Y-TmX-_mWYLghSh2v7fJIp5LxFhI4RCtZd0pi6Yfwo_dAZXzgp6h7v8d2CRXyBrI-BvTqmSj6do-V7QP9byhVye7njnmF3mviuVnxLma_jI5WRwna_UzcH1opAlFM21KZINzGj6BIxsug4wvwosDYZTM9IQvRSecChlFlcMU8LBD0vjdWQq3bzyTywmdSn_cZpx4FTs3sFFiBSywhO57VyOKJ60kX-mIc48i3eufyTn8ugsY95V6Dnc75vdpUGECvO8KWoE299LftJidwAs6vD9d5LbKG6LkXDR5k-jBENytNDJeMj4bIxzJ9T2jcvG5mf3-gxAhLUEuR3N_uJ3asGXZMouJjyFc7k3-TcmIodxLYL-fa7yxdne3d1Ek0zEvJQfhVN2SMtG9TcZgofP-ToAYuahYbO6XUafsEnreoq7D82HVZ9G53ypHg2lgy_UEUCagVjAlHBMK2UhdM2AhoWhiYY2S5Six6cKV3W5ZoONNYO5EyEZnFwVLl4rh5D_KI8GXxhZifcySlzcaGUTKQZu2QKTs-wC1ffpABvLTpFIylw1nKFNjdDd13itwU4omfJSTuag7hRo48rbs7vPEd6A0A1ais-NHUoHrJilxitxZSJx334VIXyy19JGtgUVBqScpc4vMbbyYlXls1t9YnOQ37SY", "user.id": **************, "session-id": "a100633d-b674-4b82-8036-71fc1884f6af", "event.timestamp": 1701798602065, "access.token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzUxMiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XpWdXzBlqbeFjwnAkXwwXfP0GtlkLuOt8PO4T_yma0oen-rA1qQC7JIobB0CIWmziDn66QnAzccefnp2Pmy2wyb_BeCevtb6_58ZMWeeyCLf0nvSqwuZpXX_UmSpknbFeP1O3C-Vr2Z3lpK-8vffzQ68hU_Y1rPCTZ6yrRDFEWZ_RUw4wsiD96WvwdbJn0e6GwU6vCMloMulM3iPxL7Zolm76HROhynwSH79n28rR79bQM7YdrtDi-SK5rXJ2zoDpoyshCKHr440jP-6-IKngKCZ2fO-9DBrpLTITRImSTEBStr_olO-_FZbt6GL-jvff7VKwU_nIbEwAyVqBLZZnniQKYnsKjP8KBJbrppWVoYl1oOx1QrDZ9J15SL9QgeRD-OVct845oitvo6w_Y8GDPP42A8Ok8aDQL3LsUVzaTSgZtPrb5q7fkiTXUz0kYL0ZRBmk31swjvDd7LbGWNHRBmsLCNcSdZ6jTfxZdvLPJbWziTlii1k-5xK2_PNawb7IdcGl_rOQ9EBG_ZfCk4TZJsk9n-ABt4qDuHtimTiWKslLIyUXkD5w-3V_mTWSuCoAPPbkkInIjBqo9amz6vGVBsmvyenwN7ZN82CuC7x5K5j9_tFFKVE_Cgmj3mWzXrQWH8j5TAT-iNp5e8WA9jlstpCv-krEdrqz5N3B1pNQKQ", "remote.address": "0:0:0:0:0:0:0:1", "_type": "1", "id": 43339543617662}, "business.hour": {"business.hour.name": "24*7", "business.hour.context": {"Sunday": [4, 0, 1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23], "Monday": [4, 0, 1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23], "Tuesday": [4, 0, 1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23], "Wednesday": [4, 0, 1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23], "Thursday": [4, 0, 1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23], "Friday": [4, 0, 1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23], "Saturday": [4, 0, 1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]}, "id": **************, "_type": "0"}, "custom.monitoring.field": {"custom.monitoring.field.name": "Owner", "id": 10000000000004, "_type": "0"}, "dashboard": {"id": 10000000000022, "dashboard.name": "SQS Service", "dashboard.category": "Home Screens", "dashboard.access.type": "public", "style": {"font.size": "medium", "h.gap": 8, "v.gap": 8, "row.height": 85}, "dashboard.context": {"dashboard.tabs": [{"id": 10000000000022, "text": "Overview"}], "dashboard.users": [], "dashboard.widgets": [{"x": 0, "y": 0, "h": 2, "w": 2, "id": 10000000001656, "options": {}}, {"x": 2, "y": 0, "h": 2, "w": 2, "id": 10000000001657, "options": {}}, {"x": 4, "y": 0, "h": 2, "w": 2, "id": 10000000001658, "options": {}}, {"x": 6, "y": 0, "h": 2, "w": 2, "id": 10000000001659, "options": {}}, {"x": 8, "y": 0, "h": 2, "w": 2, "id": 10000000001660, "options": {}}, {"x": 10, "y": 0, "h": 2, "w": 2, "id": 10000000001661, "options": {}}, {"x": 0, "y": 4, "h": 4, "w": 12, "id": 10000000001662, "options": {}}, {"x": 0, "y": 6, "h": 3, "w": 6, "id": 10000000001663, "options": {}}, {"x": 6, "y": 6, "h": 3, "w": 6, "id": 10000000001664, "options": {}}, {"x": 0, "y": 9, "h": 3, "w": 4, "id": 10000000001665, "options": {}}, {"x": 4, "y": 9, "h": 3, "w": 4, "id": 10000000001666, "options": {}}, {"x": 8, "y": 9, "h": 3, "w": 4, "id": 10000000001667, "options": {}}, {"x": 0, "y": 12, "h": 3, "w": 4, "id": 10000000001668, "options": {}}, {"x": 4, "y": 12, "h": 3, "w": 4, "id": 10000000001669, "options": {}}, {"x": 8, "y": 12, "h": 3, "w": 4, "id": 10000000001670, "options": {}}], "dashboard.owner": **************}, "_type": "0"}, "data.retention.policy": {"data.retention.policy.context": {"PERFORMANCE_METRIC": {"raw": 180, "aggregated": 360}, "LOG": {"raw": 180, "aggregated": 360}, "FLOW": {"raw": 180, "aggregated": 360}, "TRAP": {"raw": 180, "aggregated": 360}, "NOTIFICATION": {"raw": 180}, "AUDIT": {"raw": 180}, "METRIC_POLICY": {"raw": 180}, "Config": {"version": 10}}, "_type": "0", "id": 43339543617660}, "event.policy": {"policy.name": "Apache Tomcat - Failed Authentication Attempts", "policy.type": "Log", "policy.tags": ["<PERSON><PERSON><PERSON>"], "policy.scheduled": "no", "policy.context": {"entity.type": "event.source.type", "entities": ["Apache Tomcat"], "data.point": "message", "aggregator": "count", "operator": ">=", "value": 1, "trigger.mode": "individual", "evaluation.window": 5, "evaluation.window.unit": "minute", "evaluation.frequency": 5, "evaluation.frequency.unit": "minute", "policy.severity": "CRITICAL", "policy.result.by": ["event.source"], "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "message", "operator": "contain", "value": "Failed login attempt"}]}]}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.timer.seconds": 0, "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": [], "policy.suppress.action": "no", "policy.archived": "no", "policy.creation.time": 1685511087, "policy.state": "yes", "_type": "1", "id": 10000000000016}, "event.source": {"event.source": "************", "source.groups": [10000000000019, 10000000000017], "plugin.id": [600004], "event.type": ["log"], "event.category": [], "_type": "0", "id": 43339543617676}, "flow.setting": {"id": **************, "flow.settings.sflow.direction": "ingress", "flow.settings.sflow.port": 6343, "flow.settings.netflow.port": 2055, "flow.settings.aggregation.interval.minutes": 1, "_type": "0"}, "flow.sampling.rate": {"event.source": "**********", "interface.index": 12, "interface.sampling.rate": 0, "event.id": 49970524075, "_type": "1", "id": 49970524071, "interface.custom.sampling.rate": 1}, "group": {"group.name": "Windows", "group.parent": 10000000000017, "id": 10000000000018, "group.context": {"group.auto.assign": "yes", "object.type": ["Windows"]}, "_type": "0"}, "ldap.server": {"ldap.server.port": 389, "ldap.server.protocol": "PLAIN", "_type": "0", "id": 43339543617410}, "log.parser": {"log.parser.name": "SonicWall", "log.parser.event": "id=firewall sn=0017C5E89AB4 time=\"2017-06-07 12:06:02 UTC\" fw=************** pri=6 c=1024 m=537 msg=\"Connection Closed\" app=49175 appName=\"General HTTP\" n=145105588 src=***************:50995:X0:acplmum-ho-l007.avendus.com dst=**************:80:X1:domain.not.configured srcMac=bc:16:f5:0d:99:d2 dstMac=00:26:88:85:ab:f4 proto=tcp/http sent=2341 rcvd=3703 spkt=9 rpkt=9 cdur=383", "log.parser.condition.keywords": [], "log.parser.condition": "all", "log.parser.fields": [{"log.parser.field.value": 1681367985, "log.parser.field.name": "event.received.time", "log.parser.field.type": "none"}, {"log.parser.field.value": "0017C5E89AB4", "log.parser.field.name": "sonicwall.traffic.serial.number", "log.parser.field.type": "none"}, {"log.parser.field.value": 1496837162, "log.parser.field.name": "timestamp", "log.parser.field.type": "timestamp"}, {"log.parser.field.value": "**************", "log.parser.field.name": "sonicwall.traffic.firewall.wan.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "6", "log.parser.field.name": "sonicwall.traffic.priority", "log.parser.field.type": "none"}, {"log.parser.field.value": "Traffic", "log.parser.field.name": "sonicwall.traffic.category", "log.parser.field.type": "none"}, {"log.parser.field.value": "Connection Closed", "log.parser.field.name": "sonicwall.traffic.message.info", "log.parser.field.type": "none"}, {"log.parser.field.value": "49175", "log.parser.field.name": "sonicwall.traffic.application.id", "log.parser.field.type": "none"}, {"log.parser.field.value": "General HTTP", "log.parser.field.name": "sonicwall.traffic.application.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "***************", "log.parser.field.name": "sonicwall.traffic.source.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "**************", "log.parser.field.name": "sonicwall.traffic.destination.ip", "log.parser.field.type": "none"}, {"log.parser.field.value": "bc:16:f5:0d:99:d2", "log.parser.field.name": "sonicwall.traffic.source.mac", "log.parser.field.type": "none"}, {"log.parser.field.value": "00:26:88:85:ab:f4", "log.parser.field.name": "sonicwall.traffic.destination.mac", "log.parser.field.type": "none"}, {"log.parser.field.value": "tcp/http", "log.parser.field.name": "sonicwall.traffic.protocol", "log.parser.field.type": "none"}, {"log.parser.field.value": "2341", "log.parser.field.name": "sonicwall.traffic.sent.bytes", "log.parser.field.type": "none"}, {"log.parser.field.value": "3703", "log.parser.field.name": "sonicwall.traffic.received.bytes", "log.parser.field.type": "none"}, {"log.parser.field.value": "9", "log.parser.field.name": "sonicwall.traffic.sent.packets", "log.parser.field.type": "none"}, {"log.parser.field.value": "9", "log.parser.field.name": "sonicwall.traffic.received.packets", "log.parser.field.type": "none"}, {"log.parser.field.value": "383", "log.parser.field.name": "sonicwall.traffic.connection.duration", "log.parser.field.type": "none"}, {"log.parser.field.value": "SonicWall Traffic", "log.parser.field.name": "event.category", "log.parser.field.type": "none"}, {"log.parser.field.value": 6044, "log.parser.field.name": "sonicwall.traffic.bytes", "log.parser.field.type": "none"}, {"log.parser.field.value": 18, "log.parser.field.name": "sonicwall.traffic.packets", "log.parser.field.type": "none"}, {"log.parser.field.value": "kernel messages", "log.parser.field.name": "sonicwall.traffic.facility", "log.parser.field.type": "none"}, {"log.parser.field.value": "Informational", "log.parser.field.name": "sonicwall.traffic.severity", "log.parser.field.type": "none"}, {"log.parser.field.value": "50995", "log.parser.field.name": "sonicwall.traffic.source.port", "log.parser.field.type": "none"}, {"log.parser.field.value": "X0", "log.parser.field.name": "sonicwall.traffic.source.interface", "log.parser.field.type": "none"}, {"log.parser.field.value": "acplmum-ho-l007.avendus.com", "log.parser.field.name": "sonicwall.traffic.source.resolved.name", "log.parser.field.type": "none"}, {"log.parser.field.value": "80", "log.parser.field.name": "sonicwall.traffic.destination.port", "log.parser.field.type": "none"}, {"log.parser.field.value": "X1", "log.parser.field.name": "sonicwall.traffic.destination.interface", "log.parser.field.type": "none"}, {"log.parser.field.value": "domain.not.configured", "log.parser.field.name": "sonicwall.traffic.destination.resolved.name", "log.parser.field.type": "none"}], "log.parser.type": "custom", "log.parser.source.vendor": "SonicWall", "log.parser.plugin": 10000000000011, "log.parser.source.type": "Firewall", "log.parser.upload": "no", "log.parser.date.time.formatter.type": "numeric", "log.parser.date.time.format": "seconds", "plugin.id": 600021, "log.parser.entities": [], "id": 10000000000020, "_type": "0"}, "log.parser.plugin": {"id": 10000000000006, "log.parser.plugin.name": "MySQLLogParserPlugin", "log.parser.plugin.context": {"log.parser.plugin.script": "MySQLLogParserPlugin.java"}, "_type": "0"}, "mac.scanner": {"mac.scanner.interface": "ens160", "mac.scanner.address": "00:50:56:9E:E7:C8", "mac.scanner.device.ip.address": "************", "mac.scanner.interface.ip.address": "************", "_type": "1", "id": 43339543617677}, "mail.server": {"mail.server.port": 25, "mail.server.protocol": "None", "_type": "0", "id": 43339543617411}, "metric": {"metric.plugin": "objectstatus", "metric.name": "Object Status", "metric.polling.time": 50000, "metric.context": {"plugin.id": 241, "metric.polling.min.time": 60, "timeout": 60}, "metric.state": "DISABLE", "metric.object": 43339543617665, "metric.type": "Linux", "metric.category": "Server", "metric.credential.profile": 43339543617663, "metric.credential.profile.protocol": "SSH", "metric.discovery.method": "REMOTE", "_type": "1", "id": 43339543617674}, "metric.plugin": {"metric.plugin.name": "Juniper Switch", "metric.plugin.vendor": "Juniper Networks", "metric.plugin.type": "Switch", "metric.plugin.entity.type": "Monitor", "metric.plugin.entities": [], "metric.plugin.protocol": "custom", "metric.plugin.context": {"script": "juniper_switch_metric.py", "script.language": "python", "script.protocol": "SSH", "port": 22}, "id": 10000000000004, "_type": "0"}, "metric.policy": {"policy.type": "<PERSON><PERSON>", "policy.name": "Motadata Collector CPU Utilization", "policy.context": {"entity.type": "Monitor", "entities": [], "metric": "motadata.collector.agent.system.cpu.percent", "instance.type": "", "data.type": ["numeric"], "policy.trigger.time": 300, "policy.trigger.occurrences": 1, "policy.auto.clear.timer.seconds": 0, "filters": {"data.filter": {}}, "policy.severity": {"CRITICAL": {"policy.condition": ">=", "policy.threshold": "95"}, "MAJOR": {"policy.condition": ">=", "policy.threshold": "85"}, "WARNING": {"policy.condition": ">=", "policy.threshold": "75"}, "CLEAR": {"policy.condition": "<", "policy.threshold": "75"}}}, "policy.email.notification.recipients": [], "policy.renotify": "yes", "policy.monitor.polling.failed.notification.status": "no", "policy.renotification.timer.seconds": 0, "policy.actions": {}, "policy.creation.time": 1653989795, "policy.state": "yes", "_type": "1", "id": 43339543617518}, "object": {"object.context": {"ping.check.status": "yes", "port": 22, "ss.bin.path": "/bin/ss"}, "object.host": "ubuntu", "object.ip": "************", "object.name": "ubuntu", "object.type": "Linux", "object.groups": [10000000000019, 10000000000017], "object.user.tags": [], "object.discovery.method": "REMOTE", "object.target": "************", "object.state": "ENABLE", "object.category": "Server", "_type": "1", "id": 43339543617665, "ui.event.uuid": "2be06c12-09fb-4414-b444-580d832935b4", "remote.address": "0:0:0:0:0:0:0:1", "user.name": "admin", "object.creation.time": "2023/12/05 23:21:08", "object.creation.time.seconds": 1701798668, "object.business.hour.profile": **************, "object.id": 1}, "password.policy": {"password.policy.password.expiry": "yes", "password.policy.password.expiry.days": 15, "password.policy.uppercase.check": "yes", "password.policy.lowercase.check": "yes", "password.policy.number.check": "yes", "password.policy.special.character.check": "yes", "password.policy.password.minimum.length": 8, "_type": "0", "id": 43339543545392}, "protocol.mapper": {"protocol.mapper.number": 8, "protocol.mapper.name": "EGP", "_type": "0", "id": 43339543617528}, "rebranding": {"rebranding.logo": "bc508eca-78f7-4bc7-82ab-8b7556c1eb20", "id": **************, "_type": "0"}, "remote.event.processor": {"remote.event.processor.host": ",motadata", "remote.event.processor.uuid": "6edf359c-8470-4ed1-ba20-6f502298a322", "remote.event.processor.ip": "***************", "remote.event.processor.version": 1.0, "event.type": "registration", "remote.event.processor.type": "DEFAULT", "event.topic": "remote.event.processor ", "_type": "0", "id": 43339543617661}, "report": {"report.name": "Top 25 Monitors by CPU Utilization - Last Day", "report.description": "Top 25 Monitors by CPU Utilization - Last Day", "report.widgets": [{"id": -1, "visualization.timeline": {"relative.timeline": "yesterday", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"visualization.result.by": ["monitor"], "filters": {"result.filter": {}, "data.filter": {}}, "type": "metric", "data.points": [{"aggregator": "avg", "data.point": "citrix.xen.cpu.percent"}, {"aggregator": "min", "data.point": "citrix.xen.cpu.percent"}, {"aggregator": "max", "data.point": "citrix.xen.cpu.percent"}]}], "visualization.properties": {"grid": {"sorting": {"limit": 25, "column": "citrix.xen.cpu.percent.avg", "order": "desc"}, "header": "yes", "style": {"header.font.size": "small"}, "searchable": "yes"}}, "visualization.result.by": ["monitor"], "granularity": {"unit": "m", "value": 5}}, {"id": -1, "visualization.timeline": {"relative.timeline": "yesterday", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"visualization.result.by": ["monitor"], "filters": {"result.filter": {}, "data.filter": {}}, "type": "metric", "data.points": [{"aggregator": "avg", "data.point": "esxi.cpu.percent"}, {"aggregator": "min", "data.point": "esxi.cpu.percent"}, {"aggregator": "max", "data.point": "esxi.cpu.percent"}]}], "visualization.properties": {"grid": {"sorting": {"limit": 25, "column": "esxi.cpu.percent.avg", "order": "desc"}}}, "visualization.result.by": ["monitor"], "granularity": {"unit": "m", "value": 5}}, {"id": -1, "visualization.timeline": {"relative.timeline": "yesterday", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"visualization.result.by": ["monitor"], "filters": {"result.filter": {}, "data.filter": {}}, "type": "metric", "data.points": [{"aggregator": "avg", "data.point": "system.cpu.percent"}, {"aggregator": "min", "data.point": "system.cpu.percent"}, {"aggregator": "max", "data.point": "system.cpu.percent"}]}], "visualization.properties": {"grid": {"sorting": {"limit": 25, "column": "system.cpu.percent.avg", "order": "desc"}}}, "visualization.result.by": ["monitor"], "granularity": {"unit": "m", "value": 5}}, {"id": -1, "visualization.timeline": {"relative.timeline": "yesterday", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "cisco.wireless.controller.cpu.percent", "aggregator": "avg", "entities": []}, {"data.point": "cisco.wireless.controller.cpu.percent", "aggregator": "min", "entities": []}, {"data.point": "cisco.wireless.controller.cpu.percent", "aggregator": "max", "entities": []}]}], "visualization.properties": {"grid": {"sorting": {"limit": "25", "order": "desc", "column": "cisco.wireless.controller.cpu.percent.avg"}}}, "visualization.result.by": ["monitor"], "granularity": {"value": 5, "unit": "m"}}, {"id": -1, "visualization.timeline": {"relative.timeline": "yesterday", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "aruba.wireless.controller.cpu.percent", "aggregator": "avg", "entities": []}, {"data.point": "aruba.wireless.controller.cpu.percent", "aggregator": "max", "entities": []}, {"data.point": "aruba.wireless.controller.cpu.percent", "aggregator": "min", "entities": []}]}], "visualization.properties": {"grid": {"sorting": {"limit": "25", "order": "desc", "column": "aruba.wireless.controller.cpu.percent.avg"}}}, "visualization.result.by": ["monitor"], "granularity": {"value": 5, "unit": "m"}}, {"id": -1, "visualization.timeline": {"relative.timeline": "yesterday", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "hyperv.cpu.percent", "aggregator": "avg"}, {"data.point": "hyperv.cpu.percent", "aggregator": "min"}, {"data.point": "hyperv.cpu.percent", "aggregator": "max"}]}], "visualization.properties": {"grid": {"sorting": {"limit": 25, "order": "desc", "column": "hyperv.cpu.percent.avg"}}}, "visualization.result.by": ["monitor"], "granularity": {"value": 5, "unit": "m"}}, {"id": -1, "visualization.timeline": {"relative.timeline": "yesterday", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "azure.vm.cpu.percent", "aggregator": "avg"}, {"data.point": "azure.vm.cpu.percent", "aggregator": "min"}, {"data.point": "azure.vm.cpu.percent", "aggregator": "max"}]}], "visualization.properties": {"grid": {"sorting": {"limit": "25", "order": "desc", "column": "azure.vm.cpu.percent.avg"}}}, "visualization.result.by": ["monitor"], "granularity": {"value": 5, "unit": "m"}}, {"id": -1, "visualization.timeline": {"relative.timeline": "yesterday", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "aws.ec2.cpu.percent", "aggregator": "avg"}, {"data.point": "aws.ec2.cpu.percent", "aggregator": "min"}, {"data.point": "aws.ec2.cpu.percent", "aggregator": "max"}]}], "visualization.properties": {"grid": {"sorting": {"limit": "25", "order": "desc", "column": "aws.ec2.cpu.percent.avg"}}}, "visualization.result.by": ["monitor"], "granularity": {"value": 5, "unit": "m"}}], "report.scheduler": "no", "report.context": {"visualization.type": "Multi-TopN", "alias": {"system.cpu.percent^min": "cpu.percent.min", "system.cpu.percent^avg": "cpu.percent.avg", "system.cpu.percent^max": "cpu.percent.max", "esxi.cpu.percent^min": "cpu.percent.min", "esxi.cpu.percent^avg": "cpu.percent.avg", "esxi.cpu.percent^max": "cpu.percent.max", "citrix.xen.cpu.percent^min": "cpu.percent.min", "citrix.xen.cpu.percent^avg": "cpu.percent.avg", "citrix.xen.cpu.percent^max": "cpu.percent.max", "cisco.wireless.controller.cpu.percent^min": "cpu.percent.min", "cisco.wireless.controller.cpu.percent^avg": "cpu.percent.avg", "cisco.wireless.controller.cpu.percent^max": "cpu.percent.max", "aruba.wireless.controller.cpu.percent^min": "cpu.percent.min", "aruba.wireless.controller.cpu.percent^avg": "cpu.percent.avg", "aruba.wireless.controller.cpu.percent^max": "cpu.percent.max", "hyperv.cpu.percent^min": "cpu.percent.min", "hyperv.cpu.percent^avg": "cpu.percent.avg", "hyperv.cpu.percent^max": "cpu.percent.max", "azure.vm.cpu.percent^min": "cpu.percent.min", "azure.vm.cpu.percent^avg": "cpu.percent.avg", "azure.vm.cpu.percent^max": "cpu.percent.max", "aws.ec2.cpu.percent^min": "cpu.percent.min", "aws.ec2.cpu.percent^avg": "cpu.percent.avg", "aws.ec2.cpu.percent^max": "cpu.percent.max"}, "template": {"id": -1, "visualization.name": "Top 25 Monitors by CPU Utilization - Last Day", "visualization.timeline": {"relative.timeline": "yesterday", "visualization.time.range.inclusive": "no"}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.data.sources": [{"visualization.result.by": ["monitor"], "filters": {"result.filter": {}, "data.filter": {}}, "type": "metric", "data.points": [{"aggregator": "avg", "data.point": "cpu.percent"}, {"aggregator": "min", "data.point": "cpu.percent"}, {"aggregator": "max", "data.point": "cpu.percent"}]}], "visualization.properties": {"grid": {"column.selection": "no", "columns": [{"resizable": "yes", "disable": "no", "orderable": "yes", "selectable": "yes", "name": "monitor", "show": "yes", "sortable": "yes", "position": 1, "title": "Monitor Name"}, {"resizable": "yes", "disable": "no", "orderable": "yes", "selectable": "yes", "name": "object.type", "show": "yes", "sortable": "yes", "position": 2, "title": "Monitor Type"}, {"resizable": "yes", "disable": "no", "orderable": "yes", "selectable": "yes", "name": "cpu.percent.min", "show": "yes", "sortable": "yes", "position": 3, "title": "Min CPU %"}, {"resizable": "yes", "disable": "no", "orderable": "yes", "selectable": "yes", "name": "cpu.percent.max", "show": "yes", "sortable": "yes", "position": 4, "title": "Max CPU %"}, {"resizable": "yes", "disable": "no", "orderable": "yes", "selectable": "yes", "name": "cpu.percent.avg", "show": "yes", "sortable": "yes", "position": 5, "title": "Avg CPU %"}], "sorting": {"limit": 25, "column": "cpu.percent.avg", "order": "desc"}, "header": "yes", "style": {"header.font.size": "small"}, "searchable": "yes"}}, "visualization.result.by": ["monitor"], "granularity": {"unit": "m", "value": 5}}}, "report.type": "metric", "report.layout": "auto", "report.category": "Performance", "report.custom": "yes", "report.user": **************, "id": 10000000010130, "_type": "0"}, "runbook.plugin": {"runbook.plugin.name": "TFTP", "runbook.plugin.type": "Custom Script", "runbook.plugin.entity.type": "event.source", "runbook.plugin.category": "Storage Profile", "runbook.plugin.entities": [], "runbook.plugin.variables": {}, "runbook.plugin.context": {"script": "this is a script", "script.language": "go", "timeout": 60}, "id": 10000000000022, "_type": "0"}, "scheduler": {"scheduler.state": "yes", "scheduler.job.type": "Rediscover", "scheduler.start.date": "01-10-2021", "scheduler.times": ["00:00"], "scheduler.timeline": "Daily", "scheduler.context": {"rediscover.job": "Network Interface", "auto.provision.status": "yes"}, "_type": "0", "id": 43339543617485}, "sms.gateway": {"id": **************, "_type": "0"}, "snmp.device.catalog": {"snmp.device.catalog.model": "FortiADC DEV", "snmp.device.catalog.vendor": "Fortinet", "snmp.device.catalog.oid": ".*******.4.1.12356.112.100.10", "snmp.device.catalog.type": "Firewall", "_type": "0", "id": 43339543616880}, "snmp.trap.listener": {"snmp.trap.listener.v1.v2.port": 1620, "snmp.trap.listener.v1.v2.status": "yes", "snmp.community": "public", "snmp.trap.listener.v3.status": "no", "snmp.trap.listener.v3.port": 1630, "_type": "0", "id": 43339543617475}, "snmp.trap.profile": {"snmp.trap.profile.name": "hwIOBoardVCC3V3OverVoltageDeassert", "snmp.trap.profile.oid": ".*******.4.1.2011.2.235.1.1.500.11.36.36", "snmp.trap.profile.translator": "I/O board VCC 3.3V overvoltage major alarm. (Cleared)", "snmp.trap.profile.drop.status": "no", "snmp.trap.profile.auto.clear.status": "no", "snmp.trap.profile.severity": "Unknown", "_type": "0", "id": 43339543550323}, "system.file": {"system.file": "/var/log", "system.file.type": "Directory", "system.file.os": "Linux", "_type": "0", "id": 43339543617423}, "system.process": {"system.process": "db2dasrrm.exe", "system.process.app.type": "IBM Db2", "system.process.os": "Windows", "_type": "0", "id": 43339543617422}, "system.service": {"system.service": "Amsp", "system.service.os": "Windows", "_type": "0", "id": 43339543617484}, "template": {"id": 10000000000154, "template.name": "License Details Palo Alto Firewall", "template.tabs": [], "style": {"font.size": "medium", "h.gap": 8, "v.gap": 8, "row.height": 85}, "template.widgets": [{"x": 0, "y": 0, "h": 2, "w": 12, "id": 10000000001384, "options": {"show.title": "no"}}], "_type": "0"}, "topology.plugin": {"topology.plugin.name": "SSH Cisco BGP Topology", "topology.plugin.vendor": "Cisco Systems", "topology.plugin.type": "Custom", "topology.plugin.entity.type": "Monitor", "topology.plugin.entities": [], "topology.plugin.protocol": "BGP", "topology.plugin.context": {"timeout": 60, "script": "cisco_bgp.py", "script.language": "python"}, "id": 10000000000004, "_type": "0"}, "user": {"user.name": "admin", "user.type": "System", "user.first.name": "motadata", "user.last.name": "admin", "user.password": "flsTVAS7R94buE4Av1x36g==", "user.status": "yes", "user.role": **************, "user.groups": [**************], "user.preferences": {"user.home.screen": 10000000001026, "ui.theme": "black", "user.preference.time.zone": "Asia/Calcutta", "user.favourite.dashboards": [], "user.favourite.reports": [], "user.preference.date.time.format": "ddd, <PERSON><PERSON>, yyyy hh:mm:ss A", "ui.columns": {}, "ui.page.size": 50, "setup.completed": "no", "setup.guide.completed": [], "setup.sections.completed": [], "setup.steps.completed": [], "ui.inventory.view": "widget", "ui.topology.preferences": {}}, "id": **************, "user.password.last.updated.time": 1701798575789, "_type": "0"}, "user.role": {"user.role.name": "admin", "user.role.context": ["user-settings:read", "user-settings:read-write", "user-settings:delete", "system-settings:read", "system-settings:read-write", "system-settings:delete", "discovery-settings:read", "discovery-settings:read-write", "discovery-settings:delete", "monitor-settings:read", "monitor-settings:read-write", "monitor-settings:delete", "group-settings:read", "group-settings:read-write", "group-settings:delete", "agent-settings:read", "agent-settings:read-write", "agent-settings:delete", "snmp-trap-settings:read", "snmp-trap-settings:read-write", "snmp-trap-settings:delete", "plugin-library-settings:read", "plugin-library-settings:read-write", "plugin-library-settings:delete", "audit-settings:read", "my-account-settings:read", "my-account-settings:read-write", "notification-settings:read", "dashboards:read-write", "dashboards:delete", "dashboards:read", "inventory:read-write", "inventory:delete", "inventory:read", "templates:read-write", "templates:delete", "templates:read", "widgets:read-write", "widgets:delete", "widgets:read", "policy-settings:read-write", "policy-settings:delete", "policy-settings:read", "flow-settings:read", "flow-settings:read-write", "flow-settings:delete", "log-settings:read", "log-settings:read-write", "log-settings:delete", "aiops-settings:read", "aiops-settings:read-write", "aiops-settings:delete", "metric-explorer:read", "log-explorer:read", "flow-explorer:read", "alert-explorer:read", "trap-explorer:read", "topology:read", "reports:read", "reports:read-write", "reports:delete", "config:read", "config:read-write", "config:delete", "integrations:read", "integrations:read-write"], "id": **************, "_type": "0"}, "widget": {"_type": "0", "id": **************, "visualization.name": "Memory Utilization", "visualization.description": "Load Balancer Memory Utilization", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.memory.percent", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "small", "color.data.point": "system.memory.percent", "icon": {"name": "memory", "placement": "prefix"}}, "header": {"title": "Memory Utilization", "style": {"font.size": "medium"}, "data.points": []}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "system.memory.percent.last"}]}}}}}