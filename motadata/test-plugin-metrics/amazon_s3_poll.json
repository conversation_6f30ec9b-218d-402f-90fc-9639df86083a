{"127.0.0.1": {"_type": "1", "cloud.access.id": "********************", "cloud.secret.key": "pu6mxO0hJxJHQ3/ee5d4rNmErG/sKvlfVoYh+uvv", "cloud.type": "AWS Cloud", "credential.profile.name": "AWS-Cloud-Test1655203749779", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 41, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "AWS-Cloud-Test1655203749780", "discovery.object.type": "AWS Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:19:50.876 pm 14/06/2022", "discovery.total.objects": 41, "errors": [], "event.id": 176************, "event.timestamp": **********, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Amazon S3", "metric.object": ***************, "metric.plugin": "amazons3", "metric.polling.min.time": 1800, "metric.polling.time": 3600, "metric.state": "ENABLE", "metric.type": "Amazon S3", "object.account.id": "************", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:27.857 pm 14/06/2022", "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 4, "object.name": "docs.motadata.com(ap-south-1)", "object.region": "ap-northeast-3", "object.state": "ENABLE", "object.target": "motadata-common(ap-northeast-3)", "object.type": "Amazon S3", "object.vendor": "AWS Cloud", "plugin.engine": "go", "plugin.id": 11, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"aws.s3.bucket": [{"aws.location": "ap-south-1", "aws.s3.bucket": "motadata-common", "aws.s3.bucket.bytes": 0, "aws.s3.bucket.creation.time": " 274 days 4 hours 17 minutes 30 seconds", "aws.s3.bucket.creation.time.seconds": ********, "aws.s3.bucket.objects": 0, "aws.s3.bucket.region": "ap-northeast-3", "aws.s3.bucket.virtual.folders": 0}]}, "status": "succeed", "timeout": 60}}