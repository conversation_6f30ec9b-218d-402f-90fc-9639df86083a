{"10.10.10.131": {"result": {"cisco.meraki.stp": [{"cisco.meraki.stp": "1", "cisco.meraki.stp.enabled": true, "cisco.meraki.stp.received.bytes.per.sec": 0, "cisco.meraki.stp.sent.bytes.per.sec": 0, "cisco.meraki.stp.state": "Disconnected", "cisco.meraki.stp.total.used.bytes.per.sec": 0, "cisco.meraki.stp.uplinked": false}, {"cisco.meraki.stp": "2", "cisco.meraki.stp.enabled": true, "cisco.meraki.stp.received.bytes.per.sec": 0, "cisco.meraki.stp.sent.bytes.per.sec": 0, "cisco.meraki.stp.state": "Disconnected", "cisco.meraki.stp.total.used.bytes.per.sec": 0, "cisco.meraki.stp.uplinked": false}, {"cisco.meraki.stp": "3", "cisco.meraki.stp.enabled": true, "cisco.meraki.stp.received.bytes.per.sec": 0, "cisco.meraki.stp.sent.bytes.per.sec": 0, "cisco.meraki.stp.state": "Disconnected", "cisco.meraki.stp.total.used.bytes.per.sec": 0, "cisco.meraki.stp.uplinked": false}, {"cisco.meraki.stp": "4", "cisco.meraki.stp.enabled": true, "cisco.meraki.stp.received.bytes.per.sec": 0, "cisco.meraki.stp.sent.bytes.per.sec": 0, "cisco.meraki.stp.state": "Disconnected", "cisco.meraki.stp.total.used.bytes.per.sec": 0, "cisco.meraki.stp.uplinked": false}, {"cisco.meraki.stp": "5", "cisco.meraki.stp.enabled": true, "cisco.meraki.stp.received.bytes.per.sec": 0, "cisco.meraki.stp.sent.bytes.per.sec": 0, "cisco.meraki.stp.state": "Disconnected", "cisco.meraki.stp.total.used.bytes.per.sec": 0, "cisco.meraki.stp.uplinked": false}, {"cisco.meraki.stp": "6", "cisco.meraki.stp.enabled": true, "cisco.meraki.stp.received.bytes.per.sec": 0, "cisco.meraki.stp.sent.bytes.per.sec": 0, "cisco.meraki.stp.state": "Disconnected", "cisco.meraki.stp.total.used.bytes.per.sec": 0, "cisco.meraki.stp.uplinked": false}, {"cisco.meraki.stp": "7", "cisco.meraki.stp.enabled": true, "cisco.meraki.stp.received.bytes.per.sec": 0, "cisco.meraki.stp.sent.bytes.per.sec": 0, "cisco.meraki.stp.state": "Disconnected", "cisco.meraki.stp.total.used.bytes.per.sec": 0, "cisco.meraki.stp.uplinked": false}, {"cisco.meraki.stp": "8", "cisco.meraki.stp.enabled": true, "cisco.meraki.stp.received.bytes.per.sec": 0, "cisco.meraki.stp.sent.bytes.per.sec": 0, "cisco.meraki.stp.state": "Disconnected", "cisco.meraki.stp.total.used.bytes.per.sec": 0, "cisco.meraki.stp.uplinked": true}, {"cisco.meraki.stp": "9", "cisco.meraki.stp.enabled": true, "cisco.meraki.stp.received.bytes.per.sec": 0, "cisco.meraki.stp.sent.bytes.per.sec": 0, "cisco.meraki.stp.state": "Disconnected", "cisco.meraki.stp.total.used.bytes.per.sec": 0, "cisco.meraki.stp.uplinked": false}, {"cisco.meraki.stp": "10", "cisco.meraki.stp.enabled": true, "cisco.meraki.stp.received.bytes.per.sec": 0, "cisco.meraki.stp.sent.bytes.per.sec": 0, "cisco.meraki.stp.state": "Disconnected", "cisco.meraki.stp.total.used.bytes.per.sec": 0, "cisco.meraki.stp.uplinked": false}]}, "errors": []}}