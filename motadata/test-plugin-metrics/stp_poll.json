{"************": {"errors": [], "object.ip": "************", "object.vendor": "Cisco Systems", "port": 161, "rediscover.job": "Network Metric", "result": {"stp": [{"stp": "40", "stp.blocking.ports": 0, "stp.bridge.forward.delay.sec": 1500, "stp.bridge.hello.time.sec": 200, "stp.bridge.mac.address": "4C:E1:76:40:B4:80", "stp.bridge.max.age.sec": 2000, "stp.bridge.priority": 32769, "stp.bridge.transmit.hold.count.sec": 100, "stp.broken.ports": 0, "stp.disabled.ports": 0, "stp.forwarding.ports": 1, "stp.last.topology.change.time": " 151 days 19 hours 22 minutes 5 seconds", "stp.learning.ports": 0, "stp.listening.ports": 0, "stp.port": "43", "stp.protocol": "ieee8021d", "stp.root.cost": 4, "stp.root.forward.delay.sec": 1500, "stp.root.hello.time.sec": 200, "stp.root.mac.address": "4C:E1:76:40:B4:80", "stp.root.max.age.sec": 2000, "stp.root.port": 43, "stp.topology.change": 0}, {"stp": "80", "stp.blocking.ports": 0, "stp.bridge.forward.delay.sec": 1500, "stp.bridge.hello.time.sec": 200, "stp.bridge.mac.address": "4C:E1:76:40:B4:80", "stp.bridge.max.age.sec": 2000, "stp.bridge.priority": 32769, "stp.bridge.transmit.hold.count.sec": 100, "stp.broken.ports": 0, "stp.disabled.ports": 0, "stp.forwarding.ports": 1, "stp.last.topology.change.time": " 151 days 19 hours 22 minutes 5 seconds", "stp.learning.ports": 0, "stp.listening.ports": 0, "stp.port": "43", "stp.protocol": "ieee8021d", "stp.root.cost": 4, "stp.root.forward.delay.sec": 1500, "stp.root.hello.time.sec": 200, "stp.root.mac.address": "4C:E1:76:40:B4:80", "stp.root.max.age.sec": 2000, "stp.root.port": 43, "stp.topology.change": 0}, {"stp": "100", "stp.blocking.ports": 0, "stp.bridge.forward.delay.sec": 1500, "stp.bridge.hello.time.sec": 200, "stp.bridge.mac.address": "4C:E1:76:40:B4:80", "stp.bridge.max.age.sec": 2000, "stp.bridge.priority": 32769, "stp.bridge.transmit.hold.count.sec": 100, "stp.broken.ports": 0, "stp.disabled.ports": 0, "stp.forwarding.ports": 1, "stp.last.topology.change.time": " 151 days 19 hours 22 minutes 6 seconds", "stp.learning.ports": 0, "stp.listening.ports": 0, "stp.port": "43", "stp.protocol": "ieee8021d", "stp.root.cost": 4, "stp.root.forward.delay.sec": 1500, "stp.root.hello.time.sec": 200, "stp.root.mac.address": "4C:E1:76:40:B4:80", "stp.root.max.age.sec": 2000, "stp.root.port": 43, "stp.topology.change": 0}, {"stp": "200", "stp.blocking.ports": 0, "stp.bridge.forward.delay.sec": 1500, "stp.bridge.hello.time.sec": 200, "stp.bridge.mac.address": "4C:E1:76:40:B4:80", "stp.bridge.max.age.sec": 2000, "stp.bridge.priority": 32769, "stp.bridge.transmit.hold.count.sec": 100, "stp.broken.ports": 0, "stp.disabled.ports": 0, "stp.forwarding.ports": 1, "stp.last.topology.change.time": " 151 days 19 hours 22 minutes 6 seconds", "stp.learning.ports": 0, "stp.listening.ports": 0, "stp.port": "43", "stp.protocol": "ieee8021d", "stp.root.cost": 4, "stp.root.forward.delay.sec": 1500, "stp.root.hello.time.sec": 200, "stp.root.mac.address": "4C:E1:76:40:B4:80", "stp.root.max.age.sec": 2000, "stp.root.port": 43, "stp.topology.change": 0}, {"stp": "1", "stp.blocking.ports": 0, "stp.bridge.forward.delay.sec": 1500, "stp.bridge.hello.time.sec": 200, "stp.bridge.mac.address": "4C:E1:76:40:B4:80", "stp.bridge.max.age.sec": 2000, "stp.bridge.priority": 32769, "stp.bridge.transmit.hold.count.sec": 100, "stp.broken.ports": 0, "stp.disabled.ports": 0, "stp.forwarding.ports": 1, "stp.last.topology.change.time": " 151 days 19 hours 22 minutes 7 seconds", "stp.learning.ports": 0, "stp.listening.ports": 0, "stp.port": "43", "stp.protocol": "ieee8021d", "stp.root.cost": 4, "stp.root.forward.delay.sec": 1500, "stp.root.hello.time.sec": 200, "stp.root.mac.address": "4C:E1:76:40:B4:80", "stp.root.max.age.sec": 2000, "stp.root.port": 43, "stp.topology.change": 0}, {"stp": "20", "stp.blocking.ports": 0, "stp.bridge.forward.delay.sec": 1500, "stp.bridge.hello.time.sec": 200, "stp.bridge.mac.address": "4C:E1:76:40:B4:80", "stp.bridge.max.age.sec": 2000, "stp.bridge.priority": 32769, "stp.bridge.transmit.hold.count.sec": 100, "stp.broken.ports": 0, "stp.disabled.ports": 0, "stp.forwarding.ports": 1, "stp.last.topology.change.time": " 151 days 19 hours 22 minutes 7 seconds", "stp.learning.ports": 0, "stp.listening.ports": 0, "stp.port": "43", "stp.protocol": "ieee8021d", "stp.root.cost": 4, "stp.root.forward.delay.sec": 1500, "stp.root.hello.time.sec": 200, "stp.root.mac.address": "4C:E1:76:40:B4:80", "stp.root.max.age.sec": 2000, "stp.root.port": 43, "stp.topology.change": 0}, {"stp": "30", "stp.blocking.ports": 0, "stp.bridge.forward.delay.sec": 1500, "stp.bridge.hello.time.sec": 200, "stp.bridge.mac.address": "4C:E1:76:40:B4:80", "stp.bridge.max.age.sec": 2000, "stp.bridge.priority": 32769, "stp.bridge.transmit.hold.count.sec": 100, "stp.broken.ports": 0, "stp.disabled.ports": 0, "stp.forwarding.ports": 1, "stp.last.topology.change.time": " 151 days 19 hours 22 minutes 8 seconds", "stp.learning.ports": 0, "stp.listening.ports": 0, "stp.port": "43", "stp.protocol": "ieee8021d", "stp.root.cost": 4, "stp.root.forward.delay.sec": 1500, "stp.root.hello.time.sec": 200, "stp.root.mac.address": "4C:E1:76:40:B4:80", "stp.root.max.age.sec": 2000, "stp.root.port": 43, "stp.topology.change": 0}, {"stp": "300", "stp.blocking.ports": 0, "stp.bridge.forward.delay.sec": 1500, "stp.bridge.hello.time.sec": 200, "stp.bridge.mac.address": "4C:E1:76:40:B4:80", "stp.bridge.max.age.sec": 2000, "stp.bridge.priority": 32769, "stp.bridge.transmit.hold.count.sec": 100, "stp.broken.ports": 0, "stp.disabled.ports": 0, "stp.forwarding.ports": 1, "stp.last.topology.change.time": " 151 days 19 hours 22 minutes 8 seconds", "stp.learning.ports": 0, "stp.listening.ports": 0, "stp.port": "43", "stp.protocol": "ieee8021d", "stp.root.cost": 4, "stp.root.forward.delay.sec": 1500, "stp.root.hello.time.sec": 200, "stp.root.mac.address": "4C:E1:76:40:B4:80", "stp.root.max.age.sec": 2000, "stp.root.port": 43, "stp.topology.change": 0}], "stp.vlans": 8}, "snmp.community": "public", "snmp.version": "V2c", "timeout": 1, "status": "succeed"}, "**********": {"errors": [], "object.ip": "**********", "object.vendor": "Cisco Systems", "port": 161, "result": {"stp": [{"stp": "20", "stp.blocking.ports": 0, "stp.bridge.forward.delay.sec": 1500, "stp.bridge.hello.time.sec": 200, "stp.bridge.mac.address": "4C:E1:76:40:B4:80", "stp.bridge.max.age.sec": 2000, "stp.bridge.priority": 4097, "stp.bridge.transmit.hold.count.sec": 100, "stp.broken.ports": 0, "stp.disabled.ports": 0, "stp.forwarding.ports": 1, "stp.last.topology.change.time": " 0 day 10 hours 4 minutes 48 seconds", "stp.learning.ports": 0, "stp.listening.ports": 0, "stp.port": "24,33,8,13,17,18,19,22", "stp.protocol": "unknown", "stp.root.cost": 0, "stp.root.forward.delay.sec": 1500, "stp.root.hello.time.sec": 200, "stp.root.mac.address": "4C:E1:76:40:B4:80", "stp.root.max.age.sec": 2000, "stp.topology.change": 28}, {"stp": "30", "stp.blocking.ports": 0, "stp.bridge.forward.delay.sec": 1500, "stp.bridge.hello.time.sec": 200, "stp.bridge.mac.address": "4C:E1:76:40:B4:80", "stp.bridge.max.age.sec": 2000, "stp.bridge.priority": 4097, "stp.bridge.transmit.hold.count.sec": 100, "stp.broken.ports": 0, "stp.disabled.ports": 0, "stp.forwarding.ports": 1, "stp.last.topology.change.time": " 0 day 10 hours 4 minutes 48 seconds", "stp.learning.ports": 0, "stp.listening.ports": 0, "stp.port": "24,33,8,13,17,18,19,22", "stp.protocol": "unknown", "stp.root.cost": 0, "stp.root.forward.delay.sec": 1500, "stp.root.hello.time.sec": 200, "stp.root.mac.address": "4C:E1:76:40:B4:80", "stp.root.max.age.sec": 2000, "stp.topology.change": 28}, {"stp": "40", "stp.blocking.ports": 0, "stp.bridge.forward.delay.sec": 1500, "stp.bridge.hello.time.sec": 200, "stp.bridge.mac.address": "4C:E1:76:40:B4:80", "stp.bridge.max.age.sec": 2000, "stp.bridge.priority": 4097, "stp.bridge.transmit.hold.count.sec": 100, "stp.broken.ports": 0, "stp.disabled.ports": 0, "stp.forwarding.ports": 1, "stp.last.topology.change.time": " 0 day 10 hours 4 minutes 49 seconds", "stp.learning.ports": 0, "stp.listening.ports": 0, "stp.port": "17,18,19,22,24,33,8,13", "stp.protocol": "unknown", "stp.root.cost": 0, "stp.root.forward.delay.sec": 1500, "stp.root.hello.time.sec": 200, "stp.root.mac.address": "4C:E1:76:40:B4:80", "stp.root.max.age.sec": 2000, "stp.topology.change": 28}, {"stp": "300", "stp.blocking.ports": 0, "stp.bridge.forward.delay.sec": 1500, "stp.bridge.hello.time.sec": 200, "stp.bridge.mac.address": "4C:E1:76:40:B4:80", "stp.bridge.max.age.sec": 2000, "stp.bridge.priority": 4097, "stp.bridge.transmit.hold.count.sec": 100, "stp.broken.ports": 0, "stp.disabled.ports": 0, "stp.forwarding.ports": 1, "stp.last.topology.change.time": " 0 day 10 hours 4 minutes 49 seconds", "stp.learning.ports": 0, "stp.listening.ports": 0, "stp.port": "17,18,19,22,24,33,8,13", "stp.protocol": "unknown", "stp.root.cost": 0, "stp.root.forward.delay.sec": 1500, "stp.root.hello.time.sec": 200, "stp.root.mac.address": "4C:E1:76:40:B4:80", "stp.root.max.age.sec": 2000, "stp.topology.change": 28}, {"stp": "500", "stp.blocking.ports": 0, "stp.bridge.forward.delay.sec": 1500, "stp.bridge.hello.time.sec": 200, "stp.bridge.mac.address": "4C:E1:76:40:B4:80", "stp.bridge.max.age.sec": 2000, "stp.bridge.priority": 4097, "stp.bridge.transmit.hold.count.sec": 100, "stp.broken.ports": 0, "stp.disabled.ports": 0, "stp.forwarding.ports": 1, "stp.last.topology.change.time": " 0 day 10 hours 4 minutes 49 seconds", "stp.learning.ports": 0, "stp.listening.ports": 0, "stp.port": "24,33,8,13,17,18,19,22", "stp.protocol": "unknown", "stp.root.cost": 0, "stp.root.forward.delay.sec": 1500, "stp.root.hello.time.sec": 200, "stp.root.mac.address": "4C:E1:76:40:B4:80", "stp.root.max.age.sec": 2000, "stp.topology.change": 28}, {"stp": "1", "stp.blocking.ports": 0, "stp.bridge.forward.delay.sec": 1500, "stp.bridge.hello.time.sec": 200, "stp.bridge.mac.address": "4C:E1:76:40:B4:80", "stp.bridge.max.age.sec": 2000, "stp.bridge.priority": 4097, "stp.bridge.transmit.hold.count.sec": 100, "stp.broken.ports": 0, "stp.disabled.ports": 0, "stp.forwarding.ports": 1, "stp.last.topology.change.time": " 0 day 10 hours 4 minutes 49 seconds", "stp.learning.ports": 0, "stp.listening.ports": 0, "stp.port": "22,24,33,8,13,17,18,19", "stp.protocol": "unknown", "stp.root.cost": 0, "stp.root.forward.delay.sec": 1500, "stp.root.hello.time.sec": 200, "stp.root.mac.address": "4C:E1:76:40:B4:80", "stp.root.max.age.sec": 2000, "stp.topology.change": 28}, {"stp": "50", "stp.blocking.ports": 0, "stp.bridge.forward.delay.sec": 1500, "stp.bridge.hello.time.sec": 200, "stp.bridge.mac.address": "4C:E1:76:40:B4:80", "stp.bridge.max.age.sec": 2000, "stp.bridge.priority": 4097, "stp.bridge.transmit.hold.count.sec": 100, "stp.broken.ports": 0, "stp.disabled.ports": 0, "stp.forwarding.ports": 1, "stp.last.topology.change.time": " 0 day 10 hours 4 minutes 50 seconds", "stp.learning.ports": 0, "stp.listening.ports": 0, "stp.port": "13,17,18,19,22,24,33,8", "stp.protocol": "unknown", "stp.root.cost": 0, "stp.root.forward.delay.sec": 1500, "stp.root.hello.time.sec": 200, "stp.root.mac.address": "4C:E1:76:40:B4:80", "stp.root.max.age.sec": 2000, "stp.topology.change": 28}, {"stp": "200", "stp.blocking.ports": 0, "stp.bridge.forward.delay.sec": 1500, "stp.bridge.hello.time.sec": 200, "stp.bridge.mac.address": "4C:E1:76:40:B4:80", "stp.bridge.max.age.sec": 2000, "stp.bridge.priority": 4097, "stp.bridge.transmit.hold.count.sec": 100, "stp.broken.ports": 0, "stp.disabled.ports": 0, "stp.forwarding.ports": 1, "stp.last.topology.change.time": " 0 day 10 hours 4 minutes 50 seconds", "stp.learning.ports": 0, "stp.listening.ports": 0, "stp.port": "33,8,13,17,18,19,22,24", "stp.protocol": "unknown", "stp.root.cost": 0, "stp.root.forward.delay.sec": 1500, "stp.root.hello.time.sec": 200, "stp.root.mac.address": "4C:E1:76:40:B4:80", "stp.root.max.age.sec": 2000, "stp.topology.change": 28}, {"stp": "400", "stp.blocking.ports": 0, "stp.bridge.forward.delay.sec": 1500, "stp.bridge.hello.time.sec": 200, "stp.bridge.mac.address": "4C:E1:76:40:B4:80", "stp.bridge.max.age.sec": 2000, "stp.bridge.priority": 4097, "stp.bridge.transmit.hold.count.sec": 100, "stp.broken.ports": 0, "stp.disabled.ports": 0, "stp.forwarding.ports": 1, "stp.last.topology.change.time": " 0 day 10 hours 4 minutes 50 seconds", "stp.learning.ports": 0, "stp.listening.ports": 0, "stp.port": "13,17,18,19,22,24,33,8", "stp.protocol": "unknown", "stp.root.cost": 0, "stp.root.forward.delay.sec": 1500, "stp.root.hello.time.sec": 200, "stp.root.mac.address": "4C:E1:76:40:B4:80", "stp.root.max.age.sec": 2000, "stp.topology.change": 28}], "stp.vlans": 9}, "snmp.security.level": "No Authentication No Privacy", "snmp.security.user.name": "trapuser", "snmp.version": "v3", "timeout": 1, "status": "succeed"}}