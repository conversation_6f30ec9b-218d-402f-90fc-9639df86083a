{"************": {"result": {}, "errors": [{"error": "1129: Host '************' is blocked because of many connection errors; unblock with 'mysqladmin flush-hosts'\n\tat /home/<USER>/workspace-new/motadata/Plugin%20Engine%20(Python)/pluginengine/clients/databaseclient.py at line 231\n\tat /home/<USER>/workspace-new/motadata/Plugin%20Engine%20(Python)/pluginengine/clients/databaseclient.py at line 90\n\tat /home/<USER>/workspace-new/motadata/Plugin%20Engine%20(Python)/pluginengine/plugins/metric/mariadbinnodb/plugin.py at line 41\n\tat /home/<USER>/workspace-new/motadata/Plugin%20Engine%20(Python)/tests/unit/plugins/metric/test_mariadbinnodbplugin.py at line 39\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/python.py at line 183\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/python.py at line 1641\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 162\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 255\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 311\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 255\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 215\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 126\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/runner.py at line 109\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/main.py at line 348\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/main.py at line 323\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/main.py at line 269\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/main.py at line 316\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/callers.py at line 187\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 87\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/manager.py at line 93\n\tat /motadata/python-embedded/lib/python3.7/site-packages/pluggy/hooks.py at line 286\n\tat /motadata/python-embedded/lib/python3.7/site-packages/_pytest/config/__init__.py at line 163\n\tat /home/<USER>/pycharm/helpers/pycharm/_jb_pytest_runner.py at line 45\n", "message": "Failed to establish DB connection on ************:3306", "error.code": "MD047"}]}, "172.16.8.165": {"result": {"mariadb.innodb.buffer.pool.pages.data": 320.0, "mariadb.innodb.buffer.pool.dirty.pages": 0.0, "mariadb.innodb.buffer.pool.flush.pages.rate": 159.0, "mariadb.innodb.buffer.pool.free.pages": 15710.0, "mariadb.innodb.buffer.pool.misc.pages": 0.0, "mariadb.innodb.buffer.pool.pages": 16030.0, "mariadb.innodb.buffer.pool.random.ahead.reads": 0.0, "mariadb.innodb.buffer.pool.read.requests.rate": 262545.0, "mariadb.innodb.buffer.pool.reads.rate": 190.0, "mariadb.innodb.buffer.pool.free.waits": 0.0, "mariadb.innodb.buffer.pool.write.requests.rate": 883.0, "mariadb.innodb.data.fsyncs.rate": 9.0, "mariadb.innodb.data.pending.fsyncs": 0.0, "mariadb.innodb.data.pending.reads": 0.0, "mariadb.innodb.data.pending.writes": 0.0, "mariadb.innodb.data.reads.rate": 311.0, "mariadb.innodb.data.writes.rate": 167.0, "mariadb.innodb.double.write.written.pages.rate": 28.0, "mariadb.innodb.double.write.writes.rate": 1.0, "mariadb.innodb.log.waits.rate": 0.0, "mariadb.innodb.log.write.requests.rate": 2.0, "mariadb.innodb.log.writes.rate": 4.0, "mariadb.innodb.os.log.fsyncs.rate": 7.0, "mariadb.innodb.os.log.pending.fsyncs": 0.0, "mariadb.innodb.os.log.pending.writes": 0.0, "mariadb.innodb.os.log.written.rate": 3072.0, "mariadb.innodb.page.size.bytes": 0.02, "mariadb.innodb.created.pages.rate": 131.0, "mariadb.innodb.read.pages.rate": 189.0, "mariadb.innodb.written.pages.rate": 159.0, "mariadb.innodb.row.lock.current.waits": 0.0, "mariadb.innodb.row.lock.time.ms": 0.0, "mariadb.innodb.average.row.lock.time.ms": 0.0, "mariadb.innodb.row.lock.waits": 0.0, "mariadb.innodb.deleted.rows.rate": 0.0, "mariadb.innodb.inserted.rows.rate": 0.0, "mariadb.innodb.read.rows.rate": 0.0, "mariadb.innodb.update.rows.rate": 0.0}, "errors": []}}