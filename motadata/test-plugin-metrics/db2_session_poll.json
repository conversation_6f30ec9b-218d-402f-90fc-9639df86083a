{"172.16.8.165": {"result": {"db2.session.lock.held": [{"db2.session.agent.id": "56859", "db2.session.application": "db2pkgrb", "db2.session.table.name": "SYSPLAN", "db2.session.lock.type": "ROW_LOCK", "db2.session.lock.status": "GRNT", "db2.session.lock.mode": "NS"}, {"db2.session.agent.id": "56859", "db2.session.application": "db2pkgrb", "db2.session.table.name": "SYSPLAN", "db2.session.lock.type": "TABLE_LOCK", "db2.session.lock.status": "GRNT", "db2.session.lock.mode": "IS"}, {"db2.session.agent.id": "56896", "db2.session.application": "python", "db2.session.table.name": "", "db2.session.lock.type": "INTERNALV_LOCK", "db2.session.lock.status": "GRNT", "db2.session.lock.mode": "S"}, {"db2.session.agent.id": "56896", "db2.session.application": "python", "db2.session.table.name": "SYSUSERAUTH", "db2.session.lock.type": "TABLE_LOCK", "db2.session.lock.status": "GRNT", "db2.session.lock.mode": "IS"}], "db2.session": [{"db2.session.agent.id": "56638", "db2.session.application": "db2cmpd", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56697", "db2.session.application": "db2fw11", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56690", "db2.session.application": "db2stmm", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56637", "db2.session.application": "db2evmg_DB2DETAILDEA", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56709", "db2.session.application": "db2lused", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56643", "db2.session.application": "db2wlmd", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56859", "db2.session.application": "db2pkgrb", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 329, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 2, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56760", "db2.session.application": "db2fw0", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56740", "db2.session.application": "db2taskd", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 6, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56851", "db2.session.application": "db2dbctrld", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56831", "db2.session.application": "db2fw1", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56844", "db2.session.application": "db2fw5", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56890", "db2.session.application": "db2fw2", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56778", "db2.session.application": "db2fw10", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56903", "db2.session.application": "db2fw8", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56896", "db2.session.application": "python", "db2.session.application.status": "UOWEXEC", "db2.session.read.rows": 355, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 2, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 4, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": "10.20.41.187"}, {"db2.session.agent.id": "56863", "db2.session.application": "db2fw6", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56909", "db2.session.application": "db2fw9", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56889", "db2.session.application": "db2pcsd", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56810", "db2.session.application": "db2fw7", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56731", "db2.session.application": "db2mcd", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56882", "db2.session.application": "db2fw4", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56829", "db2.session.application": "db2fw3", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}], "db2.blocked.sessions": 0, "db2.waiting.sessions": 0, "db2.active.sessions": 1, "db2.sessions": 23, "correlation.metrics": ["db2.session", "db2.session.lock.held", "db2.session.lock.wait"]}, "errors": []}, "fd00:1:1:1::132": {"result": {"db2.session.lock.held": [{"db2.session.agent.id": "56859", "db2.session.application": "db2pkgrb", "db2.session.table.name": "SYSPLAN", "db2.session.lock.type": "ROW_LOCK", "db2.session.lock.status": "GRNT", "db2.session.lock.mode": "NS"}, {"db2.session.agent.id": "56859", "db2.session.application": "db2pkgrb", "db2.session.table.name": "SYSPLAN", "db2.session.lock.type": "TABLE_LOCK", "db2.session.lock.status": "GRNT", "db2.session.lock.mode": "IS"}, {"db2.session.agent.id": "56896", "db2.session.application": "python", "db2.session.table.name": "", "db2.session.lock.type": "INTERNALV_LOCK", "db2.session.lock.status": "GRNT", "db2.session.lock.mode": "S"}, {"db2.session.agent.id": "56896", "db2.session.application": "python", "db2.session.table.name": "SYSUSERAUTH", "db2.session.lock.type": "TABLE_LOCK", "db2.session.lock.status": "GRNT", "db2.session.lock.mode": "IS"}], "db2.session": [{"db2.session.agent.id": "56638", "db2.session.application": "db2cmpd", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56697", "db2.session.application": "db2fw11", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56690", "db2.session.application": "db2stmm", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56637", "db2.session.application": "db2evmg_DB2DETAILDEA", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56709", "db2.session.application": "db2lused", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56643", "db2.session.application": "db2wlmd", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56859", "db2.session.application": "db2pkgrb", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 329, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 2, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56760", "db2.session.application": "db2fw0", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56740", "db2.session.application": "db2taskd", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 6, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56851", "db2.session.application": "db2dbctrld", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56831", "db2.session.application": "db2fw1", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56844", "db2.session.application": "db2fw5", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56890", "db2.session.application": "db2fw2", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56778", "db2.session.application": "db2fw10", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56903", "db2.session.application": "db2fw8", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56896", "db2.session.application": "python", "db2.session.application.status": "UOWEXEC", "db2.session.read.rows": 355, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 2, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 4, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": "10.20.41.187"}, {"db2.session.agent.id": "56863", "db2.session.application": "db2fw6", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56909", "db2.session.application": "db2fw9", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56889", "db2.session.application": "db2pcsd", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56810", "db2.session.application": "db2fw7", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56731", "db2.session.application": "db2mcd", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56882", "db2.session.application": "db2fw4", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "56829", "db2.session.application": "db2fw3", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}], "db2.blocked.sessions": 0, "db2.waiting.sessions": 0, "db2.active.sessions": 1, "db2.sessions": 23, "correlation.metrics": ["db2.session", "db2.session.lock.held", "db2.session.lock.wait"]}, "errors": []}, "172.16.8.244": {"result": {"db2.session.lock.held": [{"db2.session.agent.id": "1176", "db2.session.application": "python", "db2.session.table.name": "", "db2.session.lock.type": "INTERNALV_LOCK", "db2.session.lock.status": "GRNT", "db2.session.lock.mode": "S"}, {"db2.session.agent.id": "1176", "db2.session.application": "python", "db2.session.table.name": "", "db2.session.lock.type": "INTERNALP_LOCK", "db2.session.lock.status": "GRNT", "db2.session.lock.mode": "S"}, {"db2.session.agent.id": "1176", "db2.session.application": "python", "db2.session.table.name": "SYSUSERAUTH", "db2.session.lock.type": "TABLE_LOCK", "db2.session.lock.status": "GRNT", "db2.session.lock.mode": "IS"}], "db2.session": [{"db2.session.agent.id": "1171", "db2.session.application": "db2wlmd", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "1170", "db2.session.application": "db2stmm", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "1176", "db2.session.application": "python", "db2.session.application.status": "UOWEXEC", "db2.session.read.rows": 317, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 3, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 3, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": "::ffff:10.20.41.187"}, {"db2.session.agent.id": "1169", "db2.session.application": "db2pcsd", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "1175", "db2.session.application": "db2taskd", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 6, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "1181", "db2.session.application": "db2fw5", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "1174", "db2.session.application": "db2lused", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "1180", "db2.session.application": "db2fw2", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "1173", "db2.session.application": "db2fw3", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "1179", "db2.session.application": "db2fw1", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "1172", "db2.session.application": "db2fw4", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "1198", "db2.session.application": "db2evmg_DB2DETAILDEA", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}, {"db2.session.agent.id": "1178", "db2.session.application": "db2fw0", "db2.session.application.status": "CONNECTED", "db2.session.read.rows": 0, "db2.session.written.rows": 0, "db2.session.data.reads.rate": 0, "db2.session.index.reads": 0, "db2.session.data.writes.rate": 0, "db2.session.index.writes": 0, "db2.session.physical.read.time.ms": 0, "db2.session.physical.write.time.ms": 0, "db2.session.lock.held": 0, "db2.session.lock.wait": 0, "db2.session.lock.wait.time.ms": 0, "db2.session.deadlocks": 0, "db2.session.sorts": 0, "db2.session.commit.sql.statements": 0, "db2.session.rollback.sql.statements": 0, "db2.session.deleted.rows": 0, "db2.session.inserted.rows": 0, "db2.session.updated.rows": 0, "db2.session.selected.rows": 0, "db2.session.timedout.locks": 0, "db2.session.agents": 1, "db2.session.cpu.time.sec": 0, "db2.session.statement.execution.elapsed.time.sec": 0, "db2.session.user.id": 0, "db2.session.remote.client": ""}], "db2.blocked.sessions": 0, "db2.waiting.sessions": 0, "db2.active.sessions": 1, "db2.sessions": 13, "correlation.metrics": ["db2.session", "db2.session.lock.held", "db2.session.lock.wait"]}, "errors": []}}