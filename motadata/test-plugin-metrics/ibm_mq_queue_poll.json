{"************": {"result": {"ibm.mq.queue": [{"ibm.mq.queue": "local123", "ibm.mq.queue.availability": "up", "ibm.mq.queue.type": "Local Queue", "ibm.mq.queue.cluster.name": null, "ibm.mq.queue.cluster.list": null, "ibm.mq.queue.cluster.type": null, "ibm.mq.queue.description": null, "ibm.mq.queue.pending.messages": 0, "ibm.mq.queue.max.depth": 5000, "ibm.mq.queue.max.message.length.bytes": 4194304, "ibm.mq.queue.max.incoming.messages": 0, "ibm.mq.queue.max.outgoing.messages": 0, "ibm.mq.queue.usage": "Normal", "ibm.mq.queue.remote.queue.manager.name": null, "ibm.mq.queue.xmit.queue.manager.name": null, "ibm.mq.queue.definition.type": "Predefined", "ibm.mq.queue.alteration.date": "2019-10-16", "ibm.mq.queue.alteration.time": "07.54.04", "ibm.mq.queue.last.get.date": null, "ibm.mq.queue.last.get.time": null, "ibm.mq.queue.last.put.get": null, "ibm.mq.queue.last.put.time": null, "ibm.mq.queue.oldest.message.age.sec": null, "ibm.mq.queue.uncommitted.messages": null}, {"ibm.mq.queue": "receiver.queue", "ibm.mq.queue.availability": "up", "ibm.mq.queue.type": "Local Queue", "ibm.mq.queue.cluster.name": null, "ibm.mq.queue.cluster.list": null, "ibm.mq.queue.cluster.type": null, "ibm.mq.queue.description": null, "ibm.mq.queue.pending.messages": 0, "ibm.mq.queue.max.depth": 5000, "ibm.mq.queue.max.message.length.bytes": 4194304, "ibm.mq.queue.max.incoming.messages": 0, "ibm.mq.queue.max.outgoing.messages": 0, "ibm.mq.queue.usage": "Transmission", "ibm.mq.queue.remote.queue.manager.name": null, "ibm.mq.queue.xmit.queue.manager.name": null, "ibm.mq.queue.definition.type": "Predefined", "ibm.mq.queue.alteration.date": "2019-10-16", "ibm.mq.queue.alteration.time": "08.18.27", "ibm.mq.queue.last.get.date": null, "ibm.mq.queue.last.get.time": null, "ibm.mq.queue.last.put.get": null, "ibm.mq.queue.last.put.time": null, "ibm.mq.queue.oldest.message.age.sec": null, "ibm.mq.queue.uncommitted.messages": null}, {"ibm.mq.queue": "sender.queue", "ibm.mq.queue.availability": "up", "ibm.mq.queue.type": "Local Queue", "ibm.mq.queue.cluster.name": null, "ibm.mq.queue.cluster.list": null, "ibm.mq.queue.cluster.type": null, "ibm.mq.queue.description": null, "ibm.mq.queue.pending.messages": 0, "ibm.mq.queue.max.depth": 5000, "ibm.mq.queue.max.message.length.bytes": 4194304, "ibm.mq.queue.max.incoming.messages": 0, "ibm.mq.queue.max.outgoing.messages": 0, "ibm.mq.queue.usage": "Transmission", "ibm.mq.queue.remote.queue.manager.name": null, "ibm.mq.queue.xmit.queue.manager.name": null, "ibm.mq.queue.definition.type": "Predefined", "ibm.mq.queue.alteration.date": "2019-10-16", "ibm.mq.queue.alteration.time": "08.18.02", "ibm.mq.queue.last.get.date": null, "ibm.mq.queue.last.get.time": null, "ibm.mq.queue.last.put.get": null, "ibm.mq.queue.last.put.time": null, "ibm.mq.queue.oldest.message.age.sec": null, "ibm.mq.queue.uncommitted.messages": null}], "ibm.mq.queue.handler": [{"ibm.mq.queue.handler": "AMQ.MQEXPLORER.60F2A9FE0368E425", "ibm.mq.queue.handler.appl.tag": "here MQ\\bin64\\MQExplorer.exe", "ibm.mq.queue.handler.channel.name": null, "ibm.mq.queue.handler.connection.name": null, "ibm.mq.queue.handler.open.options": 8196, "ibm.mq.queue.handler.user.identifier": "Administrator@WIN-RC9AF0CA565", "ibm.mq.queue.handler.appl.type": "User Application", "ibm.mq.queue.handler.open.input.type": "User Application", "ibm.mq.queue.handler.open.output": "No", "ibm.mq.queue.handler.state": "InActive"}, {"ibm.mq.queue.handler": "AMQ.MQEXPLORER.60F2A9FE0368E425", "ibm.mq.queue.handler.appl.tag": "here MQ\\bin64\\MQExplorer.exe", "ibm.mq.queue.handler.channel.name": null, "ibm.mq.queue.handler.connection.name": null, "ibm.mq.queue.handler.open.options": 8196, "ibm.mq.queue.handler.user.identifier": "Administrator@WIN-RC9AF0CA565", "ibm.mq.queue.handler.appl.type": "User Application", "ibm.mq.queue.handler.open.input.type": "User Application", "ibm.mq.queue.handler.open.output": "No", "ibm.mq.queue.handler.state": "InActive"}]}, "errors": []}, "***********": {"result": {"ibm.mq.queue": [{"ibm.mq.queue": "QUEUE2", "ibm.mq.queue.availability": "up", "ibm.mq.queue.type": "Local Queue", "ibm.mq.queue.cluster.name": null, "ibm.mq.queue.cluster.list": null, "ibm.mq.queue.cluster.type": null, "ibm.mq.queue.description": null, "ibm.mq.queue.pending.messages": 0, "ibm.mq.queue.max.depth": 5000, "ibm.mq.queue.max.message.length.bytes": 4194304, "ibm.mq.queue.max.incoming.messages": 0, "ibm.mq.queue.max.outgoing.messages": 0, "ibm.mq.queue.usage": "Normal", "ibm.mq.queue.remote.queue.manager.name": null, "ibm.mq.queue.xmit.queue.manager.name": null, "ibm.mq.queue.definition.type": "Predefined", "ibm.mq.queue.alteration.date": "2020-09-01", "ibm.mq.queue.alteration.time": "11.08.32", "ibm.mq.queue.last.get.date": null, "ibm.mq.queue.last.get.time": null, "ibm.mq.queue.last.put.get": null, "ibm.mq.queue.last.put.time": null, "ibm.mq.queue.oldest.message.age.sec": null, "ibm.mq.queue.uncommitted.messages": null}], "ibm.mq.queue.handler": [{"ibm.mq.queue.handler": "AMQ.MQEXPLORER.5FFAAF0B20832D03", "ibm.mq.queue.handler.appl.tag": "MQ Explorer 9.1.5", "ibm.mq.queue.handler.channel.name": null, "ibm.mq.queue.handler.connection.name": null, "ibm.mq.queue.handler.open.options": 8196, "ibm.mq.queue.handler.user.identifier": "mqm", "ibm.mq.queue.handler.appl.type": "Queue Manager Process", "ibm.mq.queue.handler.open.input.type": "User Application", "ibm.mq.queue.handler.open.output": "No", "ibm.mq.queue.handler.state": "InActive"}, {"ibm.mq.queue.handler": "AMQ.MQEXPLORER.5FFAAF0B20832D03", "ibm.mq.queue.handler.appl.tag": "MQ Explorer 9.1.5", "ibm.mq.queue.handler.channel.name": null, "ibm.mq.queue.handler.connection.name": null, "ibm.mq.queue.handler.open.options": 8196, "ibm.mq.queue.handler.user.identifier": "mqm", "ibm.mq.queue.handler.appl.type": "Queue Manager Process", "ibm.mq.queue.handler.open.input.type": "User Application", "ibm.mq.queue.handler.open.output": "No", "ibm.mq.queue.handler.state": "InActive"}]}, "errors": []}}