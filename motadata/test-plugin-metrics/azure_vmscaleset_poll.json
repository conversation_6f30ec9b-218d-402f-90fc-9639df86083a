{"127.0.0.1": {"219": {"metadata.fields": {"name": "saleset", "type": "scaleset"}}, "_type": "1", "cloud.client.id": "aaa130f3-5ed7-404b-87f5-90a5a42750ea", "cloud.secret.key": "**********************************", "cloud.tenant.id": "5b4acec3-2592-4187-9489-98c654cc6c87", "cloud.type": "Azure Cloud", "credential.profile.name": "Azure-Cloud-Test1655203790900", "credential.profile.protocol": "Cloud", "discovery.category": "Cloud", "discovery.context": {}, "discovery.credential.profiles": [***************], "discovery.discovered.objects": 24, "discovery.event.processors": [176993700471165], "discovery.failed.objects": 0, "discovery.groups": [**************], "discovery.name": "Azure-Cloud-Test-1655203790903", "discovery.object.type": "Azure Cloud", "discovery.progress": 100, "discovery.status": "Last ran at 04:20:25.670 pm 14/06/2022", "discovery.total.objects": 24, "errors": [], "event.id": 176993700500115, "event.timestamp": 1655204250, "event.topic": "remote.event.processor ", "event.type": "metric.poll", "id": ***************, "metric.category": "Cloud", "metric.credential.profile": ***************, "metric.credential.profile.protocol": "Cloud", "metric.discovery.method": "REMOTE", "metric.name": "Azure Virtual Machine Scale Sets", "metric.object": ***************, "metric.plugin": "azurevmscaleset", "metric.polling.min.time": 600, "metric.polling.time": 600, "metric.state": "ENABLE", "metric.type": "Azure VM Scale Set", "object.account.id": "5807cfb0-41a6-4da6-b920-71d934d4a2af", "object.business.hour.profile": **************, "object.category": "Cloud", "object.creation.time": "04:20:35.777 pm 14/06/2022", "object.custom.fields": {"***************": "scaleset", "***************": "saleset"}, "object.discovery.method": "REMOTE", "object.event.processors": [], "object.groups": [**************, **************, ***************, ***************], "object.id": 51, "object.name": "saleset(cloud-shell-storage-centralindia)", "object.resource.group": "cloud-shell-storage-centralindia", "object.state": "ENABLE", "object.target": "saleset(cloud-shell-storage-centralindia)", "object.type": "Azure VM Scale Set", "object.vendor": "Azure Cloud", "plugin.engine": "go", "plugin.id": 219, "remote.event.processor.uuid": "4066de21-18b0-49df-b20d-fe59231e2bd4", "result": {"219": {"metadata.fields": {"name": "saleset", "type": "scaleset"}}, "azure.vmscaleset": [{"azure.location": "centralindia", "azure.name": "saleset_1", "azure.sku.name": "Standard_B1ls", "azure.status": "Succeeded", "azure.type": "Microsoft.Compute/virtualMachineScaleSets/virtualMachines", "azure.vmscaleset": "saleset_1", "azure.vmscaleset.cpu.consumed.credits": 0, "azure.vmscaleset.cpu.percent": 0.66, "azure.vmscaleset.cpu.remaining.credits": 72, "azure.vmscaleset.disk.io.cache.read.hit.percent": 100, "azure.vmscaleset.disk.io.cache.read.miss.percent": 0, "azure.vmscaleset.disk.queue.length": 0, "azure.vmscaleset.disk.read.bytes.per.sec": 0, "azure.vmscaleset.disk.read.ops.per.sec": 0, "azure.vmscaleset.disk.write.bytes.per.sec": 19789.13, "azure.vmscaleset.disk.write.ops.per.sec": 4.04, "azure.vmscaleset.inbound.flows": 7, "azure.vmscaleset.maximum.inbound.flows.per.sec": 5, "azure.vmscaleset.maximum.outbound.flows.per.sec": 5, "azure.vmscaleset.network.in.bytes.rate": 50384, "azure.vmscaleset.network.out.bytes.rate": 74106, "azure.vmscaleset.outbound.flows": 7}], "azure.vmscalesets": 1}, "status": "succeed", "timeout": 60}}