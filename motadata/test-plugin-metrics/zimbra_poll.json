{"172.16.8.231": {"result": {"zimbra.ldap.account.cache.hit.ratio.percent": 0, "zimbra.ldap.account.cache.size": 5, "zimbra.ldap.acl.cache.hit.ratio.percent": 0, "zimbra.bis.reads": 0, "zimbra.bis.seek.ratio.percent": 0, "zimbra.calendar.cache.hits": 0, "zimbra.calendar.cache.lru.size": 0, "zimbra.calendar.cache.memory.hits": 0, "zimbra.ldap.cos.cache.hit.ratio.percent": 0, "zimbra.ldap.cos.cache.size": 1, "zimbra.db.connections": 3, "zimbra.db.connection.latency.ms": 1, "zimbra.db.used.connections": 0, "zimbra.ldap.domain.cache.hit.ratio.percent": 0, "zimbra.ldap.domain.cache.size": 1, "zimbra.file.descriptor.cache.hit.ratio.percent": 0, "zimbra.file.descriptor.cache.size": 0, "zimbra.gc.concurrentmarksweep.count": 6, "zimbra.gc.concurrentmarksweep.sec": 22.605, "zimbra.gc.major.count": 6, "zimbra.gc.major.sec": 22.605, "zimbra.gc.minor.count": 6506, "zimbra.gc.minor.sec": 90.129, "zimbra.gc.parnew.count": 6506, "zimbra.gc.parnew.sec": 90.129, "zimbra.ldap.group.cache.hit.ratio.percent": 0, "zimbra.ldap.group.cache.size": 0, "zimbra.heap.free.bytes": *********, "zimbra.heap.used.bytes": 121765808, "zimbra.index.read.bytes": 0, "zimbra.index.written.bytes": 0, "zimbra.index.average.writers": 0, "zimbra.opened.index.writers": 0, "zimbra.opened.index.writer.cache.hits": 0, "zimbra.cleartext.imap.connections": 0, "zimbra.imap.received.requests": 0, "zimbra.imap.request.latency.ms": 0, "zimbra.imap.ssl.connections": 0, "zimbra.ldap.directory.count": 7, "zimbra.ldap.directory.latency.ms": 0, "zimbra.lmtp.delivered.bytes": 0, "zimbra.lmtp.delivered.messages": 0, "zimbra.lmtp.received.bytes": 0, "zimbra.lmtp.received.messages": 0, "zimbra.lmtp.recipients": 0, "zimbra.mailbox.added.messages": 0, "zimbra.mailbox.added.message.latency.ms": 0, "zimbra.mailbox.cache.hit.ratio.percent": 100, "zimbra.mailbox.cache.size": 4, "zimbra.mailbox.gets": 4, "zimbra.mailbox.get.latency.ms": 0, "zimbra.mailbox.item.cache.hit.ratio.percent": 0, "zimbra.mailbox.message.cache.hit.ratio.percent": 0, "zimbra.memory.pool.cms.old.gen.free.bytes": 329853160, "zimbra.memory.pool.cms.old.gen.used.bytes": 72800024, "zimbra.memory.pool.code.cache.free.bytes": 460480, "zimbra.memory.pool.code.cache.used.bytes": 53147968, "zimbra.memory.pool.eden.space.free.bytes": 59979976, "zimbra.memory.pool.eden.space.used.bytes": 47499064, "zimbra.memory.pool.survivor.space.free.bytes": 1466720, "zimbra.message.cache.count": 0, "zimbra.cleartext.pop3.connections": 0, "zimbra.pop3.received.requests": 0, "zimbra.pop3.request.latency.ms": 0, "zimbra.pop3.ssl.connections": 0, "zimbra.ldap.server.cache.hit.ratio.percent": 0, "zimbra.ldap.server.cache.size": 1, "zimbra.soap.received.requests": 2, "zimbra.soap.request.latency.ms": 17, "zimbra.soap.sessions": 0, "zimbra.ldap.xmpp.cache.hit.ratio.percent": 0, "zimbra.ldap.xmapp.cache.size": 0, "zimbra.ldap.zimlet.cache.hit.ratio.percent": 0, "zimbra.ldap.zimlet.cache.size": 0, "zimbra.service": [{"zimbra.service": "antivirus", "zimbra.service.state": "Stopped", "zimbra.service.status": "Down"}, {"zimbra.service": "stats", "zimbra.service.state": "Running", "zimbra.service.status": "Up"}, {"zimbra.service": "logger", "zimbra.service.state": "Running", "zimbra.service.status": "Up"}, {"zimbra.service": "mailbox", "zimbra.service.state": "Running", "zimbra.service.status": "Up"}, {"zimbra.service": "amavis", "zimbra.service.state": "Stopped", "zimbra.service.status": "Down"}, {"zimbra.service": "antispam", "zimbra.service.state": "Stopped", "zimbra.service.status": "Down"}, {"zimbra.service": "spell", "zimbra.service.state": "Running", "zimbra.service.status": "Up"}, {"zimbra.service": "snmp", "zimbra.service.state": "Running", "zimbra.service.status": "Up"}, {"zimbra.service": "proxy", "zimbra.service.state": "Running", "zimbra.service.status": "Up"}, {"zimbra.service": "memcached", "zimbra.service.state": "Running", "zimbra.service.status": "Up"}, {"zimbra.service": "ldap", "zimbra.service.state": "Running", "zimbra.service.status": "Up"}, {"zimbra.service": "mta", "zimbra.service.state": "Running", "zimbra.service.status": "Up"}]}, "errors": []}, "172.16.10.88": {"result": {"zimbra.ldap.account.cache.hit.ratio.percent": 0, "zimbra.ldap.account.cache.size": 2, "zimbra.ldap.acl.cache.hit.ratio.percent": 0, "zimbra.bis.reads": 0, "zimbra.bis.seek.ratio.percent": 0, "zimbra.calendar.cache.hits": 0, "zimbra.calendar.cache.lru.size": 0, "zimbra.calendar.cache.memory.hits": 0, "zimbra.ldap.cos.cache.hit.ratio.percent": 0, "zimbra.ldap.cos.cache.size": 1, "zimbra.db.connections": 5, "zimbra.db.connection.latency.ms": 1, "zimbra.db.used.connections": 0, "zimbra.ldap.domain.cache.hit.ratio.percent": 0, "zimbra.ldap.domain.cache.size": 1, "zimbra.file.descriptor.cache.hit.ratio.percent": 0, "zimbra.file.descriptor.cache.size": 0, "zimbra.gc.concurrentmarksweep.count": 2, "zimbra.gc.concurrentmarksweep.sec": 0.099, "zimbra.gc.major.count": 2, "zimbra.gc.major.sec": 0.099, "zimbra.gc.minor.count": 30, "zimbra.gc.minor.sec": 1.148, "zimbra.gc.parnew.count": 30, "zimbra.gc.parnew.sec": 1.148, "zimbra.ldap.group.cache.hit.ratio.percent": 0, "zimbra.ldap.group.cache.size": 0, "zimbra.heap.free.bytes": *********, "zimbra.heap.used.bytes": 64065040, "zimbra.index.read.bytes": 0, "zimbra.index.written.bytes": 0, "zimbra.index.average.writers": 0, "zimbra.opened.index.writers": 0, "zimbra.opened.index.writer.cache.hits": 0, "zimbra.cleartext.imap.connections": 0, "zimbra.imap.received.requests": 0, "zimbra.imap.request.latency.ms": 0, "zimbra.imap.ssl.connections": 0, "zimbra.ldap.directory.count": 4, "zimbra.ldap.directory.latency.ms": 0, "zimbra.lmtp.delivered.bytes": 0, "zimbra.lmtp.delivered.messages": 0, "zimbra.lmtp.received.bytes": 0, "zimbra.lmtp.received.messages": 0, "zimbra.lmtp.recipients": 0, "zimbra.mailbox.added.messages": 0, "zimbra.mailbox.added.message.latency.ms": 0, "zimbra.mailbox.cache.hit.ratio.percent": 100, "zimbra.mailbox.cache.size": 1, "zimbra.mailbox.gets": 2, "zimbra.mailbox.get.latency.ms": 0, "zimbra.mailbox.item.cache.hit.ratio.percent": 0, "zimbra.mailbox.message.cache.hit.ratio.percent": 0, "zimbra.memory.pool.cms.old.gen.free.bytes": 368205688, "zimbra.memory.pool.cms.old.gen.used.bytes": 34447496, "zimbra.memory.pool.code.cache.free.bytes": 143104, "zimbra.memory.pool.code.cache.used.bytes": 19190016, "zimbra.memory.pool.eden.space.free.bytes": 82238376, "zimbra.memory.pool.eden.space.used.bytes": 25240664, "zimbra.memory.pool.survivor.space.free.bytes": 4376880, "zimbra.message.cache.count": 0, "zimbra.cleartext.pop3.connections": 0, "zimbra.pop3.received.requests": 0, "zimbra.pop3.request.latency.ms": 0, "zimbra.pop3.ssl.connections": 0, "zimbra.ldap.server.cache.hit.ratio.percent": 0, "zimbra.ldap.server.cache.size": 1, "zimbra.soap.received.requests": 3, "zimbra.soap.request.latency.ms": 8, "zimbra.soap.sessions": 0, "zimbra.ldap.xmpp.cache.hit.ratio.percent": 0, "zimbra.ldap.xmapp.cache.size": 0, "zimbra.ldap.zimlet.cache.hit.ratio.percent": 0, "zimbra.ldap.zimlet.cache.size": 0, "zimbra.service": [{"zimbra.service": "logger", "zimbra.service.state": "Running", "zimbra.service.status": "Up"}, {"zimbra.service": "antispam", "zimbra.service.state": "Running", "zimbra.service.status": "Up"}, {"zimbra.service": "proxy", "zimbra.service.state": "Running", "zimbra.service.status": "Up"}, {"zimbra.service": "snmp", "zimbra.service.state": "Running", "zimbra.service.status": "Up"}, {"zimbra.service": "amavis", "zimbra.service.state": "Running", "zimbra.service.status": "Up"}, {"zimbra.service": "mailbox", "zimbra.service.state": "Running", "zimbra.service.status": "Up"}, {"zimbra.service": "spell", "zimbra.service.state": "Running", "zimbra.service.status": "Up"}, {"zimbra.service": "mta", "zimbra.service.state": "Running", "zimbra.service.status": "Up"}, {"zimbra.service": "stats", "zimbra.service.state": "Stopped", "zimbra.service.status": "Down"}, {"zimbra.service": "ldap", "zimbra.service.state": "Running", "zimbra.service.status": "Up"}, {"zimbra.service": "memcached", "zimbra.service.state": "Running", "zimbra.service.status": "Up"}, {"zimbra.service": "antivirus", "zimbra.service.state": "Stopped", "zimbra.service.status": "Down"}]}, "errors": []}}