{"172.16.8.238": {"result": {"websphere.version": "8.5.5.16", "websphere.jdbc.pool.used.percent": 0.0, "websphere.jdbc.pool": [{"websphere.jdbc.pool": "Derby JDBC Provider", "websphere.jdbc.pool.created.connections": 1, "websphere.jdbc.pool.destroyed.connections": 0, "websphere.jdbc.pool.allocated.connections": 1, "websphere.jdbc.pool.returned.connections": 1, "websphere.jdbc.pool.fault.connections": 0, "websphere.jdbc.pool.managed.connections": 1, "websphere.jdbc.pool.handle.connections": 0, "websphere.jdbc.pool.discarded.statements": 0, "websphere.jdbc.pool.size": 1, "websphere.jdbc.free.pool.size": 1, "websphere.jdbc.pool.waiting.threads": 0, "websphere.jdbc.pool.percent.used": 0.0}, {"websphere.jdbc.pool": "Derby JDBC Provider (XA)", "websphere.jdbc.pool.created.connections": 0, "websphere.jdbc.pool.destroyed.connections": 0, "websphere.jdbc.pool.allocated.connections": 0, "websphere.jdbc.pool.returned.connections": 0, "websphere.jdbc.pool.fault.connections": 0, "websphere.jdbc.pool.managed.connections": 0, "websphere.jdbc.pool.handle.connections": 0, "websphere.jdbc.pool.discarded.statements": 0, "websphere.jdbc.pool.size": 0, "websphere.jdbc.free.pool.size": 0, "websphere.jdbc.pool.waiting.threads": 0, "websphere.jdbc.pool.percent.used": 0.0}], "websphere.requests": 102, "websphere.hits": 94, "websphere.hit.ratio.percent": 92.16, "websphere.thread.pool.used.percent": 6.0, "websphere.thread.pool": [{"websphere.thread.pool": "AriesThreadPool", "websphere.thread.pool.created.threads": 0, "websphere.thread.pool.destroyed.threads": 0, "websphere.thread.pool.declared.hung.threads": 0, "websphere.thread.pool.cleared.hung.threads": 0, "websphere.thread.pool.concurrent.hung.threads": 0, "websphere.thread.pool.active.threads": 0, "websphere.thread.pool.size": 0, "websphere.thread.pool.maxed.percent": 0, "websphere.thread.pool.used.percent": 0.0}, {"websphere.thread.pool": "<PERSON><PERSON><PERSON>", "websphere.thread.pool.created.threads": 0, "websphere.thread.pool.destroyed.threads": 0, "websphere.thread.pool.declared.hung.threads": 0, "websphere.thread.pool.cleared.hung.threads": 0, "websphere.thread.pool.concurrent.hung.threads": 0, "websphere.thread.pool.active.threads": 0, "websphere.thread.pool.size": 0, "websphere.thread.pool.maxed.percent": 0, "websphere.thread.pool.used.percent": 0.0}, {"websphere.thread.pool": "HAManager.thread.pool", "websphere.thread.pool.created.threads": 2, "websphere.thread.pool.destroyed.threads": 0, "websphere.thread.pool.declared.hung.threads": 0, "websphere.thread.pool.cleared.hung.threads": 0, "websphere.thread.pool.concurrent.hung.threads": 0, "websphere.thread.pool.active.threads": 0, "websphere.thread.pool.size": 2, "websphere.thread.pool.maxed.percent": 0, "websphere.thread.pool.used.percent": 0.0}, {"websphere.thread.pool": "Message Listener", "websphere.thread.pool.created.threads": 0, "websphere.thread.pool.destroyed.threads": 0, "websphere.thread.pool.declared.hung.threads": 0, "websphere.thread.pool.cleared.hung.threads": 0, "websphere.thread.pool.concurrent.hung.threads": 0, "websphere.thread.pool.active.threads": 0, "websphere.thread.pool.size": 0, "websphere.thread.pool.maxed.percent": 0, "websphere.thread.pool.used.percent": 0.0}, {"websphere.thread.pool": "Object Request Broker", "websphere.thread.pool.created.threads": 0, "websphere.thread.pool.destroyed.threads": 0, "websphere.thread.pool.declared.hung.threads": 0, "websphere.thread.pool.cleared.hung.threads": 0, "websphere.thread.pool.concurrent.hung.threads": 0, "websphere.thread.pool.active.threads": 0, "websphere.thread.pool.size": 0, "websphere.thread.pool.maxed.percent": 0, "websphere.thread.pool.used.percent": 0.0}, {"websphere.thread.pool": "SIBFAPInboundThreadPool", "websphere.thread.pool.created.threads": 0, "websphere.thread.pool.destroyed.threads": 0, "websphere.thread.pool.declared.hung.threads": 0, "websphere.thread.pool.cleared.hung.threads": 0, "websphere.thread.pool.concurrent.hung.threads": 0, "websphere.thread.pool.active.threads": 0, "websphere.thread.pool.size": 0, "websphere.thread.pool.maxed.percent": 0, "websphere.thread.pool.used.percent": 0.0}, {"websphere.thread.pool": "SIBFAPThreadPool", "websphere.thread.pool.created.threads": 0, "websphere.thread.pool.destroyed.threads": 0, "websphere.thread.pool.declared.hung.threads": 0, "websphere.thread.pool.cleared.hung.threads": 0, "websphere.thread.pool.concurrent.hung.threads": 0, "websphere.thread.pool.active.threads": 0, "websphere.thread.pool.size": 0, "websphere.thread.pool.maxed.percent": 0, "websphere.thread.pool.used.percent": 0.0}, {"websphere.thread.pool": "SoapConnectorThreadPool", "websphere.thread.pool.created.threads": 0, "websphere.thread.pool.destroyed.threads": 0, "websphere.thread.pool.declared.hung.threads": 0, "websphere.thread.pool.cleared.hung.threads": 0, "websphere.thread.pool.concurrent.hung.threads": 0, "websphere.thread.pool.active.threads": 0, "websphere.thread.pool.size": 0, "websphere.thread.pool.maxed.percent": 0, "websphere.thread.pool.used.percent": 0.0}, {"websphere.thread.pool": "TCPChannel.DCS", "websphere.thread.pool.created.threads": 0, "websphere.thread.pool.destroyed.threads": 0, "websphere.thread.pool.declared.hung.threads": 0, "websphere.thread.pool.cleared.hung.threads": 0, "websphere.thread.pool.concurrent.hung.threads": 0, "websphere.thread.pool.active.threads": 0, "websphere.thread.pool.size": 0, "websphere.thread.pool.maxed.percent": 0, "websphere.thread.pool.used.percent": 0.0}, {"websphere.thread.pool": "WMQJCAResourceAdapter", "websphere.thread.pool.created.threads": 0, "websphere.thread.pool.destroyed.threads": 0, "websphere.thread.pool.declared.hung.threads": 0, "websphere.thread.pool.cleared.hung.threads": 0, "websphere.thread.pool.concurrent.hung.threads": 0, "websphere.thread.pool.active.threads": 0, "websphere.thread.pool.size": 0, "websphere.thread.pool.maxed.percent": 0, "websphere.thread.pool.used.percent": 0.0}, {"websphere.thread.pool": "WebContainer", "websphere.thread.pool.created.threads": 9, "websphere.thread.pool.destroyed.threads": 0, "websphere.thread.pool.declared.hung.threads": 0, "websphere.thread.pool.cleared.hung.threads": 0, "websphere.thread.pool.concurrent.hung.threads": 0, "websphere.thread.pool.active.threads": 3, "websphere.thread.pool.size": 9, "websphere.thread.pool.maxed.percent": 0, "websphere.thread.pool.used.percent": 6.0}], "websphere.heap.memory.free.bytes": 48075776, "websphere.heap.memory.used.bytes": 119367680, "startedtime.sec": 44381122, "websphere.process.cpu.percent": 0, "websphere.heap.memory.size.bytes": 167444480, "websphere.servlet.created.sessions": 1038, "websphere.servlet.invalidated.sessions": 1038, "websphere.servlet.discarded.session": 0, "websphere.servlet.cache.discarded.sessions": 1038, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 1032, "websphere.servlet.non.exist.activated.sessions": 17, "websphere.servlet.session.lifetime.ms": 1938080726, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.session.external.last.activated.time.ms": 759903, "websphere.servlet.active.sessions": 6, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes": 0, "websphere.servlet.session.external.write.bytes": 0, "websphere.servlet.session.session.object.bytes": 0, "websphere.orb.lookup.time.ms": 0, "websphere.orb.requests": 0, "websphere.orb.concurrent.requests": 0}}, "172.16.9.145": {"result": {"websphere.version": "9.0.5.1", "websphere.jdbc.pool.used.percent": 0.0, "websphere.jdbc.pool": [{"websphere.jdbc.pool": "Derby JDBC Provider", "websphere.jdbc.pool.created.connections": 0, "websphere.jdbc.pool.destroyed.connections": 0, "websphere.jdbc.pool.allocated.connections": 0, "websphere.jdbc.pool.returned.connections": 0, "websphere.jdbc.pool.fault.connections": 0, "websphere.jdbc.pool.managed.connections": 0, "websphere.jdbc.pool.handle.connections": 0, "websphere.jdbc.pool.discarded.statements": 0, "websphere.jdbc.pool.size": 0, "websphere.jdbc.free.pool.size": 0, "websphere.jdbc.pool.waiting.threads": 0, "websphere.jdbc.pool.percent.used": 0.0}, {"websphere.jdbc.pool": "Derby JDBC Provider (XA)", "websphere.jdbc.pool.created.connections": 0, "websphere.jdbc.pool.destroyed.connections": 0, "websphere.jdbc.pool.allocated.connections": 0, "websphere.jdbc.pool.returned.connections": 0, "websphere.jdbc.pool.fault.connections": 0, "websphere.jdbc.pool.managed.connections": 0, "websphere.jdbc.pool.handle.connections": 0, "websphere.jdbc.pool.discarded.statements": 0, "websphere.jdbc.pool.size": 0, "websphere.jdbc.free.pool.size": 0, "websphere.jdbc.pool.waiting.threads": 0, "websphere.jdbc.pool.percent.used": 0.0}], "websphere.requests": 102, "websphere.hits": 94, "websphere.hit.ratio.percent": 92.16, "websphere.thread.pool.used.percent": 4.0, "websphere.thread.pool": [{"websphere.thread.pool": "AriesThreadPool", "websphere.thread.pool.created.threads": 0, "websphere.thread.pool.destroyed.threads": 0, "websphere.thread.pool.declared.hung.threads": 0, "websphere.thread.pool.cleared.hung.threads": 0, "websphere.thread.pool.concurrent.hung.threads": 0, "websphere.thread.pool.active.threads": 0, "websphere.thread.pool.size": 0, "websphere.thread.pool.maxed.percent": 0, "websphere.thread.pool.used.percent": 0.0}, {"websphere.thread.pool": "<PERSON><PERSON><PERSON>", "websphere.thread.pool.created.threads": 0, "websphere.thread.pool.destroyed.threads": 0, "websphere.thread.pool.declared.hung.threads": 0, "websphere.thread.pool.cleared.hung.threads": 0, "websphere.thread.pool.concurrent.hung.threads": 0, "websphere.thread.pool.active.threads": 0, "websphere.thread.pool.size": 0, "websphere.thread.pool.maxed.percent": 0, "websphere.thread.pool.used.percent": 0.0}, {"websphere.thread.pool": "HAManager.thread.pool", "websphere.thread.pool.created.threads": 2, "websphere.thread.pool.destroyed.threads": 0, "websphere.thread.pool.declared.hung.threads": 0, "websphere.thread.pool.cleared.hung.threads": 0, "websphere.thread.pool.concurrent.hung.threads": 0, "websphere.thread.pool.active.threads": 0, "websphere.thread.pool.size": 2, "websphere.thread.pool.maxed.percent": 0, "websphere.thread.pool.used.percent": 0.0}, {"websphere.thread.pool": "Message Listener", "websphere.thread.pool.created.threads": 0, "websphere.thread.pool.destroyed.threads": 0, "websphere.thread.pool.declared.hung.threads": 0, "websphere.thread.pool.cleared.hung.threads": 0, "websphere.thread.pool.concurrent.hung.threads": 0, "websphere.thread.pool.active.threads": 0, "websphere.thread.pool.size": 0, "websphere.thread.pool.maxed.percent": 0, "websphere.thread.pool.used.percent": 0.0}, {"websphere.thread.pool": "Object Request Broker", "websphere.thread.pool.created.threads": 0, "websphere.thread.pool.destroyed.threads": 0, "websphere.thread.pool.declared.hung.threads": 0, "websphere.thread.pool.cleared.hung.threads": 0, "websphere.thread.pool.concurrent.hung.threads": 0, "websphere.thread.pool.active.threads": 0, "websphere.thread.pool.size": 0, "websphere.thread.pool.maxed.percent": 0, "websphere.thread.pool.used.percent": 0.0}, {"websphere.thread.pool": "SIBFAPInboundThreadPool", "websphere.thread.pool.created.threads": 0, "websphere.thread.pool.destroyed.threads": 0, "websphere.thread.pool.declared.hung.threads": 0, "websphere.thread.pool.cleared.hung.threads": 0, "websphere.thread.pool.concurrent.hung.threads": 0, "websphere.thread.pool.active.threads": 0, "websphere.thread.pool.size": 0, "websphere.thread.pool.maxed.percent": 0, "websphere.thread.pool.used.percent": 0.0}, {"websphere.thread.pool": "SIBFAPThreadPool", "websphere.thread.pool.created.threads": 0, "websphere.thread.pool.destroyed.threads": 0, "websphere.thread.pool.declared.hung.threads": 0, "websphere.thread.pool.cleared.hung.threads": 0, "websphere.thread.pool.concurrent.hung.threads": 0, "websphere.thread.pool.active.threads": 0, "websphere.thread.pool.size": 0, "websphere.thread.pool.maxed.percent": 0, "websphere.thread.pool.used.percent": 0.0}, {"websphere.thread.pool": "SoapConnectorThreadPool", "websphere.thread.pool.created.threads": 0, "websphere.thread.pool.destroyed.threads": 0, "websphere.thread.pool.declared.hung.threads": 0, "websphere.thread.pool.cleared.hung.threads": 0, "websphere.thread.pool.concurrent.hung.threads": 0, "websphere.thread.pool.active.threads": 0, "websphere.thread.pool.size": 0, "websphere.thread.pool.maxed.percent": 0, "websphere.thread.pool.used.percent": 0.0}, {"websphere.thread.pool": "TCPChannel.DCS", "websphere.thread.pool.created.threads": 0, "websphere.thread.pool.destroyed.threads": 0, "websphere.thread.pool.declared.hung.threads": 0, "websphere.thread.pool.cleared.hung.threads": 0, "websphere.thread.pool.concurrent.hung.threads": 0, "websphere.thread.pool.active.threads": 0, "websphere.thread.pool.size": 0, "websphere.thread.pool.maxed.percent": 0, "websphere.thread.pool.used.percent": 0.0}, {"websphere.thread.pool": "WMQJCAResourceAdapter", "websphere.thread.pool.created.threads": 0, "websphere.thread.pool.destroyed.threads": 0, "websphere.thread.pool.declared.hung.threads": 0, "websphere.thread.pool.cleared.hung.threads": 0, "websphere.thread.pool.concurrent.hung.threads": 0, "websphere.thread.pool.active.threads": 0, "websphere.thread.pool.size": 0, "websphere.thread.pool.maxed.percent": 0, "websphere.thread.pool.used.percent": 0.0}, {"websphere.thread.pool": "WebContainer", "websphere.thread.pool.created.threads": 7, "websphere.thread.pool.destroyed.threads": 0, "websphere.thread.pool.declared.hung.threads": 0, "websphere.thread.pool.cleared.hung.threads": 0, "websphere.thread.pool.concurrent.hung.threads": 0, "websphere.thread.pool.active.threads": 2, "websphere.thread.pool.size": 7, "websphere.thread.pool.maxed.percent": 0, "websphere.thread.pool.used.percent": 4.0}], "websphere.heap.memory.free.bytes": 16326656, "websphere.heap.memory.used.bytes": 194239488, "startedtime.sec": 33441491, "websphere.process.cpu.percent": 0, "websphere.heap.memory.size.bytes": 210567168, "websphere.servlet.created.sessions": 718, "websphere.servlet.invalidated.sessions": 718, "websphere.servlet.discarded.session": 0, "websphere.servlet.cache.discarded.sessions": 718, "websphere.servlet.affinity.broken.sessions": 0, "websphere.servlet.invalid.timedout.sessions": 718, "websphere.servlet.non.exist.activated.sessions": 0, "websphere.servlet.session.lifetime.ms": 1338891184, "websphere.servlet.session.external.read.time.ms": 0, "websphere.servlet.session.external.write.time.ms": 0, "websphere.servlet.session.external.last.activated.time.ms": 16, "websphere.servlet.active.sessions": 0, "websphere.servlet.live.sessions": 0, "websphere.servlet.session.external.read.bytes": 0, "websphere.servlet.session.external.write.bytes": 0, "websphere.servlet.session.session.object.bytes": 0, "websphere.orb.lookup.time.ms": 0, "websphere.orb.requests": 0, "websphere.orb.concurrent.requests": 0}}}